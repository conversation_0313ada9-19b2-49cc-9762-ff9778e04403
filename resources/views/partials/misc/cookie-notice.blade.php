<div id="cookieNoticeId" class="cookie-notice" data-show-notice="{{ (string) $cookieNotice->visible() }}"
    @if (!$cookieNotice->visible()) tabindex="-1" @endif>
    <div class="container">
        <div class="col-xs-12 col-sm-3 col-md-2">
            <span class="cookie-notice-icon">
                <i class="af-icons-lg af-icons-lg-cookie"></i>
            </span>
        </div>
        <div class="col-xs-12 col-sm-7 col-md-8">
            <div class="island">
                <h2 class="h1">{{ $cookieNotice->title() }}</h2>
                <p>
                    {!! $cookieNotice->content() !!}
                </p>
                <div class="cookie-buttons-list">
                    <button data-cookies-action="necessary" type="button" class="btn btn-tertiary">
                        @lang('agreements.cookies.accept_necessary')
                    </button>
                    <button data-cookies-action="allow-all" type="button" class="btn btn-tertiary order-3">
                        @lang('agreements.cookies.accept_all')
                    </button>
                    <button aria-expanded="false" aria-controls="cookieSelectionId" data-cookies-action="choose" type="button" class="btn btn-tertiary">
                        @lang('agreements.cookies.choose_cookies')
                    </button>
                    <div id="cookieSelectionId" class="cookie-selection">
                        <div class="cookie-types">
                            <div class="checkbox styled cookie-checkbox"
                                 data-help-text="@lang('agreements.cookies.popover.necessary')" tabindex="0">
                                {!! html()->checkbox('necessary', true)->attributes(['id' => 'necessary', 'disabled' => true]) !!}
                                <label for="necessary">
                                    @lang('agreements.cookies.cookie_types.necessary')
                                </label>
                            </div>
                            <div class="checkbox styled cookie-checkbox"
                                 data-help-text="@lang('agreements.cookies.popover.analytics')">
                                {!! html()->checkbox('analytics', in_array('analytics', $cookieNotice->settings()))->id('analytics') !!}
                                <label for="analytics">
                                    @lang('agreements.cookies.cookie_types.analytics')
                                </label>
                            </div>
                            <div class="checkbox styled cookie-checkbox"
                                 data-help-text="@lang('agreements.cookies.popover.social_sharing')">
                                {!! html()->checkbox('social-sharing', in_array('social-sharing', $cookieNotice->settings()))->id('social-sharing') !!}
                                <label for="social-sharing">
                                    @lang('agreements.cookies.cookie_types.social_sharing')
                                </label>
                            </div>
                            <div class="checkbox styled cookie-checkbox"
                                 data-help-text="@lang('agreements.cookies.popover.marketing')">
                                {!! html()->checkbox('marketing', in_array('marketing', $cookieNotice->settings()))->id('marketing') !!}
                                <label for="marketing">
                                    @lang('agreements.cookies.cookie_types.marketing')
                                </label>
                            </div>
                            <button data-cookies-action="save" type="button" class="btn btn-secondary">
                                @lang('buttons.save')
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
