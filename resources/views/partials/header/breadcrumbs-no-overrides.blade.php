<nav aria-label="Breadcrumb" @class(['breadcrumbs-list', 'breadcrumbs-compact' => isset($compact)])>
@if(!isset($compact))<h1>@endif
<ul>
    @foreach ($crumbs as $crumb)
        @if( next( $crumbs ) )
            <li>
                @if(count($crumb)>1)
                    <a href="{{ $crumb[1] }}">{!! $crumb[0] !!}</a>
                @else
                    @if(is_array($crumb[0])) {!! $crumb[0][0] !!}
                    @else {!! $crumb[0] !!} @endif
                @endif
                <i class="breadcrumb-icon af-icons af-icons-arrow-tail-right"></i>
            </li>
        @else
            <li aria-current="page">
                @if(count($crumb)>1)
                    <a href="{{ $crumb[1] }}">{!! $crumb[0] !!}</a>
                @else
                    @if(is_array($crumb[0])) {!! $crumb[0][0] !!}
                    @else {!! $crumb[0] !!} @endif
                @endif
            </li>
        @endif
    @endforeach
</ul>
@if(!isset($compact))</h1>@endif
</nav>
