@if (!Request::filled('trashed') || Request::get('trashed') == 'none')
    <deletism
        :ids="@if(isset($selected)) @js([$selected]) @else {{'selected'}} @endif"
        :validate-ids="{{ isset($validIds) ? 'true' : 'false' }}"
        :valid-ids="@js($validIds ?? [])"
        :labels="@js(array_merge([
            'button' => trans('buttons.delete'),
            'ok' => trans('buttons.ok'),
            'cancel' => trans('buttons.cancel'),
        ], $labels ?? []))"
        button-class="{{ $class ?? null }}"
        route="{{ route($resource.'.delete', $params ?? []) }}"
        method="DELETE">
    </deletism>
@else
    <undeletism
        :ids="@if(isset($selected)) @js([$selected]) @else {{'selected'}} @endif"
        :labels="@js(['button' => trans('buttons.undelete')])"
        button-class="{{ $class ?? null }}"
        route="{{ route($resource.'.undelete', $params ?? []) }}"
        method="PUT">
    </undeletism>
@endif
