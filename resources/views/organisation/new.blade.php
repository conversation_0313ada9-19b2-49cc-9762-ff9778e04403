@section('title')
    {!! HTML::pageTitle([trans('organisations.titles.main'), trans('organisations.titles.new')]) !!}
@stop

@section('main')
    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs-no-overrides', ['crumbs' => [
                    [trans('organisations.titles.main'), route('organisation.index')],
                    [trans('organisations.titles.new')],
                ]])
            </div>
        </div>
    </div>

    @include('partials.errors.summary')
    @include('partials.errors.message')

    <organisation-new inline-template>
        {!! html()->modelForm($organisation, 'post', action:route('organisation.create'))->attributes(['class' => 'vertical'])->open() !!}

        <div class="row">
            <div class="col-xs-12 col-md-6">
                <div class="panel panel-default search-panel">
                    <div class="panel-body">
                        <div class="panel-title">
                            <h4>{{ trans('organisations.titles.organisation') }}</h4>
                        </div>
                        <div class="form-group has-feedback has-feedback-left {{ FormError::classIfError('name') }} ">
                            {!! html()->label(trans('organisations.table.columns.name'), 'name') !!}
                            {!! html()->text('name', '')->attributes(['id' => 'name', 'class' => 'form-control field']) !!}
                            <i class="form-control-feedback af-icons af-icons-organisation"></i>
                            {!! FormError::message('name') !!}
                        </div>

                        <div class="form-group">
                            {!! html()->label(trans_merge('organisations.table.columns.administrator', 'miscellaneous.optional'), 'administrator') !!}
                            <search-field
                                    id="administrator"
                                    name="administrator"
                                    hidden-input-name="administrator"
                                    src="{{ route('users.typeahead', ['name' => '']) }}"
                                    initial-id=""
                                    initial-value=""
                                    id-property="slug"
                                    value-property="name"
                                    icon="user"
                            ></search-field>
                        </div>
                    </div>
                </div>
                <div class="panel panel-default search-panel">
                    <div class="panel-body">
                        <div class="panel-title">
                            <h4>@lang('organisations.form.domains.authorised')</h4>
                        </div>
                        <p>@lang('organisations.form.domains.message')</p>
                        <domain-list :base-domains="{{ json_encode([]) }}"
                                     :errors="{{ $errors->count() ? json_encode($errors->getMessages()) : '{}' }}"
                        ></domain-list>
                    </div>
                </div>
            </div>

            <!-- Field values to be implemented later -->
            {{--    <div class="col-xs-12 col-md-6">--}}
            {{--        <div class="panel panel-default search-panel">--}}
            {{--            <div class="panel-body">--}}
            {{--                <organisation-fields--}}
            {{--                    :fields="{{ json_encode($fields) }}"--}}
            {{--                    :errors="{{ $errors->count() ? json_encode($errors->getMessages()) : '{}' }}"--}}
            {{--                ></organisation-fields>--}}
            {{--            </div>--}}
            {{--        </div>--}}
            {{--    </div>--}}
        </div>

        <div class="row">
            <div class="col-xs-12">
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-lg">
                        {!! trans('buttons.save') !!}
                    </button>

                    <a href="{{ route('organisation.index') }}" class="btn btn-tertiary btn-lg">{{ trans('buttons.cancel') }}</a>
                </div>
            </div>
        </div>

        {!! html()->closeModelForm() !!}
    </organisation-new>
@stop
