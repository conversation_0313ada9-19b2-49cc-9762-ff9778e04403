@section('title')
    {!! HTML::pageTitle([trans('organisations.titles.main'), $organisation->name]) !!}
@stop

@section('main')
    <organisation-show inline-template>
        <div>
            <div class="row island content-header">
                <div class="col-xs-12">
                    <div class="title" style="display: flex; flex-direction:row;">
                        <organisation-logo :organisation="@js($organisation->toVue())" :upload-options="@js($uploadOptions)"></organisation-logo>
                        @include('partials.header.breadcrumbs-no-overrides', ['crumbs' => [
                            [trans('organisations.titles.main'), route('organisation.index')],
                            [$organisation->name],
                        ]])
                    </div>
                </div>
            </div>
        
            <div class="content-header">
                @include('partials.errors.display')
                @include('partials.errors.message')
            </div>
    
            @include('html.tabular-vue')
        </div>
    </organisation-show>
@stop
