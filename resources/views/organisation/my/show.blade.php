@section('title')
    {!! HTML::pageTitle([trans('organisations.titles.main'), $organisation->name]) !!}
@stop

@section('main')
    <my-organisation-show inline-template>
        <div>
            <div class="row island content-header">
                <div class="col-xs-12">
                    <div class="title" style="display: flex; flex-direction:row;">
                        <organisation-logo 
                                :organisation="@js($organisation->toVue())"
                               @if($uploadOptions) :upload-options="@js($uploadOptions )" @endif>
                        </organisation-logo>
                        
                        <div>
                            @include('partials.header.breadcrumbs-no-overrides', ['crumbs' => [
                                [trans('organisations.titles.my_organisations'), route('my-organisation.index')],
                                [$organisation->name],
                            ]])
                            @if($organisation->mine())
                                <div>@lang('organisations.my.administrator.description')</div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            @include('partials.errors.display')

            @if ($organisation->mine())
                <my-organisation-administrator-view :organisation="@js($organisation->toVue())"
                                            :member-count="{{ $memberCount }}">
                </my-organisation-administrator-view>
            @else
        <my-organisation-member-view :organisation="@js($organisation->toVue())" :member-count="{{ $memberCount }}">
        </my-organisation-member-view>
            @endif
        </div>
    </my-organisation-show>
@stop
