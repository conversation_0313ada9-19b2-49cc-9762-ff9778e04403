@foreach ($referees->tabbed() as $tabName => $refereeResourceFields)
    <h3>{{ substr($tabName, 0, -8) }}</h3>
    @foreach($refereeResourceFields as $refereeResourceField)

        <div class="field-value-wrapper">
            @if (! ($hideRefereeName ?? false))
                <b>{{trans('entries.form.referee.name')}}</b>
                <p class="field-value">
                    {{ $refereeResourceField->resource->name }}
                </p>
            @endif

            @if (! ($hideRefereeEmail ?? false))
                <b>{{trans('entries.form.referee.email')}}</b>
                <p class="field-value">
                    {{ $refereeResourceField->resource->email }}
                </p>
            @endif

            @if($refereeResourceField->fields->isNotEmpty())
                @foreach (HTML::resourceFields($refereeResourceField->fields) as $field)
                    <b>{{ $field['label'] }}</b>
                    <p class="field-value">
                        {!! nl2br($field['value']) !!}
                    </p>
                @endforeach
            @endif
        </div>
    @endforeach
@endforeach
