@extends('layouts.pdf')

@section('content')
    <div class="row">
        <div class="col-xs-9">
            <p class="entry-category">
                <b>{{ lang($account, 'name') }} ({{ lang($season, 'name') }})</b><br>
                {{ local_id($entry) }} {{ lang($entry->category, 'name') }}
                @if ($chapter)
                    (@lang('pdf.labels.chapter'): {{ lang($chapter, 'name') }})
                @endif
            </p>
            @if ($displayEntryName)
                <h1 class="entry-title">{{ $entry->title }}</h1>
            @endif
        </div>
        <div class="col-xs-3">
            @include('entry.pdf.qr-code', compact('entry', 'mode'))
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            <hr class="hr"/>
        </div>
    </div>

    <div class="row">
        <div class="col-xs-12">
            @if ($displayEntrantName || count($userFields))
                <h3>@lang('judging.view.entrant.details')</h3>

                @if ($displayEntrantName)
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="field-title">@lang('judging.view.entrant.name')</div>
                            <div class="field-value">{{ $entry->user->name }}</div>
                        </div>
                    </div>
                @endif

                @foreach ($userFields as $field)
                    @include('entry.pdf.field', ['field' => $field])
                @endforeach

                <hr class="hr"/>
            @endif
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <h3>@lang('judging.view.entry.details')</h3>

            @foreach ($tabbedFields as $fields)
                @if ($tab = $fields->get('tab'))
                    @if ($tab->tabDividerOnPdfs)
                        <hr class="hr" />
                        <h3>{{ lang($tab, 'name') }}</h3>
                    @endif
                @endif
                @foreach ($fields->get('blocks') as $index => $block)
                    @if ($scoringBoxes && $index > 0)
                        <div class="row">
                            <div class="col-xs-12">
                                <hr class="hr hr-scoring-box" />
                            </div>
                        </div>
                    @endif

                    <div class="row">
                        <div class="{{ $scoringBoxes ? 'col-xs-9' : 'col-xs-12' }}">
                            @foreach($block['fields'] as $field)
                                @include('entry.pdf.field', ['field' => $field, 'showReadOffIcon' => false])
                            @endforeach
                        </div>

                        @if ($scoringBoxes)
                            <div class="col-xs-3">
                                @if (count($block['criteria']))
                                    @foreach ($block['criteria'] as $criterion)
                                        <div class="scoring-box">
                                            @include('judging.pdf.criterion', compact('criterion'))
                                        </div>
                                    @endforeach
                                @endif
                             </div>
                         @endif
                     </div>
                @endforeach
            @endforeach
        </div>
    </div>

    @if (!$contributors->isEmpty())
        <div class="row">
            <div class="col-xs-12">
                @include('entry.pdf.contributors', ['contributors' => $contributors])
            </div>
        </div>
    @endif

    @if ($referees->isNotEmpty())
        <div class="row">
            <div class="col-xs-12">
                @include('entry.pdf.referees', ['referees' => $referees, 'hideRefereeName' => $hideRefereeName, 'hideRefereeEmail' => $hideRefereeEmail])
            </div>
        </div>
    @endif

    @if ($scoringBoxes && !$additionalCriteria->isEmpty())
        @foreach ($additionalCriteria as $criterion)
            <div class="row">
                <div class="col-xs-12">
                    <hr class="hr hr-scoring-box" />
                </div>
            </div>
            <div class="row">
                <div class="col-xs-9">
                    <div class="scoring-box">
                        @include('judging.pdf.criterion', compact('criterion'))
                    </div>
                </div>
            </div>
        @endforeach
    @endif

    <div class="row">
        <div class="col-xs-12">
            <hr class="hr" />
            <p>{!! trans('pdf.text.complete_attachments', ['link' => link_to(route('judging.score', [$entry->slug]), $domain->domain)]) !!}</p>
        </div>
    </div>

    @include('entry.pdf.attachments', ['attachments' => $attachments, 'links' => $links, 'attachmentDownload' => $attachmentDownload])

@stop
