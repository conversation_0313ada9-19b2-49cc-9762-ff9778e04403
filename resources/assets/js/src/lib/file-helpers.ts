const getImageFromFile = (imageFile: File): Promise<HTMLImageElement> =>
	new Promise((resolve, reject) => {
		try {
			const reader = new FileReader();
			reader.addEventListener('load', () => {
				const image = new Image();
				image.src = reader.result as string;
				image.addEventListener('load', () => {
					resolve(image);
				});
			});

			reader.readAsDataURL(imageFile);
		} catch (e) {
			reject(e);
		}
	});

export { getImageFromFile };
