<template>
	<div class="col-xs-2">
		<div class="panel panel-default">
			<div class="panel-body">
				<div class="panel-title">
					<span class="h1 panel-counter">{{ count }}</span>
				</div>
				{{ label }}
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
	props: {
		count: {
			type: Number,
			required: true,
		},
		label: {
			type: String,
			required: true,
		},
	},
});
</script>

<style scoped>
.panel-counter {
	margin: 0;
}
</style>
