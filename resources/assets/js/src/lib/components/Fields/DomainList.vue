<template>
	<div>
		<div v-for="(domain, index) in domains" :key="'domain' + index" :class="errors['domains.' + index] ? 'error' : ''">
			<div class="form-group inline-icon">
				<input v-model="domains[index]" name="domains[]" type="text" placeholder="" class="form-control" />
				<span class="button-pill-close">
					<i class="af-icons af-icons-close-circle" @click="remove(domain)"></i>
				</span>
			</div>
			<span class="error-message">{{ errors['domains.' + index] ? errors['domains.' + index][0] : '' }}</span>
		</div>
		<div class="form-group">
			<button type="button" class="btn btn-secondary" :disabled="!canAddNewDomain" @click.prevent="addDomainRow">
				{{ lang.get('organisations.form.domains.add') }}
			</button>
		</div>
	</div>
</template>

<script>
import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';

export default {
	mixins: [langMixin],
	props: {
		baseDomains: {
			type: Array,
			required: true,
		},
		errors: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			domains: [],
			isInvalid: false,
		};
	},
	computed: {
		canAddNewDomain() {
			const domainRegex = /^([a-zA-Z0-9-]+\.){1,}[a-zA-Z]{2,}$/;
			const isValidDomain = this.domains.every((domain) => domainRegex.test(domain));
			const hasDuplicates = new Set(this.domains).size !== this.domains.length;
			return isValidDomain && !hasDuplicates;
		},
	},
	mounted() {
		this.domains = this.baseDomains.length === 0 ? [''] : this.baseDomains;
	},
	methods: {
		addDomainRow() {
			this.domains.push('');
		},
		remove(domain) {
			this.domains = this.domains.filter((d) => d !== domain);
		},
	},
};
</script>

<style scoped>
.inline-icon {
	display: flex;
	flex-direction: row;
	align-items: center;
}
</style>
