<template>
	<label :id="`${fieldId}-label`" :for="fieldId" class="label-with-chip">
		<field-access-icon v-if="showFieldAccess" :field="field" :is-manager="isManager" />
		<span v-output="label"></span>
		<slot name="lock-indicator"></slot>
	</label>
</template>

<script>
import FieldAccessIcon from './FieldAccessIcon';

export default {
	inject: ['lang'],
	components: {
		FieldAccessIcon,
	},
	props: {
		field: {
			type: Object,
			required: true,
		},
		isManager: {
			type: Boolean,
			default: false,
		},
		for: {
			type: String,
			default: null,
		},
		elementId: {
			type: String,
			default: '',
		},
	},
	computed: {
		isOptional() {
			return (
				!this.field.required &&
				!['content', 'formula'].includes(this.field.type) &&
				((this.field.entrantReadAccess && this.field.entrantWriteAccess) || this.field.resource === 'Users')
			);
		},
		label() {
			if (!this.isOptional) {
				return this.field.labelMarkdown ? this.field.labelMarkdown : this.field.title;
			}

			const text = !this.field.labelMarkdown ? this.field.title : this.field.labelMarkdown;

			if (!text) {
				return null;
			}

			// Split the text by <br> or line breaks
			const parts = text.split(/(\r\n|\n|\r|<br>|<br \/>|<\/p>)/);

			// First meaningful line
			const firstLine = parts[0].trim();
			const optionalText = this.lang.get('miscellaneous.optional');

			const remainingText = parts.slice(1).join('');

			if (firstLine.startsWith('<p')) {
				// Add optional text to the end of the first < /p>
				return `${firstLine}${optionalText}${remainingText}`;
			} else if (firstLine.startsWith('<ul') || firstLine.startsWith('<ol')) {
				// Add optional text to the end of the first <li>
				return text.replace(
					/<li([^>]*)>(.*?)<\/li>/,
					(match, attrs, content) => `<li${attrs}>${content}${optionalText}</li>`
				);
			} else {
				return `${firstLine}${optionalText}${remainingText}`;
			}
		},
		fieldId() {
			return this.for ? this.for : this.field.slug + '-' + this.elementId;
		},
		showFieldAccess() {
			return this.field.resource !== 'Users' && this.field.resource !== 'Organisations';
		},
	},
};
</script>
