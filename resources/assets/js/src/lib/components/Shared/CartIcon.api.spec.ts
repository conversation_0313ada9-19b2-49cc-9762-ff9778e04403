import { useApiItemCount } from '@/lib/components/Shared/CartIcon.api';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DataRequestMethod, DataResponse, dataSource } from '@/domain/services/Api/DataSource';

const { getItemsCount } = useApiItemCount();

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: (headers: Record<string, string>) => headers,
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'cart.items.count': 'cart/items-count',
		},
	},
}));

type ItemsCountResponse = DataResponse<{
	count: number;
}>;

describe('CartIcon Api', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('should call item count get endpoint when getItemCount is called', async () => {
		const responseData = {
			count: 2,
		};

		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: responseData } as ItemsCountResponse);

		const response = await getItemsCount();

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/cart/items-count',
			method: DataRequestMethod.GET,
			headers: {
				Accept: 'application/json',
			},
			data: undefined,
		});
		expect(response).toStrictEqual(responseData);
	});
});
