<template>
	<div v-if="date">
		<div class="date-output">
			{{ formatDate(date) }}
		</div>
		<div class="timezone-output">
			{{ formatTimezone(timezone || defaultTimezone) }}
		</div>
	</div>
</template>

<script>
import moment from 'moment';
import { mapState } from 'vuex';

export default {
	props: {
		date: {
			type: String,
			default: null,
		},
		timezone: {
			type: String,
			default: null,
		},
	},
	computed: {
		...mapState('global', ['momentLocale', 'momentDateFormat', 'momentTimeFormat', 'timezones', 'defaultTimezone']),
	},
	methods: {
		formatTimezone(timezone) {
			return (this.timezones.find((tz) => tz.id === timezone) || {}).name;
		},
		formatDate(date) {
			return moment(date)
				.locale(this.momentLocale)
				.format(this.momentDateFormat + ' ' + this.momentTimeFormat);
		},
	},
};
</script>
