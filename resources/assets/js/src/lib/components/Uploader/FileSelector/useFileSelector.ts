import { Ref, ref } from 'vue';

const useFileSelector = (
	dragAndDropElement: Ref<HTMLDivElement | null>,
	onDrop: (files: FileList | never[]) => void
) => {
	const isDragging = ref(false);
	const dragOver = (event: DragEvent) => {
		const target = event.target as HTMLElement;
		if (target.classList !== undefined && target.classList.contains('dnd-overlay')) {
			dragAndDropElement.value?.classList.add('drag-enter');
		}

		isDragging.value = true;
	};

	const dragLeave = (event: DragEvent) => {
		const target = event.target as HTMLElement;
		if (target.classList !== undefined && target.classList.contains('dnd-overlay')) {
			dragAndDropElement.value?.classList.remove('drag-enter');
		}

		isDragging.value = false;
	};

	const dragDrop = async (event: DragEvent) => {
		dragAndDropElement.value?.classList.remove('drag-enter');

		isDragging.value = false;

		const files = event?.dataTransfer?.files || [];

		onDrop(files);
	};

	return {
		isDragging,
		dragOver,
		dragLeave,
		dragDrop,
	};
};

type FileSelector = ReturnType<typeof useFileSelector>;

export { useFileSelector, FileSelector };
