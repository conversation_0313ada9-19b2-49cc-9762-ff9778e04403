import { beforeEach, describe, expect, it, vi } from 'vitest';
import { FileSelector, useFileSelector } from '@/lib/components/Uploader/FileSelector/useFileSelector';
import { ref, Ref } from 'vue';

describe('useFileSelector', () => {
	let mockDiv: HTMLDivElement;
	let dndElement: Ref<HTMLDivElement | null>;
	let onDrop: ReturnType<typeof vi.fn>;
	let dnd: FileSelector;

	beforeEach(() => {
		mockDiv = document.createElement('div');
		mockDiv.classList.add('dnd-overlay'); // Simulate the expected class
		dndElement = ref(mockDiv);
		onDrop = vi.fn();
		dnd = useFileSelector(dndElement, onDrop);
	});

	it('adds drag-enter class on dragOver if target has class dnd-overlay', () => {
		const event = {
			target: mockDiv,
		} as unknown as DragEvent;

		dnd.dragOver(event);
		expect(mockDiv.classList.contains('drag-enter')).toBe(true);
		expect(dnd.isDragging.value).toBe(true);
	});

	it('removes drag-enter class on dragLeave if target has class dnd-overlay', () => {
		mockDiv.classList.add('drag-enter'); // Pre-add
		const event = {
			target: mockDiv,
		} as unknown as DragEvent;

		dnd.dragLeave(event);
		expect(mockDiv.classList.contains('drag-enter')).toBe(false);
		expect(dnd.isDragging.value).toBe(false);
	});

	it('removes drag-enter and calls onDrop on dragDrop', async () => {
		mockDiv.classList.add('drag-enter');
		const fakeFiles = [{ name: 'test.txt' }] as unknown as FileList;
		const event = {
			dataTransfer: {
				files: fakeFiles,
			},
		} as DragEvent;

		await dnd.dragDrop(event);
		expect(mockDiv.classList.contains('drag-enter')).toBe(false);
		expect(dnd.isDragging.value).toBe(false);
		expect(onDrop).toHaveBeenCalledWith(fakeFiles);
	});

	it('calls onDrop with empty array if no files in dataTransfer', async () => {
		const event = {
			dataTransfer: null,
		} as DragEvent;

		await dnd.dragDrop(event);
		expect(onDrop).toHaveBeenCalledWith([]);
	});
});
