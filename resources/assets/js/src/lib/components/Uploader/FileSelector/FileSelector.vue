<template>
	<div
		ref="dragAndDropElement"
		:class="classes"
		@dragleave="dragLeave"
		@dragover.prevent="dragOver"
		@drop.prevent="dragDrop"
	>
		<div class="dnd">
			<div class="dnd-icon">
				<i class="af-icons af-icons-drag-n-drop-uploader"></i>
			</div>
			<div>
				<span>{{ lang.transChoice('files.buttons.drag_and_drop', 1) }}</span>
			</div>
			<div>
				<span>{{ lang.get('miscellaneous.search.or') }}</span>
			</div>
			<button type="button" :class="['upload-button', 'btn', 'btn-secondary']" @click.prevent.stop="openFileDialog">
				<slot name="btn-upload-text">
					<translation :text="lang.get('files.buttons.single')" />
				</slot>
			</button>
			<input ref="fileInput" type="file" style="display: none" @change.prevent.stop="handleFileChange" />
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { useController } from '@/domain/services/Composer';
import { fileSelectorController, View } from '@/lib/components/Uploader/FileSelector/FileSelector.controller';

export default defineComponent({
	props: {
		isUploading: {
			type: Boolean,
			default: false,
		},
		showFileDialog: {
			type: Boolean,
			default: false,
		},
	},
	setup: useController(fileSelectorController, 'FileSelectorController') as () => View,
});
</script>
<style scoped>
.action-card {
	float: none;
}
</style>
