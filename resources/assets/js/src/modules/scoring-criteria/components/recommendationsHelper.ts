interface Recommendation {
	[key: string]: number;
}

interface Translated {
	[lang: string]: {
		recommendationLabel: string;
	};
}

interface Criterion {
	id: [number, null];
	recommendations: string;
	translated: Translated;
}

interface ParsedRecommendation {
	key: string;
	new: boolean;
	score: number;
	translated: {
		[lang: string]: string;
	};
}

const getTranslatedLabels = (
	translated: Translated,
	supportedLanguages: string[],
	key: string
): { [lang: string]: string } =>
	supportedLanguages.reduce(
		(trans, lang) => ({
			...trans,
			[lang]: translated[lang]?.recommendationLabel ? JSON.parse(translated[lang].recommendationLabel)[key] : '\r\n',
		}),
		{} as { [lang: string]: string }
	);

/**
 * Extract keys from JSON string in their original order
 * This is necessary because JSON.parse() reorders numeric keys in ascending order
 */
const extractJsonKeysInOrder = (jsonString: string): string[] => {
	const keyMatches = [...jsonString.matchAll(/"([^"]+)":/g)];

	return keyMatches.map((match) => match[1]);
};

export function parseRecommendations(criterion: Criterion, supportedLanguages: string[] = []): ParsedRecommendation[] {
	const recommendationsJson = criterion.recommendations || '{}';
	const recommendation: Recommendation = JSON.parse(recommendationsJson);
	const translated: Translated = JSON.parse(JSON.stringify(criterion.translated || {}));

	return extractJsonKeysInOrder(recommendationsJson).map((key) => ({
		key: key,
		new: !criterion.id || recommendation[key] === 0,
		score: recommendation[key] || 0,
		translated: getTranslatedLabels(translated, supportedLanguages, key),
	}));
}
