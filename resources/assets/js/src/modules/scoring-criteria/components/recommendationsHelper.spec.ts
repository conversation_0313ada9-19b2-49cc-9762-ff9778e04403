import { parseRecommendations } from '@/modules/scoring-criteria/components/recommendationsHelper';
import { describe, expect, it } from 'vitest';

describe('parseRecommendations', () => {
	it('should parse recommendations correctly', () => {
		const criterion = {
			recommendations: '{"Gold":3,"Silver":2,"Bronze":1,"Zero":0}',
			translated: {
				en_GB: { recommendationLabel: '{"Gold":"Gold","Silver":"Silver","Bronze":"Bronze","Zero":"Zero"}' },
				fr_CA: { recommendationLabel: '{"Gold":"Or","Silver":"Argent","Bronze":"Bronze","Zero":"Zéro"}' },
			},
		};
		const supportedLanguages = ['en_GB', 'fr_CA'];

		const result = parseRecommendations(criterion, supportedLanguages);

		expect(result).toEqual([
			{
				key: 'Gold',
				new: true,
				score: 3,
				translated: { en_GB: 'Gold', fr_CA: 'Or' },
			},
			{
				key: 'Silver',
				new: true,
				score: 2,
				translated: { en_GB: 'Silver', fr_CA: 'Argent' },
			},
			{
				key: 'Bronze',
				new: true,
				score: 1,
				translated: { en_GB: 'Bronze', fr_CA: 'Bronze' },
			},
			{
				key: 'Zero',
				new: true,
				score: 0,
				translated: { en_GB: 'Zero', fr_CA: 'Zéro' },
			},
		]);
	});

	it('should preserve order of numeric keys in descending order', () => {
		const criterion = {
			id: [1, null],
			recommendations: '{"3":3,"2":2,"1":1}',
			translated: {
				en_GB: { recommendationLabel: '{"3":"Option 3","2":"Option 2","1":"Option 1"}' },
			},
		};
		const supportedLanguages = ['en_GB'];

		const result = parseRecommendations(criterion, supportedLanguages);

		// Verify the order is preserved as 3, 2, 1 (not reordered to 1, 2, 3)
		expect(result.map((r) => r.key)).toEqual(['3', '2', '1']);
		expect(result).toEqual([
			{
				key: '3',
				new: false,
				score: 3,
				translated: { en_GB: 'Option 3' },
			},
			{
				key: '2',
				new: false,
				score: 2,
				translated: { en_GB: 'Option 2' },
			},
			{
				key: '1',
				new: false,
				score: 1,
				translated: { en_GB: 'Option 1' },
			},
		]);
	});

	it('should preserve order of mixed numeric and string keys', () => {
		const criterion = {
			recommendations: '{"4":4,"B":2,"1":1,"A":3}',
			translated: {
				en_GB: { recommendationLabel: '{"4":"Fourth","B":"Second","1":"First","A":"Third"}' },
			},
		};
		const supportedLanguages = ['en_GB'];

		const result = parseRecommendations(criterion, supportedLanguages);

		// Verify the order is preserved as 4, B, 1, A
		expect(result.map((r) => r.key)).toEqual(['4', 'B', '1', 'A']);
		expect(result).toEqual([
			{
				key: '4',
				new: true,
				score: 4,
				translated: { en_GB: 'Fourth' },
			},
			{
				key: 'B',
				new: true,
				score: 2,
				translated: { en_GB: 'Second' },
			},
			{
				key: '1',
				new: true,
				score: 1,
				translated: { en_GB: 'First' },
			},
			{
				key: 'A',
				new: true,
				score: 3,
				translated: { en_GB: 'Third' },
			},
		]);
	});
});
