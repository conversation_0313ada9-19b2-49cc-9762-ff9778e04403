<template>
	<div class="row">
		<entry-fields :tab="tab" :transition="transition" class="col-xs-12" />
		<div class="col-xs-12 col-md-6">
			<div class="contributors-container">
				<div v-if="!hasRefereeReviewStages" class="alert-warning island" role="alert">
					<div class="icon">
						<i class="af-icons-md af-icons-md-alert-warning"></i>
					</div>
					<div class="message">
						<p>{{ lang.get('entries.form.referee.no-review-stage') }}</p>
					</div>
				</div>
				<referee-configurator v-if="configurationMode && referees.length === 0" :tab="tab"> </referee-configurator>
				<template v-else-if="hasRefereeReviewStages">
					<referee
						v-for="(referee, index) in referees"
						:key="'referee-' + index"
						:position="index + 1"
						:referee="referee"
						:eligible-readonly="ineligible || eligibleReadOnly"
						@deleteReferee="deleteReferee"
						@initiatedReviewStage="initiatedReviewStage"
					>
						<draggable-fields
							v-if="configurationMode"
							:fields="refereeFields"
							:group="groupName(referee.id)"
							:lock-tabs="true"
							:resource="referee"
							:resource-name="resourceName"
							:tab="tab"
							:visible-fields="visibleRefereeFields(referee)"
						/>
						<fields
							v-else
							:fields="visibleRefereeFields(tab.id, referee)"
							:resource-name="resourceName"
							:resource="referee"
							:tab="tab"
							:transition="transition"
						/>
					</referee>
				</template>
				<button
					:disabled="
						refereeLimitReached() ||
						configurationMode ||
						!hasRefereeReviewStages ||
						ineligible ||
						eligibleReadOnly ||
						!access.canEdit
					"
					class="btn btn-primary btn-lg"
					type="button"
					@click.prevent="addReferee"
				>
					{{ lang.get('entries.form.add-referee') }}
				</button>
			</div>
		</div>
		<buttons-container v-if="isLoaded && !configure" class="col-xs-12" />
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import { refereesTabController, View } from '@/modules/entry-form/Tabs/RefereesTab.controller';
import { useController } from '@/domain/services/Composer';
import Translation from '@/modules/interface-text/components/Translation.vue';
import EntryFields from '@/modules/entry-form/Fields/EntryFields.vue';
import Referee from '@/modules/entry-form/Referee.vue';
import DraggableFields from '@/modules/entry-form/Configuration/DraggableFields.vue';
import Fields from '@/modules/entry-form/Fields/Fields.vue';
import ButtonsContainer from '@/modules/entry-form/Buttons/ButtonsContainer.vue';
import RefereeConfigurator from '@/modules/entry-form/Configuration/RefereeConfigurator.vue';

export default defineComponent({
	components: { RefereeConfigurator, ButtonsContainer, Fields, DraggableFields, Referee, EntryFields, Translation },
	props: {
		tab: {
			type: Object,
			required: true,
		},
		transition: {
			type: String,
			default: 'push',
		},
		configure: {
			type: Boolean,
			required: true,
		},
	},
	setup: useController(refereesTabController, 'refereesTabController') as () => View,
});
</script>
