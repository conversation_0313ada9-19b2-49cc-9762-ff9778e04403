import { Composer } from '@/domain/services/Composer';
import OrganisationLogo from '@/modules/organisations/components/logo/OrganisationLogo.vue';
import OrganisationView from '@/modules/organisations/components/OrganisationView.vue';
import PortalVue from '@/plugins/portal-vue';
import TabbedContent from '@/lib/components/TabbedContent.vue';
import VuexPlugin from '@/plugins/vuex';

export default () => {
	Composer.registerVuePlugins({
		...VuexPlugin(),
		...PortalVue(),
	});

	Composer.registerComponents({
		TabbedContent,
		OrganisationView,
		OrganisationLogo,
	});
};
