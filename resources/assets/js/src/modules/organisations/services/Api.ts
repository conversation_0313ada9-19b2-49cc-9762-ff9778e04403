import { DeletingParametersEndpoint } from '@/domain/services/Api/Endpoint';
import { RoutesGetter, useRoutesDao } from '@/domain/dao/Routes';

const routes = useRoutesDao<{
	organisation: {
		delete: () => string;
	};
}>();

type DeleteOrganisationRequest = {
	selected: string[];
};

const deleteOrganisation = (parameters: DeleteOrganisationRequest) =>
	DeletingParametersEndpoint<DeleteOrganisationRequest, null>(routes.organisation.delete as RoutesGetter)()(parameters);

export { deleteOrganisation, DeleteOrganisationRequest };
