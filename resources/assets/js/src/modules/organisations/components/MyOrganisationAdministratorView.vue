<template>
	<tabbed-content :tabs="tabs">
		<template slot="overview">
			<div class="tabs-panels">
				<!-- Profile -->
				<div class="row">
					<div class="col-xs-12 col-lg-6">
						<div class="panel panel-default">
							<div class="panel-body">
								<div class="panel-title">
									<h4>{{ lang.get('organisations.titles.organisation') }}</h4>
								</div>
								<div class="row">
									<description-list-item :value="organisation.name" :label="lang.get('organisations.table.columns.name')">
									</description-list-item>
									<description-list-item
										:value="organisation.administrator.fullName"
										:label="lang.get('organisations.form.administrator')"
									>
									</description-list-item>
								</div>
							</div>
						</div>
					</div>
					<counter-panel :count="memberCount" :label="'Users'"></counter-panel>
				</div>
				<!-- Fields -->
				<!-- IMPLEMENT AFTER MVP -->
				<!-- Domains -->
				<div class="row">
					<div class="col-xs-12 col-lg-6">
						<div class="panel panel-default">
							<div class="panel-body">
								<div class="panel-title">
									<h4>{{ lang.get('organisations.form.domains.authorised') }}</h4>
								</div>
								<p>
									{{ lang.get('organisations.my.authorised_domains.description') }}
								</p>
								<div class="row">
									<div class="col-md-12">
										<span v-if="organisation.domains.length">{{ organisation.domains.join(', ') }}</span>
										<span v-else>{{ lang.get('organisations.form.domains.not_configured') }}</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</template>
	</tabbed-content>
</template>
<script lang="ts">
import { Organisation } from '@/modules/organisations/models/Organisation';
import { useController } from '@/domain/services/Composer';
import {
	myOrganisationAdministratorViewController,
	Props,
	View,
} from '@/modules/organisations/components/MyOrganisationAdministratorView.controller';
import { defineComponent, PropType } from 'vue';
import TabbedContent from '@/lib/components/TabbedContent.vue';
import CounterPanel from '@/lib/components/CounterPanel.vue';
import DescriptionListItem from '@/lib/components/Shared/DescriptionListItem.vue';

export default defineComponent<Props, View>({
	components: { DescriptionListItem, CounterPanel, TabbedContent },
	props: {
		organisation: {
			type: Object as PropType<Organisation>,
			required: true,
		},
		memberCount: {
			type: Number,
			required: true,
		},
	},
	setup: useController(
		myOrganisationAdministratorViewController,
		'MyOrganisationAdministratorViewController'
	) as () => View,
});
</script>
