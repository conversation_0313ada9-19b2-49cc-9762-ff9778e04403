import { deleteOrganisation } from '@/modules/organisations/services/Api';
import { Season } from '@/modules/seasons/models/Season';
import { useContainer } from '@/domain/services/Container';
import { User } from '@/domain/models/User';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';
import { Organisation, OrganisationCreatedBy } from '@/modules/organisations/models/Organisation';
import { organisationViewController, View } from '@/modules/organisations/components/OrganisationView.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'organisation.delete': 'organisation',
			'users.show': 'users/{user}',
		},
	},
}));

vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

vi.mock('@/modules/organisations/services/Api', () => ({
	deleteOrganisation: vi.fn(),
}));

const administrator: User = {
	email: '<EMAIL>',
	slug: 'theAdministrator',
	firstName: 'The',
	lastName: 'Administrator',
	fullName: 'The Administrator',
	initials: 'TA',
	img: 'theImg',
	color: '#000000',
};

const createdByUser: User = {
	email: '<EMAIL>',
	slug: 'theCreator',
	firstName: 'The',
	lastName: 'Creator',
	fullName: 'The Creator',
	initials: 'TC',
	img: 'theImg',
	color: '#000000',
};

const season: Season = {
	slug: 'seasonSlug',
	name: 'Active season',
};

const organisation: Organisation = {
	id: 123,
	name: 'The organisation',
	administrator,
	domains: ['domain1.test', 'domain2.test'],
	createdBy: OrganisationCreatedBy.System,
	createdByUser: null,
	createdAt: '2025-01-01T00:00:00',
	updatedAt: '2025-03-01T00:00:00',
	season,
};

describe('OrganisationView controller', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi.clearAllMocks();
		(useContainer as Mock).mockReturnValue({ onMounted: vi.fn() });
	});

	it('should initialize with organisation', () => {
		const view = organisationViewController({ organisation }) as View;

		expect(view.organisation.value).toEqual(organisation);
		expect(view.administratorLink).toBe('/users/theAdministrator');
		expect(view.showConfirmDelete.value).toBe(false);
	});

	it('should show and hide delete confirmation dialog', () => {
		const view = organisationViewController({ organisation }) as View;

		view.showDeleteConfirmationDialog();
		expect(view.showConfirmDelete.value).toBe(true);

		view.hideDeleteConfirmationDialog();
		expect(view.showConfirmDelete.value).toBe(false);
	});

	it('should delete organisation', async () => {
		const view = organisationViewController({ organisation }) as View;

		await view.deleteAction();
		expect(deleteOrganisation).toHaveBeenCalledWith({ selected: [organisation.id] });
	});

	it('should not show user link if created by is not User', () => {
		const view = organisationViewController({ organisation }) as View;

		expect(view.showUserLink.value).toBe(false);
	});

	it('should show user link if created by is User', () => {
		const view = organisationViewController({
			organisation: { ...organisation, createdBy: OrganisationCreatedBy.User, createdByUser },
		}) as View;

		expect(view.showUserLink.value).toBe(true);
		expect(view.createdByUserLink).toBe('/users/theCreator');
	});

	it('shows administrator full name when present', () => {
		organisation.administrator = { fullName: 'Test Administrator' };
		const view = organisationViewController({ organisation }) as View;

		expect(view.administratorFullName.value).toBe('Test Administrator');
	});

	it('shows em when administrator is not present', () => {
		organisation.administrator = null;
		const view = organisationViewController({ organisation }) as View;

		expect(view.administratorFullName.value).toBe('—');
	});
});
