import { apiRequest } from '@/modules/billing/services/Api';
import { deleteOrganisation } from '@/modules/organisations/services/Api';
import { useRoutesDao } from '@/domain/dao/Routes';
import { Organisation, OrganisationCreatedBy } from '@/modules/organisations/models/Organisation';
import { ref, Ref } from 'vue';
import { Trans, trans } from '@/domain/dao/Translations';

type Props = {
	organisation: Organisation;
};

type View = {
	lang: Trans;
	organisation: Ref<Organisation>;
	administratorLink: string;
	administratorFullName: Ref<string>;
	createdByUserLink: string;
	showConfirmDelete: Ref<boolean>;
	showUserLink: Ref<boolean>;
	showDeleteConfirmationDialog: () => void;
	hideDeleteConfirmationDialog: () => void;
	deleteAction: () => void;
};

const organisationViewController = (props: Props): View => {
	const routes = useRoutesDao<{
		users: {
			show: (p: { user: string }) => string;
		};
	}>();

	const organisation = ref(props.organisation);
	const administratorLink = routes.users.show({ user: organisation.value.administrator?.slug || '' });
	const administratorFullName = ref(props.organisation.administrator?.fullName || '—');
	const createdByUserLink = routes.users.show({ user: organisation.value.createdByUser?.slug || '' });
	const showConfirmDelete = ref(false);
	const showUserLink = ref(organisation.value.createdBy === OrganisationCreatedBy.User);

	const showDeleteConfirmationDialog = () => {
		showConfirmDelete.value = true;
	};

	const hideDeleteConfirmationDialog = () => {
		showConfirmDelete.value = false;
	};

	const deleteAction = async () => {
		await apiRequest(deleteOrganisation({ selected: [organisation.value.id] }));
	};

	return {
		lang: trans(),
		organisation,
		administratorLink,
		administratorFullName,
		createdByUserLink,
		showConfirmDelete,
		showUserLink,
		showDeleteConfirmationDialog,
		hideDeleteConfirmationDialog,
		deleteAction,
	};
};

export { Props, View, organisationViewController };
