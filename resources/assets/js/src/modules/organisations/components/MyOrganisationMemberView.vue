<template>
	<tabbed-content :tabs="tabs">
		<template slot="overview">
			<div class="tabs-panels">
				<!-- Profile -->
				<div class="row">
					<div class="col-xs-12 col-lg-6">
						<div class="panel panel-default">
							<div class="panel-body">
								<div class="panel-title">
									<h4>{{ lang.get('organisations.titles.organisation') }}</h4>
								</div>
								<div class="row">
									<description-list-item :value="organisation.name" :label="lang.get('organisations.table.columns.name')">
									</description-list-item>
									<description-list-item :label="lang.get('organisations.form.administrator')">
										<template v-if="organisation.administrator" slot="value">
											<span v-if="organisation.administrator.email" class="mail-link">
												<a class="no-underline" :href="'mailto:' + organisation.administrator.email">
													{{ administratorFullName }}
												</a>
												<i class="af-icons af-icons-email"></i>
											</span>
											<span v-else>{{ administratorFullName }}</span>
										</template>
									</description-list-item>
								</div>
							</div>
						</div>
					</div>
					<counter-panel :count="memberCount" :label="'Users'"></counter-panel>
				</div>
				<!-- Fields -->
				<!-- IMPLEMENT AFTER MVP -->
				<div class="row">
					<div class="col-xs-12 col-lg-6">
						<button class="action-button">{{ lang.get('organisations.my.buttons.leave') }}</button>
					</div>
				</div>
			</div>
		</template>
	</tabbed-content>
</template>
<script lang="ts">
import { Organisation } from '@/modules/organisations/models/Organisation';
import { useController } from '@/domain/services/Composer';
import {
	myOrganisationMemberViewController,
	Props,
	View,
} from '@/modules/organisations/components/MyOrganisationMemberView.controller';
import { defineComponent, PropType } from 'vue';
import TabbedContent from '@/lib/components/TabbedContent.vue';
import CounterPanel from '@/lib/components/CounterPanel.vue';
import DescriptionListItem from '@/lib/components/Shared/DescriptionListItem.vue';

export default defineComponent<Props, View>({
	components: { DescriptionListItem, CounterPanel, TabbedContent },
	props: {
		organisation: {
			type: Object as PropType<Organisation>,
			required: true,
		},
		memberCount: {
			type: Number,
			required: true,
		},
	},
	setup: useController(myOrganisationMemberViewController, 'MyOrganisationMemberViewController') as () => View,
});
</script>
<style scoped lang="scss">
.mail-link {
	display: flex;
	gap: 6px;

	i {
		font-size: 18px;
	}
}
</style>
