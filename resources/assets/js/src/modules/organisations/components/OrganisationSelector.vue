<template>
	<div class="organisations-selector">
		<search-field
			:id="'organisation-search'"
			:name="'organisation-search'"
			:label="lang.get('organisations.titles.organisation')"
			:search-label="lang.get('buttons.search')"
			:reset-label="lang.get('buttons.reset')"
			:advanced-search="false"
			url="/organisation/autocomplete"
			:search-keywords="searchKeywords"
			:on-search="search"
			@update:searchKeywords="searchKeywords = $event"
			@reset="reset"
		></search-field>
		<div class="row">
			<div class="col-xs-12">
				<div class="form-group">
					<multiselect
						:filter="false"
						:options="availableOrganisations"
						:select-all-label="lang.get('multiselect.select_all')"
						value-property="name"
						value-popover-placement="right"
						value-popover-property="title"
						:remember-selection="true"
						:counter="false"
						help-icon-class="af-icons af-icons-help"
						:show-nothing-found="!availableOrganisations.length"
						:nothing-found-label="lang.get('search.nothing-found')"
						@selected="select"
					/>
					<p v-if="selectedOrganisations.length >= 100" class="help-text">
						{{ lang.get('assignments.form.judges.capped') }}
					</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import {
	organisationSelectorController,
	View,
} from '@/modules/organisations/components/OrganisationSelector.controller';
import SearchField from '@/lib/components/Judging/SearchField.vue';
import { Multiselect, SelectField } from 'vue-bootstrap';
import { useController } from '@/domain/services/Composer';
import EmailsBox from '@/lib/components/Shared/EmailsBox.vue';

export default defineComponent<View>({
	components: {
		Multiselect,
		EmailsBox,
		SearchField,
		SelectField,
	},
	setup: useController(organisationSelectorController, 'OrganisationSelectorController') as () => View,
});
</script>
