import { Organisation } from '@/modules/organisations/models/Organisation';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
	myOrganisationAdministratorViewController,
	View,
} from '@/modules/organisations/components/MyOrganisationAdministratorView.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
	},
}));

describe('MyOrganisationAdministratorView controller', () => {
	let organisation: Organisation;
	let memberCount: number;

	beforeEach(() => {
		organisation = {
			id: '1',
			name: 'Test Organisation',
			administrator: null,
			domains: [],
			createdBy: null,
			createdByUser: null,
			createdAt: '',
			updatedAt: '',
			season: null,
		};
		memberCount = 10;
	});

	it('initializes with organisation and member count', () => {
		const view = myOrganisationAdministratorViewController({ organisation, memberCount }) as View;

		expect(view.organisation.value).toEqual(organisation);
		expect(view.memberCount.value).toBe(memberCount);
		expect(view.tabs.value).toEqual([
			{
				id: 'overview',
				name: expect.any(String),
				active: true,
			},
		]);
	});

	it('activates a tab', () => {
		const view = myOrganisationAdministratorViewController({ organisation, memberCount }) as View;

		view.tabs.value.push({ id: 'settings', name: 'Settings', active: false });
		view.tabs.value.forEach((tab) => (tab.active = false));
		view.tabs.value[1].active = true;

		expect(view.tabs.value[1].active).toBe(true);
		expect(view.tabs.value[0].active).toBe(false);
	});
});
