<template>
	<div class="tabs-panels">
		<div class="row">
			<div class="col-md-6">
				<ul class="action-list-horizontal">
					<li>
						<button class="action-button" @click="showDeleteConfirmationDialog">{{ lang.get('buttons.delete') }}</button>
					</li>
					<!-- IMPLEMENT AFTER MVP -->
					<!--<li>-->
					<!--  <button class="action-button">{{ lang.get('buttons.delete_permanently') }}</button>-->
					<!--</li>-->
				</ul>
			</div>
		</div>
		<!-- Profile -->
		<div class="row">
			<div class="col-xs-12 col-lg-6">
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="panel-title">
							<h4>{{ lang.get('organisations.titles.organisation') }}</h4>
						</div>
						<div class="row">
							<description-list-item :value="organisation.name" :label="lang.get('organisations.table.columns.name')">
							</description-list-item>
							<description-list-item :label="lang.get('organisations.form.administrator')">
								<template slot="value">
									<a v-if="administratorLink" class="no-underline" :href="administratorLink" target="_blank">
										{{ administratorFullName }}
									</a>
									<span v-else>{{ administratorFullName }}</span>
								</template>
							</description-list-item>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Fields -->
		<!-- IMPLEMENT AFTER MVP -->
		<!-- Domains -->
		<div class="row">
			<div class="col-xs-12 col-lg-6">
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="panel-title">
							<h4>{{ lang.get('organisations.form.domains.authorised') }}</h4>
						</div>
						<div class="row">
							<div class="col-md-12">
								<span v-if="organisation.domains.length">{{ organisation.domains.join(', ') }}</span>
								<span v-else>{{ lang.get('organisations.form.domains.not_configured') }}</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- Metadata -->
		<div class="row">
			<div class="col-xs-12 col-lg-6">
				<div class="panel panel-default">
					<div class="panel-body">
						<div class="panel-title">
							<h4>{{ lang.get('organisations.form.metadata') }}</h4>
						</div>
						<div class="row">
							<description-list-item :label="lang.get('organisations.form.season_joined')" :value="organisation.season.name">
							</description-list-item>
							<description-list-item :label="lang.get('organisations.form.created_by.label')">
								<template slot="value">
									<a v-if="showUserLink" :href="createdByUserLink" class="no-underline" target="_blank">
										{{ organisation.createdByUser.fullName }}
									</a>
									<span v-else>{{ lang.get(`organisations.form.created_by.values.${organisation.createdBy}`) }}</span>
								</template>
							</description-list-item>
							<description-list-item :label="lang.get('search.columns.created')">
								<template slot="value">
									<date-timezone :date="organisation.createdAt" timezone=""></date-timezone>
								</template>
							</description-list-item>
							<description-list-item :label="lang.get('search.columns.updated')">
								<template slot="value">
									<date-timezone :date="organisation.updatedAt" timezone=""></date-timezone>
								</template>
							</description-list-item>
							<description-list-item :label="lang.get('organisations.form.id')" :value="organisation.id.toString()">
							</description-list-item>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Delete confirmation -->
		<confirmation-modal
			:key="`modal-target-delete-organisation-${organisation.id}`"
			:modal-id="`modal-target-delete-organisation-${organisation.id}`"
			:show-modal="showConfirmDelete"
			:confirmation="lang.choice('miscellaneous.alerts.delete.item', 1)"
			:confirm-button-label="lang.get('buttons.delete')"
			:cancel-button-label="lang.get('buttons.cancel')"
			@closed="hideDeleteConfirmationDialog"
			@confirmed="deleteAction"
		/>
	</div>
</template>

<script lang="ts">
import DateTimezone from '@/lib/components/Shared/DateTimezone';
import DescriptionListItem from '@/lib/components/Shared/DescriptionListItem.vue';
import { Organisation } from '@/modules/organisations/models/Organisation';
import TabContent from '@/modules/theme/layout/TabContents.vue';
import { useController } from '@/domain/services/Composer';
import { defineComponent, PropType } from 'vue';
import {
	organisationViewController,
	Props,
	View,
} from '@/modules/organisations/components/OrganisationView.controller';
import ConfirmationModal from '@/lib/components/Shared/ConfirmationModal.vue';

export default defineComponent<Props, View>({
	components: {
		ConfirmationModal,
		DateTimezone,
		DescriptionListItem,
		TabContent,
	},
	props: {
		organisation: {
			type: Object as PropType<Organisation>,
			required: true,
		},
	},

	setup: useController(organisationViewController, 'OrganisationViewController') as () => View,
});
</script>
