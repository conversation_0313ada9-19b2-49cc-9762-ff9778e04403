import { OrganisationSelectorEvents } from '@/modules/organisations/components/OrganisationSelector.events';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
	organisationSelectorController,
	View,
} from '@/modules/organisations/components/OrganisationSelector.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
	},
}));

describe('OrganisationSelector controller', () => {
	let emit: (event: string, payload: never) => void;

	beforeEach(() => {
		emit = vi.fn();
	});

	it('should initialize with default values', () => {
		const view = organisationSelectorController({}, { emit }) as View;

		expect(view.selectedOrganisations.value).toEqual([]);
		expect(view.searchKeywords.value).toBe('');
		expect(view.searchResults.value).toEqual([]);
	});

	it('should update search results', () => {
		const view = organisationSelectorController({}, { emit }) as View;

		view.search([{ id: '1', name: 'Org 1' }]);
		expect(view.searchResults.value).toEqual([{ id: '1', name: 'Org 1' }]);
	});

	it('should reset all values', () => {
		const view = organisationSelectorController({}, { emit }) as View;

		view.searchKeywords.value = 'test';
		view.searchResults.value = [{ id: '1', name: 'Org 1' }];
		view.selectedOrganisations.value = ['1'];

		view.reset();

		expect(view.searchKeywords.value).toBe('');
		expect(view.searchResults.value).toEqual([]);
		expect(view.selectedOrganisations.value).toEqual([]);
		expect(emit).toHaveBeenCalledWith(OrganisationSelectorEvents.Selected, []);
	});

	it('should select organisations', () => {
		const view = organisationSelectorController({}, { emit }) as View;

		view.select(['1', '2']);
		expect(view.selectedOrganisations.value).toEqual(['1', '2']);
		expect(emit).toHaveBeenCalledWith(OrganisationSelectorEvents.Selected, ['1', '2']);
	});
});
