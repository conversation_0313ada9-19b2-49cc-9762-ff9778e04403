import { Organisation } from '@/modules/organisations/models/Organisation';
import { Tab } from '@/lib/components/TabbedContent.controller';
import { ref, Ref, SetupFunction } from 'vue';
import { Trans, trans } from '@/domain/dao/Translations';

type View = {
	lang: Trans;
	organisation: Ref<Organisation>;
	tabs: Ref<Tab[]>;
	memberCount: Ref<number>;
	administratorFullName: Ref<string>;
};

type Props = {
	organisation: Organisation;
	memberCount: number;
};

const myOrganisationMemberViewController: SetupFunction<Props, View> = (props): View => {
	const organisation = ref(props.organisation);
	const memberCount = ref(props.memberCount);
	const administratorFullName = ref(props.organisation.administrator?.fullName || '—');

	const tabs = ref<Tab[]>([
		{
			id: 'overview',
			name: trans().get('shared.overview'),
			active: true,
		},
	]);

	return {
		lang: trans(),
		organisation,
		tabs,
		memberCount,
		administratorFullName,
	};
};

export { myOrganisationMemberViewController, Props, View };
