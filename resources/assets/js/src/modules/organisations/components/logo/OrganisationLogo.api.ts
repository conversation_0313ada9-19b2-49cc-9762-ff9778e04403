import { UploadedFile } from '@/lib/types/UploadedFile';
import { useRoutesDao } from '@/domain/dao/Routes';
import { DeletingEndpoint, GettingEndpoint, PostingEndpoint, PuttingEndpoint } from '@/domain/services/Api/Endpoint';
import { File, Status } from '@/domain/models/File';

type Routes = {
	upload: string;
	status: string;
};

type FileUpdateRequest = {
	file: number;
};

type FileUpdateResponse = {
	image: File;
};

const routes = useRoutesDao<{
	/* eslint-disable @typescript-eslint/naming-convention */
	'file.own.delete': string;
	'organisation.logo.update': string;
	'organisation.logo.delete': string;
	/* eslint-enable @typescript-eslint/naming-convention */
}>();

const deleteFile = DeletingEndpoint<[]>(routes['file.own.delete']);
const deleteLogo = DeletingEndpoint<[]>(routes['organisation.logo.delete']);
const updateLogo = (organisationId: string, payload: FileUpdateRequest) =>
	PuttingEndpoint<FileUpdateRequest, FileUpdateResponse>(routes['organisation.logo.update'])(organisationId)(payload);

const uploadFile = (url: string) =>
	PostingEndpoint<
		UploadedFile,
		{
			token: string;
			file: number;
		}
	>(url);

const getUploadStatus = (url: string) =>
	GettingEndpoint<{
		id: File['id'];
		processingStatus: string;
		attachmentId: number;
		status: Status;
		statusMessage: string;
		fileUrl: File['url'];
		imageUrl: File['image'];
		source: string;
	}>(url)();

const useApi = (routes: Routes) => ({
	upload: uploadFile(routes.upload)(),
	status: (fileId: number) => getUploadStatus(routes.status + fileId),
	delete: deleteFile,
	updateLogo: updateLogo,
	deleteLogo: deleteLogo,
});

type OrganisationLogoApi = ReturnType<typeof useApi>;

export { useApi, OrganisationLogoApi, FileUpdateResponse, FileUpdateRequest };
