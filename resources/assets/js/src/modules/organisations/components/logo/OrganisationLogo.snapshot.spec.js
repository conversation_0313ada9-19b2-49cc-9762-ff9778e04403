import { Composer } from '@/domain/services/Composer';
import { expect } from 'chai';
import Logo from '@/modules/organisations/components/logo/Logo.vue';
import OrganisationLogo from '@/modules/organisations/components/logo/OrganisationLogo.vue';
import { shallowMount } from '@vue/test-utils';

const defaultProps = {
	organisation: {
		id: 'org-123',
		administrator: { slug: 'slug' },
		logo: {
			id: 321,
			image: 'image-url',
		},
	},
	uploadOptions: { routes: {} },
};

const baseView = {
	lang: { get: (key) => key },
	showModal: true,
	toggle: () => '',
	imageSrc: 'some-image-src',
	file: {},
	confirmLogo: () => '',
	organisationLogo: {},
	fileUploaded: (file) => file,
	organisation: {},
	closeModal: () => '',
	openModal: () => '',
	logoUploaderComponent: {},
	canUpdateLogo: true,
};

describe('OrganisationLogo', () => {
	it('should not show logo if logo cant be updated and image is not set', () => {
		Composer.mockView('OrganisationLogoController', { ...baseView, canUpdateLogo: false, imageSrc: null });

		const organisationLogo = shallowMount(OrganisationLogo, { propsData: { ...defaultProps } });

		expect(organisationLogo.findComponent(Logo).exists()).to.be.false;
	});

	it('should show logo if logo can be updated and image is not set', () => {
		Composer.mockView('OrganisationLogoController', { ...baseView, canUpdateLogo: true, imageSrc: null });

		const organisationLogo = shallowMount(OrganisationLogo, { propsData: { ...defaultProps } });

		expect(organisationLogo.findComponent(Logo).exists()).to.be.true;
	});

	it('should show logo if logo can be updated and image is set', () => {
		Composer.mockView('OrganisationLogoController', { ...baseView, canUpdateLogo: true, imageSrc: 'image-url' });

		const organisationLogo = shallowMount(OrganisationLogo, { propsData: { ...defaultProps } });

		expect(organisationLogo.findComponent(Logo).exists()).to.be.true;
	});

	it('should  show logo if logo cant be updated but image is set', () => {
		Composer.mockView('OrganisationLogoController', { ...baseView, canUpdateLogo: false, imageSrc: 'image-url' });

		const organisationLogo = shallowMount(OrganisationLogo, { propsData: { ...defaultProps } });

		expect(organisationLogo.findComponent(Logo).exists()).to.be.true;
	});
});
