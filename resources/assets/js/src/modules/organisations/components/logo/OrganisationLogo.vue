<template>
	<div v-if="canUpdateLogo || imageSrc" class="image-upload-container">
		<Logo :image-src="imageSrc" @click="openModal"></Logo>
		<modal
			:value="showModal"
			:header="false"
			:footer="true"
			:confirm-on-enter="false"
			:close-on-confirm="false"
			:header-close-button="false"
			:prevent-auto-uploading="false"
			:close-button-label="lang.get('buttons.cancel')"
			:confirm-button-label="lang.get('buttons.done')"
			:confirm-disabled="isUploading"
			@confirmed="confirmLogo"
			@closed="closeModal"
		>
			<close-icon slot="before-content" @click.prevent.stop="toggle"></close-icon>
			<h4 class="modal-title">
				{{ organisation.name }} <span class="text-lowercase">{{ lang.get('organisations.titles.logo') }}</span>
			</h4>
			<logo-uploader
				v-if="showModal"
				ref="logoUploaderComponent"
				:current-image="organisationLogo"
				:upload-options="uploadOptions"
				:organisation="organisation"
				@fileUpdated="fileUploaded"
				@fileUploading="fileUploading"
			></logo-uploader>
		</modal>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import CloseIcon from '@/lib/components/ListActions/Partials/CloseIcon.vue';
import { Modal } from 'vue-bootstrap';
import Logo from './Logo.vue';
import LogoUploader from '@/modules/organisations/components/logo/LogoUploader.vue';
import { useController } from '@/domain/services/Composer';
import { organisationLogoController, View } from '@/modules/organisations/components/logo/OrganisationLogo.controller';

export default defineComponent({
	components: { LogoUploader, CloseIcon, Modal, Logo },
	props: {
		organisation: {
			type: Object,
			required: true,
		},
		uploadOptions: {
			type: Object,
			required: false,
			default: null,
		},
	},
	setup: useController(organisationLogoController, 'OrganisationLogoController') as () => View,
});
</script>

<style scoped>
.image-upload-container {
	position: relative;
	margin-right: 20px;
	margin-top: 30px;
}
</style>
