import { Consumer } from '@/domain/models/Consumer';
import { Organisation } from '@/modules/organisations/models/Organisation';
import { UploadOptions } from '@/modules/users/components/AvatarField.controller';
import { User } from '@/domain/models/User';
import { vueData } from '@/domain/services/VueData';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
	organisationLogoController,
	OrganisationLogoProps,
} from '@/modules/organisations/components/logo/OrganisationLogo.controller';
import { ref, SetupContext } from 'vue';

const defaultProps: OrganisationLogoProps = {
	organisation: {
		id: 'org-123',
		administrator: { slug: 'slug' } as unknown as User,
		logo: {
			id: 321,
			image: 'image-url',
		},
	} as unknown as Organisation,
	uploadOptions: { routes: {} } as unknown as UploadOptions,
};

vi.mock('@/services/global/modal.interface', () => ({
	useModal: vi.fn(() => ({
		showModal: ref(false),
		toggle: vi.fn(),
	})),
}));

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		variables: {
			uploader: { routes: {} },
		},
		consumer: {
			isManager: false,
			slug: 'SomeSlug',
		},
	},
}));

vi.mock('@/domain/models/File', () => ({
	createFile: vi.fn((input) => input),
	File: {},
}));

vi.mock('@/domain/dao/Translations', () => ({
	trans: () => ({ t: (key: string) => key }),
}));

const ctx = { emit: vi.fn() } as unknown as SetupContext;

describe('organisationLogoController', () => {
	let controller: ReturnType<typeof organisationLogoController>;

	beforeEach(() => {
		vi.clearAllMocks();
	});

	it('initializes with correct default values', () => {
		controller = organisationLogoController(defaultProps, ctx);
		expect(controller.organisation.id).toBe('org-123');
		expect(controller.imageSrc.value).toBe('image-url');
		expect(controller.organisationLogo.value.id).toBe(321);
	});

	it('calls toggle when confirmLogo is called without changes', () => {
		controller = organisationLogoController(defaultProps, ctx);
		controller.confirmLogo();
		expect(controller.toggle).toHaveBeenCalled();
	});

	it('closes modal if showModal is true', () => {
		controller = organisationLogoController(defaultProps, ctx);
		controller.showModal.value = true;
		controller.closeModal();
		expect(controller.toggle).toHaveBeenCalled();
	});

	it('should not be able to open logo modal if is not a manager or administrator', () => {
		vi.spyOn(vueData, 'consumer', 'get').mockReturnValue({
			isManager: false,
			slug: 'SomeSlug',
		} as Consumer);

		const controller = organisationLogoController(defaultProps, ctx);
		controller.openModal();
		expect(controller.toggle).toHaveBeenCalledTimes(0);
	});

	it('should open modal when is manager', () => {
		vi.spyOn(vueData, 'consumer', 'get').mockReturnValue({
			isManager: true,
			slug: 'SomeSlug',
		} as Consumer);
		const controller = organisationLogoController(defaultProps, ctx);
		controller.openModal();
		expect(controller.toggle).toHaveBeenCalled();
	});

	it('should not open modal when is not manager', () => {
		vi.spyOn(vueData, 'consumer', 'get').mockReturnValue({
			isManager: false,
			slug: 'SomeSlug',
		} as Consumer);

		const controller = organisationLogoController(defaultProps, ctx);
		controller.openModal();
		expect(controller.toggle).toHaveBeenCalledTimes(0);
	});

	it('should not open modal when is manager but not UplaodOptions exists', () => {
		vi.spyOn(vueData, 'consumer', 'get').mockReturnValue({
			isManager: true,
			slug: 'SomeSlug',
		} as Consumer);
		const controller = organisationLogoController({ ...defaultProps, uploadOptions: null }, ctx);
		controller.openModal();
		expect(controller.toggle).toHaveBeenCalledTimes(0);
	});
});
