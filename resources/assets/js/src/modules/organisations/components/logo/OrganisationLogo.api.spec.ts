import { UploadedFile } from '@/lib/types/UploadedFile';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import { DataRequestMethod, DataResponse, dataSource } from '@/domain/services/Api/DataSource';
import { FileUpdateRequest, useApi } from '@/modules/organisations/components/logo/OrganisationLogo.api';

vi.mock('@/domain/services/Api/Headers', async () => ({
	headersFactory: () => ({}),
}));
vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		routes: {
			'organisation.logo.delete': '{organisation}/logo',
			'organisation.logo.update': '{organisation}/logo',
		},
	},
}));

const api = useApi({
	upload: '/upload',
	status: '/status/',
});

describe('useApi', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	it('constructs upload endpoint and calls it', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-file-id' } as DataResponse<unknown>);
		const response = await api.upload({ foo: 'bar' } as unknown as UploadedFile);

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/upload',
			method: DataRequestMethod.POST,
			data: { foo: 'bar' },
			headers: {},
		});

		expect(response).toBe('test-file-id');
	});

	it('calls status endpoint with fileId', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-file-id' } as DataResponse<unknown>);

		await api.status(42);

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/status/42',
			method: DataRequestMethod.GET,
			headers: {},
		});
	});

	it('calls deleteLogo', async () => {
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-file-id' } as DataResponse<unknown>);

		await api.deleteLogo('someOrg');

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/someOrg/logo',
			method: DataRequestMethod.DELETE,
			headers: {},
		});
	});

	it('calls updateLogo with organisation and payload', async () => {
		const payload = { file: 1 };
		vi.spyOn(dataSource, 'request').mockResolvedValue({ data: 'test-file-id' } as DataResponse<unknown>);

		await api.updateLogo('someOrg', payload as unknown as FileUpdateRequest);

		expect(dataSource.request).toHaveBeenCalledWith({
			url: '/someOrg/logo',
			method: DataRequestMethod.PUT,
			headers: {},
			data: payload,
		});
	});
});
