<template>
	<div :class="['image-display', { placeholder: !imageSrc }]" @click="$emit('click')">
		<img v-if="imageSrc" :src="imageSrc" alt="Uploaded Logo" class="display-image" />
		<div v-else class="placeholder-content">
			<button class="btn btn-link" type="button" @click.prevent>
				{{ lang.get('organisations.form.logo.buttons.upload') }}
			</button>
		</div>
	</div>
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { trans } from '@/domain/dao/Translations';

export default defineComponent({
	props: {
		imageSrc: {
			type: String as PropType<string | null>,
			default: null,
		},
	},
	setup() {
		return {
			lang: trans(),
		};
	},
});
</script>

<style scoped>
.image-display {
	width: 80px;
	height: 80px;
	cursor: pointer;
	overflow: hidden;
	display: flex;
	align-items: center;
	justify-content: center;

	&.placeholder {
		max-height: 80px;
		border: 2px dashed #6f6f6f;
		&:focus-within {
			border: 2px solid #253342;
		}
	}

	.display-image {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}

	.placeholder-content {
		color: #6c757d;
		text-align: center;
		font-size: 14px;
	}
	.placeholder-content button {
		background-color: transparent;
		border: none;
		margin: 0;
	}
}

.btn.btn-link:focus {
	outline: none;
	box-shadow: none;
}
</style>
