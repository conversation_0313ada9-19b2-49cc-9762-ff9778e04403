import { LogoFileProps } from '@/modules/organisations/components/logo/LogoUploader.controller';
import LogoUploader from '@/modules/organisations/components/logo/LogoUploader.vue';
import { Organisation } from '@/modules/organisations/models/Organisation';
import { UploadOptions } from '@/modules/users/components/AvatarField.controller';
import { useModal } from '@/services/global/modal.interface';
import { vueData } from '@/domain/services/VueData';
import { createFile, File } from '@/domain/models/File';
import { Ref, ref, SetupFunction } from 'vue';
import { Trans, trans } from '@/domain/dao/Translations';

type View = {
	lang: Trans;
	showModal: Ref<boolean>;
	toggle: () => void;
	imageSrc: Ref<string | null>;
	file: Ref<File | null>;
	confirmLogo: () => void;
	organisationLogo: Ref<LogoFileProps | null>;
	fileUploaded: (file: LogoFileProps | null) => void;
	organisation: Organisation;
	closeModal: () => void;
	openModal: () => void;
	logoUploaderComponent: Ref<InstanceType<typeof LogoUploader> | null>;
	canUpdateLogo: boolean;
	isUploading: Ref<boolean>;
	fileUploading: (uploading: boolean) => void;
};

type OrganisationLogoProps = {
	organisation: Organisation;
	uploadOptions: UploadOptions | null;
};
const organisationLogoController: SetupFunction<OrganisationLogoProps, View> = (
	props: OrganisationLogoProps,
	{ emit }
): View => {
	const logoUploaderComponent: Ref<InstanceType<typeof LogoUploader> | null> = ref(null);
	const isUploading = ref(false);
	const { showModal, toggle } = useModal('OrgLogoUpload', emit);
	const uploadedFile: Ref<LogoFileProps | null> = ref(null);
	const imageChanged = ref(false);
	const organisation = props.organisation;
	const canUpdateLogo = Boolean(
		(vueData.consumer.isManager || organisation.administrator?.slug === vueData.consumer.slug) && props.uploadOptions
	);
	const organisationLogo: Ref<LogoFileProps | null> = ref(
		Array.isArray(props.organisation.logo) ? {} : props.organisation.logo
	) as unknown as Ref<LogoFileProps>;
	const originalFile = ref(createFile(props.organisation.logo as unknown as LogoFileProps));
	const file = ref(createFile(props.organisation.logo as unknown as File));
	const imageSrc = ref<string | null>(file.value?.image || null);
	const lang = trans();

	const updateLogo = (logo: LogoFileProps | null) => {
		organisationLogo.value = logo;
		originalFile.value.id = logo?.id ?? null;
		imageSrc.value = logo?.image ?? null;
	};

	const confirmLogo = async () => {
		if (imageChanged.value) {
			await logoUploaderComponent.value.handleLogoChange();
			updateLogo(uploadedFile.value);
		}

		toggle();
	};

	const fileUploaded = (file: LogoFileProps | null) => {
		uploadedFile.value = file;
		imageChanged.value = true;
	};

	const closeModal = () => {
		if (showModal.value) {
			toggle();
		}
	};

	const openModal = () => {
		if (canUpdateLogo) {
			toggle();
		}
	};

	const fileUploading = (uploading: boolean) => {
		isUploading.value = uploading;
	};

	return {
		lang,
		showModal,
		toggle,
		imageSrc,
		file,
		confirmLogo,
		organisationLogo,
		fileUploaded,
		organisation,
		closeModal,
		openModal,
		logoUploaderComponent,
		canUpdateLogo,
		isUploading,
		fileUploading,
	};
};

export { organisationLogoController, View, OrganisationLogoProps };
