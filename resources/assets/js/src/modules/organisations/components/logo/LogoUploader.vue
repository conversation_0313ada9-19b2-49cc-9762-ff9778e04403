<template>
	<div>
		<button v-show="false" ref="button" type="button" @click="() => {}"></button>
		<div class="upload-details-container">
			<div v-if="isUploading" class="upload-details card-header">
				<file-upload-status :file="uploadedFile" />
				<file-upload-actions v-if="failed" :file="uploadedFile" :field-required="$attrs['field-required']">
				</file-upload-actions>
			</div>
		</div>
		<file-selector
			v-if="showUploader"
			:is-uploading="isUploading"
			:show-file-dialog="logoReplaced"
			@fileSelected="onDrop"
		></file-selector>
		<div v-if="(showPreview || isUploading) && hasExistingImage" class="logoPreview">
			<img :src="imageSrc" />
			<div v-if="!isUploading" class="logoActions">
				<button class="action-button" type="button" @click="replaceLogo">
					{{ lang.get('organisations.form.logo.buttons.replace') }}
				</button>
				<button class="action-button" type="button" @click="removeLogo">
					{{ lang.get('organisations.form.logo.buttons.remove') }}
				</button>
			</div>
		</div>
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import FileUploadStatus from '@/lib/components/Uploader/FileUploadStatus.vue';
import FileUploadActions from '@/lib/components/Uploader/FileUploadActions.vue';
import FileSelector from '@/lib/components/Uploader/FileSelector/FileSelector.vue';
import { useController } from '@/domain/services/Composer';
import { logoUploaderController, View } from '@/modules/organisations/components/logo/LogoUploader.controller';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';

export default defineComponent({
	components: { FileSelector, FileUploadActions, FileUploadStatus },
	mixins: [langMixin],
	props: {
		currentImage: {
			type: Object,
			default: () => ({}),
		},
		organisation: {
			type: Object,
			default: () => ({}),
		},
		uploadOptions: {
			type: Object,
			default: () => ({}),
		},
	},
	setup: useController(logoUploaderController, 'LogoUploaderController') as () => View,
});
</script>

<style scoped>
.logoActions {
	display: flex;
	white-space: nowrap;
	margin-top: 20px;
}
.logoPreview {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: flex-start;
	flex-direction: column;

	img {
		max-width: 100%;
		width: 240px;
	}
}
</style>
