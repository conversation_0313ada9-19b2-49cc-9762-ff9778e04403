import { EventEmitters } from '@/domain/utils/Events';
import { LogoFileProps } from '@/modules/organisations/components/logo/LogoUploader.controller';

enum LogoEvents {
	Updated = 'fileUpdated',
	Uploading = 'fileUploading',
}

type LogoEmitters = EventEmitters<{
	[LogoEvents.Updated]: (fileId: LogoFileProps | null) => void;
	[LogoEvents.Uploading]: (uploading: boolean) => void;
}>;

export { LogoEvents, LogoEmitters };
