import { getImageFromFile } from '@/lib/file-helpers';
import { Organisation } from '@/modules/organisations/models/Organisation';
import { set } from 'lodash';
import { Uploader } from '@/domain/services/Uploader/Plupload';
import { UploadOptions } from '@/modules/users/components/AvatarField.controller';
import { useApi } from '@/modules/organisations/components/logo/OrganisationLogo.api';
import { useContainer } from '@/domain/services/Container';
import { VueHooks } from '@/domain/services/VueHooks';
import { Chunk, FileUpload, UploaderService, useUploader } from '@/domain/services/Uploader/Uploader';
import { computed, ComputedRef, Ref, ref, SetupFunction } from 'vue';
import { createFile, FileProps, Status } from '@/domain/models/File';
import { createUploadedFile, UploadedFile, UploadedFileProps, UploaderStatus } from '@/lib/types/UploadedFile';
import { LogoEmitters, LogoEvents } from '@/modules/organisations/components/logo/LogoEmitters';
import { Timer, useTimer } from '@/domain/utils/Timer';
import { trans, Trans } from '@/domain/dao/Translations';

type LogoFileProps = FileProps & { imageLarge: string | undefined };

type Props = {
	currentImage: LogoFileProps;
	uploadOptions: UploadOptions;
	organisation: Organisation;
};

type View = {
	lang: Trans;
	isUploading: Ref<boolean>;
	uploadedFile: Ref<UploadedFile | null>;
	failed: ComputedRef<boolean>;
	showUploader: Ref<boolean>;
	imageSrc: Ref<string | null>;
	showPreview: Ref<boolean>;
	hasExistingImage: ComputedRef<boolean>;
	button: Ref<HTMLButtonElement | null>;
	onDrop: (file: File) => void;
	removeLogo: () => void;
	replaceLogo: () => void;
	logoReplaced: Ref<boolean>;
	handleLogoChange: () => void;
	uploadedFileId: Ref<number | null>;
	setStatus: (status: UploaderStatus, statusMessage?: string | null) => void;
};

type Ctx = {
	emit: LogoEmitters;
};
const logoUploaderController: SetupFunction<Props, View> = (props: Props, { emit }: Ctx): View => {
	const { onMounted } = useContainer<VueHooks>();
	const organisationLogo = ref(props.currentImage);
	organisationLogo.value.image = organisationLogo.value.imageLarge;
	const uploader: UploadOptions = props.uploadOptions as UploadOptions;
	const api = useApi(uploader.routes);
	const imageChanged = ref(false);
	const organisation = props.organisation;
	const button: Ref<HTMLButtonElement | null> = ref(null);
	const onDrop = (file: File) => processFileInput(file);
	const lang = trans();
	const isUploading = ref(false);
	const uploadedFile: Ref<UploadedFile | null> = ref(null);
	const uploadedFileId = ref<number | null>(null);
	const failed = computed(() => uploadedFile.value?.status === UploaderStatus.FAILED);
	const file = ref(createFile(organisationLogo.value));
	const imageSrc = ref<string | null>(file.value?.image || null);
	const hasExistingImage = computed(() => imageSrc.value !== null);
	const showPreview = ref(hasExistingImage.value);
	const showUploader = ref(!hasExistingImage.value);
	let refreshUploadedImage: Timer | null = null;

	const processFileInput = async (file: File) => {
		await loadFilePreview(file);
		uploaderService.upload(file);
	};

	const loadFilePreview = async (previewFile: File) => {
		const image = await getImageFromFile(previewFile);
		if (image) {
			const newFile: UploadedFileProps = {
				attachmentId: null,
				file: null,
				foreignId: uploader.foreignId,
				id: null,
				image: null,
				loaded: 0,
				mime: previewFile.type,
				name: previewFile.name ?? null,
				oldId: null,
				original: previewFile.name ?? null,
				percent: 0,
				remoteId: null,
				resource: uploader.resource,
				resourceId: uploader.resourceId,
				size: previewFile.size,
				slug: null,
				source: null,
				status: UploaderStatus.QUEUED,
				statusMessage: null,
				tabId: uploader.tabId,
				token: null,
				transcodingErrors: [],
				transcodingStatus: null,
				url: null,
			};
			uploadedFile.value = createUploadedFile(newFile);
			imageSrc.value = image.src;
		}

		showUploader.value = false;
		showPreview.value = false;
	};

	const onBeforeUpload = (uploader: Uploader, fileUploadBefore: FileUpload) => {
		if (uploader) {
			uploadedFileId.value = null;
			const fileName = uploadedFile.value?.name;
			uploader.settings.multipart_params.key = fileName;
			uploader.settings.multipart_params.Filename = fileName;
			uploader.settings.multipart_params['Content-Type'] = fileUploadBefore.type;
		}

		setStatus(UploaderStatus.UPLOADING);
	};

	const onBeforeChunkUpload = (uploader: Uploader, beforeChunkFile: FileUpload, args: Chunk) => {
		const chunk: number = args.chunk;

		if (chunk !== 0) {
			const name = uploadedFile.value?.name;

			if (uploader) {
				uploader.settings.multipart_params.key = name + '-' + chunk;
			}
		}
	};

	const onUploadProgress = (uploader: Uploader, uploadingFile: FileUpload) => {
		setFileProperty('loaded', uploadingFile.loaded);
		setFileProperty('percent', uploadingFile.percent);
	};

	const setFileProperty = (property: keyof UploadedFileProps, value: unknown) => {
		if (uploadedFile.value) {
			set(uploadedFile.value, property, value);
		}
	};

	/* eslint-disable-next-line no-unused-vars */
	const onFileUploaded = (uploader: Uploader, f: UploadedFile) => {
		const file = uploadedFile.value;
		if (file === null) {
			return;
		}

		setStatus(UploaderStatus.PROCESSING);

		api.upload(file).then(
			(response) => {
				setFileProperty('token', response.token);
				setFileProperty('remoteId', response.file);

				refreshUploadedImage = useTimer(() => {
					// eslint-disable-next-line chai-friendly/no-unused-expressions
					uploadedFile.value?.remoteId ? checkProcessingStatus() : setStatus(UploaderStatus.FAILED);

					return true;
				}, 30000);
				refreshUploadedImage.start();
			},
			() => {
				setStatus(UploaderStatus.FAILED);
			}
		);
	};

	const checkProcessingStatus = () => {
		const processingFile = uploadedFile?.value;
		if (!processingFile || processingFile.remoteId === null) {
			return;
		}

		api
			.status(processingFile.remoteId)
			.then((response) => {
				const processingStatus = response.status;
				// Skip if the processing status is still 'pending'
				if ([Status.STATUS_OK, Status.STATUS_REJECTED].includes(processingStatus)) {
					processFile(
						response.id,
						processingStatus,
						response.attachmentId,
						response.statusMessage,
						response.fileUrl,
						response.imageUrl,
						response.source
					);
				}
			})
			.catch(() => {
				processFile(file.value?.id, Status.STATUS_REJECTED, null, null, null, null, null);
			});
	};

	const processFile = async (
		remoteId: number | null,
		processingStatus: string,
		attachmentId: number | null = null,
		statusMessage: string | null = null,
		url: string | null = null,
		imageUrl: string | null = null,
		source: string | null = null
	) => {
		const processFile = uploadedFile.value;

		if (!processFile || processFile.status !== UploaderStatus.PROCESSING) {
			return;
		}

		const status = processingStatus === Status.STATUS_OK ? UploaderStatus.COMPLETED : UploaderStatus.FAILED;

		setStatus(status, statusMessage);
		setFileProperty('url', url);
		setFileProperty('image', imageUrl);
		setFileProperty('source', source);

		refreshUploadedImage?.stop();

		if (status === UploaderStatus.COMPLETED) {
			setFileProperty('oldId', processFile.id);
			setFileProperty('id', processFile.remoteId);
			setFileProperty('attachmentId', attachmentId);
			file.value.image = imageUrl ?? undefined;

			if (remoteId) {
				await completeUpload(remoteId);
				const logoFile: LogoFileProps = { ...file.value, imageLarge: file.value.image };
				emit(LogoEvents.Updated, logoFile);
				imageChanged.value = true;
			}
		} else {
			imageSrc.value = null;
			uploadedFile.value = null;
			showUploader.value = true;
			showPreview.value = false;
		}
	};

	const setStatus = (status: UploaderStatus, statusMessage: string | null = null) => {
		setFileProperty('status', status);

		if (statusMessage) {
			setFileProperty('statusMessage', statusMessage);
		}

		isUploading.value = [UploaderStatus.QUEUED, UploaderStatus.UPLOADING, UploaderStatus.PROCESSING].includes(status);
		emit(LogoEvents.Uploading, isUploading.value);
	};

	const completeUpload = (remoteId: number) => {
		uploadedFileId.value = remoteId;
		file.value.id = remoteId;
		uploadedFile.value = null;
		showUploader.value = false;
		showPreview.value = true;
	};

	let uploaderService: UploaderService;
	onMounted(() => {
		uploaderService = useUploader(uploader.s3, button.value, {
			BeforeUpload: onBeforeUpload,
			BeforeChunkUpload: onBeforeChunkUpload,
			UploadProgress: onUploadProgress,
			FileUploaded: onFileUploaded,
		});
	});

	const logoReplaced = ref(false);
	const replaceLogo = () => {
		logoReplaced.value = true;
		showPreview.value = false;
		showUploader.value = true;
		uploadedFile.value = null;
		imageSrc.value = null;
	};

	const removeLogo = () => {
		if (!file.value.id) {
			return;
		}

		emit(LogoEvents.Updated, null);
		imageChanged.value = true;
		showPreview.value = false;
		showUploader.value = true;
		uploadedFile.value = null;
		imageSrc.value = null;
	};

	const handleLogoChange = async () => {
		if (!imageChanged.value) {
			return;
		}

		const hasOriginal = organisationLogo.value?.id != null;
		const hasUploaded = uploadedFileId.value != null;

		if (hasOriginal && !hasUploaded) {
			await api.deleteLogo({ organisation: organisation.id });
		} else {
			await api.updateLogo(organisation.id, { file: uploadedFileId.value as number });
		}
	};

	return {
		lang,
		isUploading,
		uploadedFile,
		failed,
		showUploader,
		imageSrc,
		showPreview,
		hasExistingImage,
		button,
		onDrop,
		removeLogo: removeLogo,
		replaceLogo: replaceLogo,
		logoReplaced,
		handleLogoChange,
		uploadedFileId,
		setStatus,
	};
};

export { Props, View, logoUploaderController, LogoFileProps };
