import { LogoEmitters } from '@/modules/organisations/components/logo/LogoEmitters';
import { Organisation } from '@/modules/organisations/models/Organisation';
import { SetupContext } from 'vue';
import { UploaderStatus } from '@/lib/types/UploadedFile';
import { UploadOptions } from '@/modules/users/components/AvatarField.controller';
import { useContainer } from '@/domain/services/Container';
import { useController } from '@/domain/services/Composer';
import { User } from '@/domain/models/User';
import { beforeEach, describe, expect, it, vi } from 'vitest';
import {
	LogoFileProps,
	logoUploaderController,
	Props,
} from '@/modules/organisations/components/logo/LogoUploader.controller';

vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

const mockApi = {
	upload: vi.fn().mockResolvedValue({ token: 'abc', file: 123 }),
	status: vi.fn().mockResolvedValue({
		status: 'ok',
		id: 123,
		attachmentId: 10,
		statusMessage: null,
		fileUrl: 'file.jpg',
		imageUrl: 'image.jpg',
		source: 'source.jpg',
	}),
	deleteLogo: vi.fn().mockResolvedValue(undefined),
	updateLogo: vi.fn().mockResolvedValue(undefined),
};

vi.mock('@/modules/organisations/components/logo/OrganisationLogo.api', () => ({
	useApi: () => mockApi,
}));

vi.mock('@/domain/services/Uploader/Uploader', () => ({
	useUploader: () => ({ upload: vi.fn() }),
}));

vi.mock('@/lib/file-helpers', () => ({
	getImageFromFile: vi.fn(() => Promise.resolve({ src: 'data:image/fake' } as HTMLImageElement)),
}));

describe('logoUploaderController', () => {
	let props: Props;
	let emit: { emit: LogoEmitters };

	beforeEach(() => {
		props = {
			currentImage: {
				id: 1,
				image: 'test.jpg',
				imageLarge: 'test-large.jpg',
			} as LogoFileProps,
			organisation: {
				id: 'org-123',
				administrator: { slug: 'slug' } as unknown as User,
				logo: {
					id: 321,
					image: 'image-url',
				},
			} as unknown as Organisation,
			uploadOptions: {
				foreignId: 1,
				resource: 'org',
				resourceId: 2,
				tabId: 'abc123',
				routes: {
					upload: '/upload',
					status: '/status',
				},
				s3: {},
			} as unknown as UploadOptions,
		};
		emit = vi.fn();
		vi.resetAllMocks();
		(useContainer as Mock).mockReturnValue({
			onMounted: vi.fn(async (callback: () => Promise<void>) => {
				await callback();
			}),
		});
	});

	it('initializes correctly when organisation has image', () => {
		const controller = logoUploaderController(props, { emit });

		expect(controller.imageSrc.value).toBe('test-large.jpg');
		expect(controller.hasExistingImage.value).toBe(true);
		expect(controller.showPreview.value).toBe(true);
		expect(controller.showUploader.value).toBe(false);
	});

	it('initializes correctly when organisation does not have image', () => {
		props.currentImage = {} as unknown as LogoFileProps;
		const controller = logoUploaderController(props, { emit });

		expect(controller.imageSrc.value).toBe(null);
		expect(controller.hasExistingImage.value).toBe(false);
		expect(controller.showPreview.value).toBe(false);
		expect(controller.showUploader.value).toBe(true);
	});

	it('calls replacePhoto and updates state', () => {
		const controller = logoUploaderController(props, { emit });

		controller.replaceLogo();

		expect(controller.logoReplaced.value).toBe(true);
		expect(controller.showUploader.value).toBe(true);
		expect(controller.showPreview.value).toBe(false);
		expect(controller.uploadedFile.value).toBe(null);
	});

	it('clears photo when removePhoto is called', () => {
		const mockEmit = vi.fn();
		const controller = logoUploaderController(props, { emit: mockEmit } as unknown as SetupContext);

		controller.removeLogo();
		expect(controller.imageSrc.value).toBeNull();
		expect(controller.showUploader.value).toBe(true);
		expect(mockEmit).toHaveBeenCalledWith('fileUpdated', null);
	});

	it('should not show uploader after file drop', async () => {
		const mockEmit = vi.fn();
		const setup = useController(logoUploaderController, 'TestView');
		const view = setup({ ...props, currentImage: { id: 1, imageLarge: null } as unknown as LogoFileProps }, {
			emit: mockEmit,
		} as unknown as SetupContext);
		const file = new File(['test'], 'logo.png', { type: 'image/png' });

		expect(view.showUploader.value).toBe(true);

		await view.onDrop(file);

		expect(view.showUploader.value).toBe(false);
	});

	it('calls deleteLogo when original exists and no uploaded file', async () => {
		const view = logoUploaderController(props, {
			emit: emit,
		} as unknown as SetupContext);

		view.removeLogo();
		await view.handleLogoChange();

		expect(mockApi.deleteLogo).toHaveBeenCalledWith({ organisation: 'org-123' });
	});

	it('calls updateLogo with current logo', async () => {
		const view = logoUploaderController(props, {
			emit: emit,
		} as unknown as SetupContext);

		view.removeLogo();
		view.uploadedFileId.value = 321;
		await view.handleLogoChange();

		expect(mockApi.updateLogo).toHaveBeenCalledWith('org-123', { file: 321 });
	});

	it('emits fileUploading with false payload when status is completed', () => {
		const mockEmit = vi.fn();
		const controller = logoUploaderController(props, { emit: mockEmit } as unknown as SetupContext);
		controller.setStatus(UploaderStatus.COMPLETED);

		expect(mockEmit).toHaveBeenCalledWith('fileUploading', false);
	});

	it('emits fileUploading with true payload when status is processing', () => {
		const mockEmit = vi.fn();
		const controller = logoUploaderController(props, { emit: mockEmit } as unknown as SetupContext);
		controller.setStatus(UploaderStatus.PROCESSING);

		expect(mockEmit).toHaveBeenCalledWith('fileUploading', true);
	});
});
