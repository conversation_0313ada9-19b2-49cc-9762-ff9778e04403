import { Organisation } from '@/modules/organisations/models/Organisation';
import { computed, ComputedRef, Ref, ref, SetupFunction } from 'vue';
import {
	OrganisationSelectorEmitters,
	OrganisationSelectorEvents,
} from '@/modules/organisations/components/OrganisationSelector.events';
import { Trans, trans } from '@/domain/dao/Translations';

type View = {
	lang: Trans;
	selectedOrganisations: Ref<string[]>;
	availableOrganisations: ComputedRef<Organisation[]>;
	searchKeywords: Ref<string>;
	searchResults: Ref<Organisation[]>;
	search: (organisations: []) => void;
	select: (organisationIDs: string[]) => void;
	reset: () => void;
};

type Props = never;

const organisationSelectorController: SetupFunction<Props, View, OrganisationSelectorEmitters> = (
	props,
	{ emit }
): View => {
	const selectedOrganisations = ref<string[]>([]); // Always starts as an empty array
	const searchKeywords = ref('');
	const searchResults = ref<Organisation[]>([]);

	const availableOrganisations = computed(() => searchResults.value);

	const search = (organisations: []) => {
		searchResults.value = organisations;
	};

	const reset = () => {
		searchKeywords.value = '';
		searchResults.value = [];
		selectedOrganisations.value = [];
		emit(OrganisationSelectorEvents.Selected, []);
	};

	const select = (organisationIDs: string[]) => {
		selectedOrganisations.value = organisationIDs.map((organisationID) => organisationID);
		emit(OrganisationSelectorEvents.Selected, selectedOrganisations.value);
	};

	return {
		lang: trans(),
		selectedOrganisations,
		availableOrganisations,
		searchKeywords,
		searchResults,
		search,
		select,
		reset,
	};
};

export { View, organisationSelectorController };
