import { DateTime } from '@/domain/utils/Types';
import { LogoFileProps } from '@/modules/organisations/components/logo/LogoUploader.controller';
import { Season } from '@/modules/seasons/models/Season';
import { User } from '@/domain/models/User';

enum OrganisationCreatedBy {
	Api = 'api',
	Import = 'import',
	System = 'system',
	User = 'user',
}

type Organisation = {
	id: string;
	name: string;
	administrator: User | null;
	domains: string[];
	createdBy: OrganisationCreatedBy | null;
	createdByUser: User | null;
	createdAt: DateTime;
	updatedAt: DateTime;
	season: Season | null;
	logo: LogoFileProps;
};

export { Organisation, OrganisationCreatedBy };
