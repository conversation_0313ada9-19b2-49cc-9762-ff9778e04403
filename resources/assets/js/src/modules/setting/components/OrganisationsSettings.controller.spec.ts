import { updateOrganisationsSettings } from '@/modules/setting/components/Organisations.api';
import { useContainer } from '@/domain/services/Container';
import { beforeEach, describe, expect, Mock, vi } from 'vitest';
import { organisationsSettingsController, View } from '@/modules/setting/components/OrganisationsSettings.controller';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
		variables: {
			labels: {
				organisations: 'Organisations',
			},
		},
	},
}));

vi.mock('@/domain/services/Container', () => ({
	useContainer: vi.fn(),
}));

vi.mock('@/modules/setting/components/Organisations.api', () => ({
	updateOrganisationsSettings: vi.fn(),
}));

const defaultProps = {
	settings: {
		enabled: false,
		joinOnRegisteredEmail: false,
		userSelect: false,
		userAddNew: false,
		administratorInvite: false,
		managerAddUser: false,
	},
};

describe('OrganisationsSettings controller', () => {
	beforeEach(() => {
		vi.resetAllMocks();
		vi.clearAllMocks();
		(useContainer as Mock).mockReturnValue({ onMounted: vi.fn() });
	});

	it('should initialize with default settings', () => {
		const view = organisationsSettingsController(defaultProps) as View;

		expect(view.isEnabled.value).toBe(false);
		expect(view.selectedSettings.value).toEqual({
			joinOnRegisteredEmail: false,
			userSelect: false,
			userAddNew: false,
			administratorInvite: false,
			managerAddUser: false,
		});
	});

	it('should toggle organisation setting', async () => {
		const view = organisationsSettingsController(defaultProps) as View;

		await view.toggleOrganisation();

		expect(updateOrganisationsSettings).toHaveBeenCalledWith({
			enabled: true,
			joinOnRegisteredEmail: false,
			userSelect: false,
			userAddNew: false,
			administratorInvite: false,
			managerAddUser: false,
		});
		expect(view.isEnabled.value).toBe(true);
	});

	it('should update settings', async () => {
		const reload = vi.fn();
		vi.stubGlobal('window', {
			location: { reload },
		});

		const view = organisationsSettingsController(defaultProps) as View;

		view.selectedSettings.value.enabled = true;
		view.selectedSettings.value.userSelect = true;

		await view.updateSettings();

		expect(updateOrganisationsSettings).toHaveBeenCalledWith({
			enabled: true,
			joinOnRegisteredEmail: false,
			userSelect: true,
			userAddNew: false,
			administratorInvite: false,
			managerAddUser: false,
		});
	});
});
