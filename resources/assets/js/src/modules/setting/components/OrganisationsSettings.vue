<template>
	<div>
		<div class="row island">
			<div class="col-xs-12">
				<div class="title title-organisations selector-title">
					<div class="mrx">
						<h1>{{ lang.get('setting.tabs.organisations') }}</h1>
						<div class="onoffswitch-container">
							<toggle-switch
								id="organisations-enabled"
								class="inline-block"
								name="enabled"
								:on-label="lang.get('buttons.on')"
								:off-label="lang.get('buttons.off')"
								:use-inner-state="true"
								:checked="!!isEnabled"
								:aria-label="lang.get('setting.tabs.organisations')"
								@change="toggleOrganisation"
							/>
						</div>
					</div>
				</div>
			</div>
		</div>
		<form v-show="isEnabled" @submit.stop.prevent="updateSettings">
			<div class="row">
				<div class="col-xs-12 col-md-6">
					<div class="panel panel-default">
						<div class="panel-body">
							<div class="panel-title">
								<h4>{{ lang.get('setting.form.organisations.panel.title') }}</h4>
							</div>
							<div class="form-settings">
								<p>{{ lang.get('setting.form.organisations.panel.description') }}</p>
								<input type="hidden" :value="isEnabled" name="enabled" />
								<div v-for="(setting, key) in selectedSettings" :key="key" class="form-group">
									<div class="checkbox styled">
										<input
											:id="key"
											v-model="selectedSettings[key]"
											:name="`setting['organisations'][${key}]`"
											value="1"
											type="checkbox"
											:checked="setting"
										/>
										<label :for="key">
											{{ lang.get(`setting.form.organisations.${key}.label`) }}
										</label>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="form-actions">
						<button type="submit" class="btn btn-lg btn-primary">{{ lang.get('buttons.save') }}</button>
						<a href="javascript:window.history.back()" class="btn btn-tertiary btn-lg">
							{{ lang.get('buttons.cancel') }}
						</a>
					</div>
				</div>
			</div>
		</form>
	</div>
</template>

<script lang="ts">
import ToggleSwitch from '@/lib/components/Shared/ToggleSwitch.vue';
import { defineComponent, PropType } from 'vue';
import { useController } from '@/domain/services/Composer';
import {
	organisationsSettingsController,
	Props,
	View,
} from '@/modules/setting/components/OrganisationsSettings.controller';
import { Setting } from '@/modules/setting/Settings.types';

export default defineComponent<Props, View>({
	components: {
		ToggleSwitch,
	},
	props: {
		settings: {
			type: Object as PropType<Setting[]>,
			default: () => ({}),
		},
	},

	setup: useController(organisationsSettingsController, 'OrganisationsSettingsController') as () => View,
});
</script>

<style scoped>
.title-organisations {
	h1 {
		display: inline-block;
	}

	.onoffswitch-container {
		display: inline-block;
		vertical-align: middle;
		margin-left: 5px;
	}
}
</style>
