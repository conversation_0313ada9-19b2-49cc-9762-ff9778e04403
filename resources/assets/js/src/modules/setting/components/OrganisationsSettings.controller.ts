import { apiRequest } from '@/modules/billing/services/Api';
import { OrganisationSettings } from '@/modules/setting/Settings.types';
import { updateOrganisationsSettings } from '@/modules/setting/components/Organisations.api';
import { Ref, ref, SetupFunction } from 'vue';
import { Trans, trans } from '@/domain/dao/Translations';

type Props = {
	settings: OrganisationSettings;
};

type View = {
	lang: Trans;
	isEnabled: Ref<boolean>;
	selectedSettings: Ref<OrganisationSettings>;
	toggleOrganisation: () => void;
	updateSettings: () => void;
};

const organisationsSettingsController: SetupFunction<Props, View> = (props): View => {
	const isEnabled = ref(props.settings.enabled ?? false);
	delete props.settings.enabled;
	const selectedSettings = ref(props.settings);

	const toggleOrganisation = async () => {
		await apiRequest(updateOrganisationsSettings({ enabled: !isEnabled.value, ...selectedSettings.value })).then(() => {
			isEnabled.value = !isEnabled.value;
		});
	};

	const updateSettings = async () => {
		await apiRequest(updateOrganisationsSettings({ enabled: isEnabled.value, ...selectedSettings.value })).then(() =>
			window.location.reload()
		);
	};

	return {
		lang: trans(),
		selectedSettings,
		isEnabled,
		toggleOrganisation,
		updateSettings,
	};
};

export { Props, View, organisationsSettingsController };
