import { OrganisationSettings } from '@/modules/setting/Settings.types';
import { PuttingEndpoint } from '@/domain/services/Api/Endpoint';
import { RoutesGetter, useRoutesDao } from '@/domain/dao/Routes';

const routes = useRoutesDao<{
	setting: {
		organisations: {
			update: () => string;
		};
	};
}>();

const updateOrganisationsSettings = (parameters: OrganisationSettings) =>
	PuttingEndpoint<OrganisationSettings, null>(routes.setting.organisations.update as RoutesGetter)()(parameters);

export { updateOrganisationsSettings };
