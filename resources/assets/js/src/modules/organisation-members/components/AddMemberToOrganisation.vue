<template>
	<div class="marker-action">
		<list-action-form
			ref="form"
			:ids="ids"
			:labels="labels"
			:route="route"
			:method="method"
			:button-class="buttonClass"
			@submitted="toggleModal"
		>
			<template v-for="(organisationId, index) in selectedOrganisations">
				<input :key="organisationId + '_' + index" type="hidden" name="organisations[]" :value="organisationId" />
			</template>
			<portal :to="name">
				<modal
					v-model="showModal"
					:header="false"
					:confirm-button-label="lang.get('organisations.selector.action_label')"
					:close-button-label="lang.get('buttons.cancel')"
					:confirm-on-enter="false"
					@closed="closeModal"
					@confirmed="submit"
				>
					<close-icon slot="before-content" @click.prevent="closeModal"></close-icon>
					<h4>{{ lang.get('organisations.titles.add_member') }}</h4>
					<p v-if="showSelectedMembersCount">
						{{ lang.get('organisations.selector.members_selected', { count: ids.length }) }}
					</p>
					<organisation-selector @selected="selectOrganisations"></organisation-selector>
				</modal>
			</portal>
		</list-action-form>
	</div>
</template>

<script lang="ts">
import {
	addMemberToOrganisationController,
	View,
} from '@/modules/organisation-members/components/AddMemberToOrganisation.controller';
import ListAction from '@/lib/components/ListActions/ListAction';
import ListActionForm from '@/lib/components/ListActions/ListActionForm.vue';
import CloseIcon from '@/lib/components/ListActions/Partials/CloseIcon.vue';
import { Modal } from 'vue-bootstrap';
import OrganisationSelector from '@/modules/organisations/components/OrganisationSelector.vue';
import { useController } from '@/domain/services/Composer';

export default {
	components: { OrganisationSelector, CloseIcon, ListActionForm, Modal },
	extends: ListAction,
	setup: useController(addMemberToOrganisationController, 'AddMemberToOrganisationController') as () => View,
};
</script>
