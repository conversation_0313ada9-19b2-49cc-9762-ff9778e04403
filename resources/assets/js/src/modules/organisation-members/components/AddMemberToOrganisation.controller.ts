import { computed, ComputedRef, nextTick, ref, Ref, SetupFunction } from 'vue';
import { ResourceListEmitters, ResourceListEvents } from '@/lib/components/ResourceList.events';
import { Trans, trans } from '@/domain/dao/Translations';

type View = {
	lang: Trans;
	selectedOrganisations: Ref<number[]>;
	name: Ref<string>;
	showModal: Ref<boolean>;
	toggleModal: () => void;
	closeModal: () => void;
	selectOrganisations: (organisations: number[]) => void;
	showSelectedMembersCount: ComputedRef<boolean>;
};

type Props = {
	ids: number[];
};

const addMemberToOrganisationController: SetupFunction<Props, View, ResourceListEmitters> = (props, { emit }): View => {
	const selectedOrganisations = ref<number[]>([]);
	const name = ref<string>('add-member-to-organisation');
	const showModal = ref(false);
	const showSelectedMembersCount = computed(() => props.ids.length > 1);

	const toggleModal = () => {
		showModal.value = !showModal.value;
		nextTick(() => emit(ResourceListEvents.Reveal, showModal.value ? name.value : ''));
	};

	const closeModal = () => {
		showModal.value = false;
		nextTick(() => emit(ResourceListEvents.Reveal, ''));
	};

	const selectOrganisations = (organisations: number[]) => {
		selectedOrganisations.value = organisations;
	};

	return {
		lang: trans(),
		selectedOrganisations,
		name,
		showModal,
		toggleModal,
		selectOrganisations,
		showSelectedMembersCount,
		closeModal,
	};
};

export { addMemberToOrganisationController, View };
