import { nextTick } from 'vue';
import { ResourceListEvents } from '@/lib/components/ResourceList.events';
import {
	addMemberToOrganisationController,
	View,
} from '@/modules/organisation-members/components/AddMemberToOrganisation.controller';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('@/domain/services/VueData', () => ({
	vueData: {
		translations: {},
	},
}));

describe('AddMemberToOrganisation controller', () => {
	let emit: (event: string, payload: string) => void;

	beforeEach(() => {
		emit = vi.fn();
	});

	it('should initialize with default values', () => {
		const view = addMemberToOrganisationController({}, { emit }) as View;

		expect(view.selectedOrganisations.value).toEqual([]);
		expect(view.name.value).toBe('add-member-to-organisation');
		expect(view.showModal.value).toBe(false);
	});

	it('should toggle modal visibility', async () => {
		const view = addMemberToOrganisationController({}, { emit }) as View;

		view.toggleModal();
		await nextTick();
		expect(view.showModal.value).toBe(true);
		expect(emit).toHaveBeenCalledWith(ResourceListEvents.Reveal, 'add-member-to-organisation');

		view.toggleModal();
		await nextTick();
		expect(view.showModal.value).toBe(false);
		expect(emit).toHaveBeenCalledWith(ResourceListEvents.Reveal, '');
	});

	it('should close modal', async () => {
		const view = addMemberToOrganisationController({}, { emit }) as View;

		view.showModal.value = true;
		view.closeModal();
		await nextTick();
		expect(view.showModal.value).toBe(false);
		expect(emit).toHaveBeenCalledWith(ResourceListEvents.Reveal, '');
	});

	it('should select organisations', () => {
		const view = addMemberToOrganisationController({}, { emit }) as View;

		view.selectOrganisations([1, 2]);
		expect(view.selectedOrganisations.value).toEqual([1, 2]);
	});

	it('should show selected members count when ids > 1', () => {
		const view = addMemberToOrganisationController({ ids: [1, 2] }, { emit }) as View;

		expect(view.showSelectedMembersCount.value).toBe(true);
	});

	it('should not show selected members count when ids = 1', () => {
		const view = addMemberToOrganisationController({ ids: [1] }, { emit }) as View;

		expect(view.showSelectedMembersCount.value).toBe(false);
	});
});
