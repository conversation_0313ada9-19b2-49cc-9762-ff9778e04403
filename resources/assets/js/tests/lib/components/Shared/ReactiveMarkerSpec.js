import { expect } from 'chai';
import ReactiveMarker from '@/lib/components/Shared/ReactiveMarker.vue';
import { shallowMount } from '@vue/test-utils';

describe('ReactiveMarker', () => {
	it('is reactive', () => {
		const reactiveMarker = shallowMount(ReactiveMarker, {
			propsData: { id: 502, ids: [500, 'string-id-501'] },
		});

		expect(reactiveMarker.vm.isChecked).to.be.false;

		reactiveMarker.vm.onClick();
		expect(reactiveMarker.vm.isChecked).to.be.true;
		expect(reactiveMarker.vm.localIds).to.deep.equal([500, 'string-id-501', 502]);

		reactiveMarker.vm.onClick();
		expect(reactiveMarker.vm.isChecked).to.be.false;
		expect(reactiveMarker.vm.localIds).to.deep.equal([500, 'string-id-501']);
	});
});
