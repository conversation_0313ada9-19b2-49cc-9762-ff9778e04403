import { Composer } from '@/domain/services/Composer';
import DraggableFields from '@/modules/entry-form/Configuration/DraggableFields.vue';
import { expect } from 'chai';
import Fields from '@/modules/entry-form/Fields/Fields.vue';
import Referee from '@/modules/entry-form/Referee.vue';
import RefereeConfigurator from '@/modules/entry-form/Configuration/RefereeConfigurator.vue';
import RefereesTab from '@/modules/entry-form/Tabs/RefereesTab.vue';
import { createLocalVue, shallowMount } from '@vue/test-utils';

const localVue = createLocalVue();

const tab = {
	id: 1,
	maxReferees: 2,
	minReferees: 1,
};

const mockViewProps = {
	addReferee: () => {},
	onInput: () => {},
	isLoaded: () => true,
	referees: [{}, {}],
	refereeFields: () => [],
	visibleRefereeFields: () => [],
	configurationMode: false,
	lang: { get: (key) => key },
	groupName: () => 'referees',
	refereeLimitReached: () => false,
	hasRefereeReviewStages: () => true,
};

const baseProps = {
	tab,
	transition: 'push',
	configure: false,
	access: { canEdit: true, canSave: true, canSubmit: true },
};

const baseData = () => ({
	access: { canEdit: true, canSave: true, canSubmit: true },
});

describe('RefereesTabSnapshot', () => {
	it('show referees add referee button', () => {
		Composer.mockView('refereesTabController', mockViewProps);
		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		expect(refereeTabSnapshot.html()).to.contain('entries.form.add-referee');
	});

	it('show referees list', () => {
		Composer.mockView('refereesTabController', mockViewProps);
		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		expect(refereeTabSnapshot.findComponent(Referee).exists()).to.be.true;
		expect(refereeTabSnapshot.findAllComponents(Referee).length).to.equal(mockViewProps.referees.length);
		expect(refereeTabSnapshot.html()).to.contain('position="1"');
		expect(refereeTabSnapshot.html()).to.contain('position="2"');
	});

	it('shows draggable fields for referees when in configuration mode', () => {
		Composer.mockView('refereesTabController', { ...mockViewProps, configurationMode: true });
		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		expect(refereeTabSnapshot.findComponent(DraggableFields).exists()).to.be.true;
	});

	it('shows fields for referees when not in configuration mode', () => {
		Composer.mockView('refereesTabController', mockViewProps);
		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		expect(refereeTabSnapshot.findComponent(Fields).exists()).to.be.true;
	});

	it('should show referee configuration where in configuration mode and no referees', () => {
		Composer.mockView('refereesTabController', { ...mockViewProps, configurationMode: true, referees: () => [] });
		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
		});
		expect(refereeTabSnapshot.findComponent(RefereeConfigurator).exists()).to.be.true;
	});

	it('add referee button should be disabled when referee limit is reached', () => {
		Composer.mockView('refereesTabController', { ...mockViewProps, refereeLimitReached: () => true });

		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		expect(refereeTabSnapshot.html()).to.contain('<button disabled="disabled"');
	});

	it('add referee button should not be disabled when referee limit is not reached', () => {
		Composer.mockView('refereesTabController', { ...mockViewProps, refereeLimitReached: () => false });

		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		expect(refereeTabSnapshot.html()).to.not.contain('<button disabled="disabled"');
	});

	it('should show review stage message when is not configured and it should not show referees', () => {
		Composer.mockView('refereesTabController', { ...mockViewProps, hasRefereeReviewStages: false });

		const refereeTabSnapshot = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		expect(refereeTabSnapshot.html()).to.contain('entries.form.referee.no-review-stage');
		expect(refereeTabSnapshot.findComponent(Referee).exists()).to.be.false;
	});

	it('it does not remount Referee component when referee id prop is updated', async () => {
		const initialReferees = [
			{ id: 'new-123', name: 'Test Referee', email: '<EMAIL>', tabId: 1, requestCompleted: false },
			{ id: 'new-456', name: 'Test Referee 2', email: '<EMAIL>', tabId: 1, requestCompleted: false },
		];

		Composer.mockView('refereesTabController', { ...mockViewProps, referees: initialReferees });

		const wrapper = shallowMount(RefereesTab, {
			localVue,
			propsData: { ...baseProps },
			data: baseData,
		});

		// 2. Get the Referee component's instance and key
		const refereeWrappers = wrapper.findAllComponents(Referee);
		expect(refereeWrappers.length).to.equal(2);

		const firstRefereeWrapper = refereeWrappers.at(0);
		const firstRefereeKey = firstRefereeWrapper.vm.$vnode.key;
		expect(firstRefereeWrapper.props('referee').id).to.equal('new-123');
		expect(firstRefereeKey).to.equal('referee-0');

		// 3. Simulate referee id update
		const updatedReferee = { ...initialReferees[0], id: 42 };
		await wrapper.setProps({ referees: initialReferees.splice(0, 1, updatedReferee) });

		// 4. Get the Referee component again
		const updatedRefereeWrappers = wrapper.findAllComponents(Referee);
		expect(updatedRefereeWrappers.length).to.equal(2);

		const updatedRefereeWrapper = updatedRefereeWrappers.at(0);
		const updatedRefereeKey = updatedRefereeWrapper.vm.$vnode.key;
		expect(updatedRefereeWrapper.props('referee').id).to.equal(42);
		expect(updatedRefereeKey).to.equal('referee-0');

		// 5. Confirm the instance and key have NOT changed
		expect(firstRefereeWrapper.vm).to.equal(updatedRefereeWrapper.vm); // same component instance
		expect(firstRefereeKey).to.equal(updatedRefereeKey); // key is stable
	});
});
