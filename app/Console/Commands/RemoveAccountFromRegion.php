<?php

namespace AwardForce\Console\Commands;

use AwardForce\Library\Database\DatabaseSelector;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Nuke\Nuke;
use Illuminate\Console\Command;
use Platform\Database\Database;

class RemoveAccountFromRegion extends Command
{
    protected $signature = 'dev:remove-account';

    public function handle(Nuke $nuke, AccountRepository $accounts)
    {
        $databases = $this->availableDatabases();

        $database = $this->choice('Which database connection would you like to remove the account from?', $databases);

        $accountSlug = $this->ask('What is the slug of the account you want to remove?');

        $this->switchConnection($database);
        $account = $accounts->requireBy('slug', $accountSlug);

        if (($intended = $account->globalAccount->database->name()) == $database) {
            $this->warn("Can't remove account {$accountSlug} from it's intended database ({$intended})");

            return;
        }

        if (! $this->confirm("Are you sure you want to remove account ($accountSlug) from database: $database?")) {
            $this->warn('Canceled.');

            return;
        }

        // Remove translations by account id
        $this->info('Removing associated records...');

        $nuke->deleteForAccount($account->id, 'assignments');
        $nuke->deleteForAccount($account->id, 'awards');
        $nuke->deleteForAccount($account->id, 'attachments');
        $nuke->deleteForAccount($account->id, 'broadcasts');
        $nuke->deleteForAccount($account->id, 'categories');
        $nuke->deleteForAccount($account->id, 'chapters');
        $nuke->deleteForAccount($account->id, 'content_blocks');
        $nuke->deleteForAccount($account->id, 'comments');
        $nuke->deleteForAccount($account->id, 'entries');
        $nuke->deleteForAccount($account->id, 'fields');
        $nuke->deleteForAccount($account->id, 'files');
        $nuke->deleteForAccount($account->id, 'memberships');
        $nuke->deleteForAccount($account->id, 'notifications');
        $nuke->deleteForAccount($account->id, 'organisations');
        $nuke->deleteForAccount($account->id, 'orders');
        $nuke->deleteForAccount($account->id, 'panels');
        $nuke->deleteForAccount($account->id, 'prices');
        $nuke->deleteForAccount($account->id, 'reports');
        $nuke->deleteForAccount($account->id, 'review_stages');
        $nuke->deleteForAccount($account->id, 'roles');
        $nuke->deleteForAccount($account->id, 'rounds');
        $nuke->deleteForAccount($account->id, 'score_sets');
        $nuke->deleteForAccount($account->id, 'scoring_criteria');
        $nuke->deleteForAccount($account->id, 'searches');
        $nuke->deleteForAccount($account->id, 'settings');
        $nuke->deleteForAccount($account->id, 'tabs');
        $nuke->deleteForAccount($account->id, 'tags');
        $nuke->deleteForAccount($account->id, 'taxes');
        $nuke->deleteForAccount($account->id, 'themeing');
        $nuke->deleteForAccount($account->id, 'translations');

        $this->info('Done.');

        $this->info('Deleting seasons...');
        $nuke->deleteForAccount($account->id, 'seasons');
        $this->info('Done.');

        $this->info('Removing account...');
        $nuke->account($account->id);
        $this->info('Done.');

        $this->info('Account removal complete.');
    }

    private function availableDatabases(): array
    {
        return Database::databases()->map(function (Database $database) {
            return $database->name();
        })->all();
    }

    private function switchConnection(string $connection)
    {
        app(DatabaseSelector::class)->select(new Database($connection));
    }
}
