<?php

namespace AwardForce\Console\Commands\Migrations;

use AwardForce\Console\Commands\RunsOnAllDatabases;
use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use CurrentAccount;
use Illuminate\Console\Command;
use Illuminate\Translation\Translator;
use Platform\Events\EventDispatcher;

class SeedOrganisationsFields extends Command
{
    use EventDispatcher;
    use RunsOnAllDatabases;

    /**
     * @var string
     */
    protected $signature = 'migrate:seed-organisations-fields';

    /**
     * @var string
     */
    protected $description = 'Seed organisations fields.';

    public function __construct(private FieldRepository $fields, private Translator $translator)
    {
        parent::__construct();
    }

    public function handle()
    {
        $organisationsFields = collect(config('seeds.'.Vertical::translationsKey().'.fields'))
            ->where(fn($fieldData) => $fieldData['resource'] == Field::RESOURCE_ORGANISATIONS);

        $this->runOnAllDatabases(function () use ($organisationsFields) {
            $accountsCount = Account::count();
            $this->info("Found {$accountsCount} accounts...");
            $this->output->progressStart($accountsCount);
            foreach (Account::cursor() as $account) {
                CurrentAccount::set($account);
                $organisationsFields->each(fn($fieldData, $fieldKey) => $this->addField($account, $fieldData, $fieldKey));
                $this->output->progressAdvance();
            }
            $this->output->progressFinish();
        });
    }

    private function addField(Account $account, $fieldData, $fieldKey)
    {
        $defaultParams = [
            'entrantReadAccess' => true,
            'entrantWriteAccess' => true,
            'required' => false,
        ];

        $field = Field::whereAccountId($account->id)
            ->whereResource($fieldData['resource'])
            ->whereType($fieldData['type'])
            ->first();

        if ($field && ! $field->wasUpdated()) {
            return;
        }

        $field = Field::add(
            $account->id,
            $account->activeSeason()->id,
            null,
            null,
            $fieldData['resource'],
            $fieldData['type'],
            $fieldData['seasonal'] ?? true,
            $fieldData['options'] ?? '',
            $fieldData['order'],
            $fieldData['categoryOption'] ?? 'all',
            $fieldData['roleOption'] ?? 'all',
            0,
            false,
            array_merge($defaultParams, $data['params'] ?? [])
        );

        $this->addTranslations($field, $account->languages, $fieldKey);

        $this->dispatch($field->releaseEvents());
    }

    /**
     * @param  Field  $field
     * @param  Collection  $supportedLanguages
     * @param  string  $fieldKey
     */
    private function addTranslations($field, $supportedLanguages, $fieldKey)
    {
        $this->addTranslation($field, $supportedLanguages, $fieldKey, 'label');
        $this->addTranslation($field, $supportedLanguages, $fieldKey, 'title');
    }

    /**
     * @param  Field  $field
     * @param  Collection  $supportedLanguages
     * @param  string  $fieldKey
     * @param  string  $key
     */
    private function addTranslation($field, $supportedLanguages, $fieldKey, $key)
    {
        $translationKey = 'fields.'.Vertical::translationsKey().'.'.$fieldKey.'.'.$key;

        foreach ($supportedLanguages as $language) {
            if (! $this->translator->has($translationKey, $language->code, false)) {
                continue;
            }

            $field->saveTranslation(
                $language->code,
                $key,
                trans($translationKey, [], $language->code),
                $field->accountId
            );
        }
    }
}
