<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Modules\Payments\Contracts\Gateway;
use AwardForce\Modules\Payments\Exceptions\InvalidGatewayConfigException;
use AwardForce\Modules\Payments\RedirectResponse;
use AwardForce\Modules\Payments\Response;
use Stripe\Checkout\Session;

abstract class StripeCheckout extends AbstractGateway implements Gateway
{
    protected string $apiKey;
    private string $currency;

    /**
     * @throws InvalidGatewayConfigException
     */
    public function __construct(protected string $clientId, protected bool $testMode)
    {
        if (empty($clientId)) {
            throw new InvalidGatewayConfigException;
        }

        $this->apiKey = $testMode ? config('services.stripe_connect.api_key_test') : config('services.stripe_connect.api_key_live');
    }

    public function purchase($amount, $data = [])
    {
        try {
            \Stripe\Stripe::setApiKey($this->apiKey);
            $checkoutSession = Session::create($this->paymentParams($amount, $data), $this->paymentOptions());
            $this->storeParams(['sessionId' => $checkoutSession->id]);

            return new RedirectResponse($checkoutSession->url);
        } catch (\Throwable $e) {
            return new Response(false, $e->getMessage());
        }
    }

    /**
     * if we reach this point, the payment was successful always as the failure is handled by stripe
     */
    public function complete()
    {
        $params = $this->retrieveParams();
        $this->clearParams();

        return new Response(true);
    }

    public function getTestMode()
    {
        return $this->testMode;
    }

    public function setTestMode($mode)
    {
        $this->testMode = $mode;
    }

    public function getCurrency()
    {
        return $this->currency;
    }

    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    public function getReturnUrl(): string
    {
        return current_account_url().$this->returnUrl().'?session_id={CHECKOUT_SESSION_ID}';
    }

    public function getCancelUrl(): string
    {
        return current_account_url().$this->cancelUrl().'?session_id={CHECKOUT_SESSION_ID}';
    }

    private function paymentParams($amount, $data = [])
    {
        return [
            'payment_method_types' => [$this->gatewayName()],
            'line_items' => [
                [
                    'price_data' => [
                        'currency' => $this->getCurrency(),
                        'product_data' => [
                            'name' => array_get($data, 'itemsDescription'),
                        ],
                        'unit_amount' => (int) ((float) $amount * 100), // Amount in cents
                    ],
                    'quantity' => 1,
                ],
            ],
            'mode' => 'payment',
            'success_url' => $this->getReturnUrl(),
            'cancel_url' => $this->getCancelUrl(),
        ];
    }

    private function paymentOptions()
    {
        return [
            'stripe_account' => $this->clientId,
        ];
    }
}
