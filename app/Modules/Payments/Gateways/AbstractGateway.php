<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Values\Amount;
use AwardForce\Modules\Payments\Contracts\Gateway;
use Exception;
use Illuminate\Support\Facades\Cache;

abstract class AbstractGateway
{
    /**
     * Length of time to store payment info in cache
     *
     * @const integer
     */
    const TIMEOUT = 60;

    /** @var Gateway */
    protected $gateway;

    /** @var string */
    const RETURN_URL = '/payment/{gateway}/return';

    /** @var string */
    const CANCEL_URL = '/payment/{gateway}/cancel';

    protected ?int $descriptionCharacterLimit = null;

    /**
     * Cache payment parameters for off-site payment handling
     */
    protected function storeParams(array $params)
    {
        // Clear any previously stored parameters
        $this->clearParams();

        Cache::put($this->cacheKey(), $params, now()->addMinutes(static::TIMEOUT));
    }

    /**
     * Retrieve caches payment parameters for off-site payment handling
     *
     * @return array
     *
     * @throws Exception
     */
    protected function retrieveParams()
    {
        $params = Cache::get($this->cacheKey());

        // If no params exist, the cache has timed out - took longer than 15 minutes
        if (is_null($params)) {
            throw new Exception('Payment took longer than '.static::TIMEOUT.' minutes and timed out.');
        }

        return $params;
    }

    /**
     * Clear cached payment parameters
     */
    protected function clearParams()
    {
        Cache::forget($this->cacheKey());
    }

    protected function cacheKey()
    {
        $user = Consumer::get()->user();

        return payment_cache_key($user->id, $user->currentMembership->id);
    }

    abstract protected function gatewayName(): string;

    /**
     * Determine is test mode is on/off
     *
     * @return bool
     */
    protected function testMode()
    {
        // TODO: add logic here
    }

    /**
     * @throws \Exception
     */
    public function setGatewayParameters(array $parameters = [])
    {
        $this->storeParams(array_merge($this->retrieveParams(), $parameters));
    }

    protected function formatAmount($amount)
    {
        $total = Amount::fromPriceAndCurrencyCode($amount, $this->getCurrency());

        return number_format($total->valueFromCents(), 2, '.', '');
    }

    protected function cancelUrl()
    {
        return str_replace('{gateway}', $this->gatewayName(), self::CANCEL_URL);
    }

    protected function returnUrl()
    {
        return str_replace('{gateway}', $this->gatewayName(), self::RETURN_URL);
    }

    public function store3DS2OrderParams($params): void
    {
        session()->put("gateway.{$this->gatewayName()}.order", $params);
    }

    public function get3DS2OrderParams(): mixed
    {
        return session("gateway.{$this->gatewayName()}.order");
    }

    public function forget3DS2OrderParams(): void
    {
        session()->forget("gateway.{$this->gatewayName()}.order");
    }

    public function checkoutFormRoute(): string
    {
        return 'cart.process';
    }

    public function checkoutFormStyle(): string
    {
        return '';
    }

    public function cardNumberCustomAttributes(): array
    {
        return [];
    }

    public function customSubmitFormButton(): string
    {
        return '';
    }

    protected function trimDescriptionToCharacterLimit(?string $description): ?string
    {
        return is_null($this->descriptionCharacterLimit) || mb_strlen($description ?? '') <= $this->descriptionCharacterLimit
            ? $description
            : mb_substr($description, 0, $this->descriptionCharacterLimit);
    }
}
