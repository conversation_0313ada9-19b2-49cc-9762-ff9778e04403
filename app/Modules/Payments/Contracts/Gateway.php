<?php

namespace AwardForce\Modules\Payments\Contracts;

interface Gateway
{
    /**
     * Get test mode
     *
     * @return bool
     */
    public function getTestMode();

    /**
     * Set test mode
     *
     *
     * @return void
     */
    public function setTestMode($mode);

    /**
     * Get currency code
     *
     * @return string
     */
    public function getCurrency();

    /**
     * Set the currency code
     *
     * @param  string  $currency
     * @return void
     */
    public function setCurrency($currency);

    /**
     * Handle making the purchase
     *
     * @param  array  $data
     * @return \AwardForce\Modules\Payments\Contracts\Response
     */
    public function purchase($amount, $data = []);
}
