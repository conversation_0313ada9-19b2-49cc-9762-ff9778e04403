<?php

namespace AwardForce\Modules\Payments\Services;

use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Costing\NullPrice;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Repositories\PriceRepository;
use Tectonic\LaravelLocalisation\Facades\Translator;

class Prices
{
    public function __construct(private PriceRepository $prices)
    {
    }

    /**
     * Return the price record for the entry specified in the cart. If no entry
     * fee is specified, return the default price for the given season.
     *
     * @return NullPrice|Price
     */
    public function getPriceForEntryFee(Cart $cart, int $seasonId)
    {
        return once(function () use ($cart, $seasonId) {
            if ($entryFee = $cart->get('entryFee')) {
                $price = $this->prices->getById($entryFee);
            }

            if (($price->seasonId ?? null) !== $seasonId) {
                $price = $this->prices->getDefaultOrFirst(Price::TYPE_ENTRY, $seasonId);
            }

            if ($price = $price ?? null) {
                return Translator::translate($price);
            }

            return new NullPrice;
        });
    }
}
