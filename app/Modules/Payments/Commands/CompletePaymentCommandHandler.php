<?php

namespace AwardForce\Modules\Payments\Commands;

use AwardForce\Modules\Ecommerce\Cart\Services\CartPayment;
use AwardForce\Modules\Payments\GatewayManager;
use Platform\Events\EventDispatcher;
use Platform\Events\Raiseable;

class CompletePaymentCommandHandler
{
    use EventDispatcher, Raiseable;

    /**
     * @var CartPayment
     */
    private $cartPayment;

    public function __construct(CartPayment $cartPayment)
    {
        $this->cartPayment = $cartPayment;
    }

    /**
     * Complete the payment with off-site payment gateway
     */
    public function handle(CompletePaymentCommand $command)
    {
        $gateway = GatewayManager::create($command->gateway);
        $gateway->setCurrency($command->cart->currencyCode());
        $gateway->setGatewayParameters($command->gatewayParameters);

        $response = $gateway->complete();

        $this->cartPayment->handleResponse($command->cart, $response);

        // Payment was unsuccessful
        $this->dispatch($this->cartPayment->releaseEvents());

        return $response;
    }
}
