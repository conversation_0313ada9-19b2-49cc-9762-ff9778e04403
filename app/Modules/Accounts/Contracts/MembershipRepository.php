<?php

namespace AwardForce\Modules\Accounts\Contracts;

use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Seasons\Models\Season;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Repository;

interface MembershipRepository extends BuilderRepository, Repository
{
    /**
     * Find the relevant membership record for the account and user.
     */
    public function getByAccountIdAndUserId(int $accountId, int $userId): ?Membership;

    /**
     * Find the relevant membership record even when they are soft deleted for the account and user.
     */
    public function getByAccountIdAndUserIdWithTrashed(int $accountId, int $userId): ?Membership;

    /**
     * Find all the memberships with activity in a given season and not registered yet
     */
    public function registerMembershipActivityInSeason(Season $season);

    /**
     * Retrieve the total number of users in account
     *
     * @param  bool  $softDeletes
     * @return int
     */
    public function getTotal($softDeletes = true);

    public function getTrashedByAccountIdAndUserId(int $accountId, int $userId);

    public function user(int ...$userId): self;
}
