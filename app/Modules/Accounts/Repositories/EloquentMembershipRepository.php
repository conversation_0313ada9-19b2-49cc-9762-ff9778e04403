<?php

namespace AwardForce\Modules\Accounts\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ImplementsSearchByField;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\WithValues;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\RepositoryWithValues;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Database\Eloquent\Builder;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentMembershipRepository extends Repository implements MembershipRepository, RepositoryWithValues
{
    use HasQueryBuilder;
    use ImplementsSearchByField;
    use WithValues;

    public function __construct(Membership $model)
    {
        $this->model = $model;
    }

    /**
     * Searches for a given record based on the account and user id provided.
     */
    public function getByAccountIdAndUserId(int $accountId, int $userId): ?Membership
    {
        return $this->model
            ->whereAccountId($accountId)
            ->whereUserId($userId)
            ->first();
    }

    public function getByAccountIdAndUserIdWithTrashed(int $accountId, int $userId): ?Membership
    {
        return $this->model
            ->whereAccountId($accountId)
            ->whereUserId($userId)
            ->withTrashed()
            ->first();
    }

    public function getSeasonedQuery(Field $field): Builder
    {
        return $field->seasonal ? $this->getQuery()->whereSeasonId($field->seasonId) : $this->getQuery();
    }

    public function registerMembershipActivityInSeason(Season $season)
    {
        $activities = app(EventLogRepository::class)->userActivityInSeason($season->id);
        while ($results = $activities->current()) {
            $this->getQuery()->select('memberships.id', 'memberships.account_id')
                ->whereIn('user_id', $results->all())
                ->groupBy('memberships.id')
                ->with('account')
                ->chunk(1000, function ($memberships) use ($season) {
                    $values = $memberships->pluck('id')->unique()->map(function ($id) use ($season) {
                        return '('.$id.','.$season->id.')';
                    })->toArray();

                    \DB::insert('INSERT INTO membership_season (membership_id, season_id) VALUES '.
                        implode(',', $values).' ON DUPLICATE KEY UPDATE membership_id = VALUES(membership_id),
                    season_id = VALUES(season_id)');
                });
            $activities->next();
        }
    }

    public function getTrashedByAccountIdAndUserId(int $accountId, int $userId)
    {
        return $this->getQuery()
            ->where('account_id', $accountId)
            ->where('user_id', $userId)
            ->whereNotNull('deleted_at')
            ->withTrashed()
            ->first();
    }

    /**
     * Retrieve the total number of users in account
     *
     * @param  bool  $softDeletes
     * @return int
     */
    public function getTotal($softDeletes = true)
    {
        $query = $this->getQuery()
            ->select(\DB::raw('COUNT(id) AS total'));
        if (! $softDeletes) {
            $query = $query->whereNull('deleted_at');
        }

        return $query->first()->total;
    }

    public function user(int ...$userId): self
    {
        $this->query()->whereIn('user_id', $userId);

        return $this;
    }
}
