<?php

namespace AwardForce\Modules\Accounts\Repositories;

use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Audit\Data\EventLog;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Support\Facades\DB;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Library\Values\FindByField;

final class EloquentMembershipRepositoryTest extends BaseTestCase
{
    use Database;
    use FindByField;
    use Laravel;

    /** @var EloquentMembershipRepository */
    protected $repository;

    public function init()
    {
        $this->repository = app(EloquentMembershipRepository::class);
    }

    public function testGetModel(): void
    {
        $this->assertInstanceOf(Membership::class, $this->repository->getModel());
    }

    public function testGetTotal(): void
    {
        $user = $this->muffin(User::class, ['first_name' => 'name ', 'last_name' => ' lastname ']);

        $this->assertEquals(0, $this->repository->getTotal());

        Membership::register(current_account(), $user, 'en_GB');

        $this->assertEquals(1, $this->repository->getTotal());
    }

    public function testGetTotalWithSoftDeletes(): void
    {
        $user = $this->muffin(User::class, ['first_name' => 'name ', 'last_name' => ' lastname ']);

        Membership::register(current_account(), $user, 'en_GB');

        $this->assertEquals(1, $this->repository->getTotal());

        $user->deleteMembership(current_account());

        $this->assertEquals(0, $this->repository->getTotal(false));
    }

    public function testRegisterMembershipActivityInSeason(): void
    {
        $this->markTestSkipped('Not used');
        $user = $this->muffin(User::class);
        $season = $this->muffin(Season::class, ['account_id' => current_account()->id, 'status' => Season::STATUS_ACTIVE]);
        $this->muffin(Membership::class, ['account_id' => current_account()->id, 'user_id' => $user->id, 'season_id' => $season->id]);
        $this->muffin(EventLog::class, ['user_id' => $user->id, 'season_id' => $season->id]);

        $this->repository->registerMembershipActivityInSeason($season);

        $results = DB::table('membership_season')->whereSeasonId($season->id)->get();

        $this->assertCount(1, $results);
    }

    public function testGetTrashedByAccountIdAndUserId(): void
    {
        $user = $this->muffin(User::class);
        Membership::register(current_account(), $user, 'en_GB');
        $user->deleteMembership(current_account());

        $membership = $this->repository->getTrashedByAccountIdAndUserId(current_account()->id, $user->id);

        $this->assertNotNull($membership);
        $this->assertEquals(current_account()->id, $membership->accountId);
        $this->assertEquals($user->id, $membership->userId);
    }

    public function testItCanQueryBySingleUser(): void
    {
        $user = $this->muffin(User::class, ['first_name' => 'name ', 'last_name' => ' lastname ']);

        Membership::register(current_account(), $user, 'en_GB');

        $membership = $this->repository
            ->fields(['id', 'account_id', 'user_id'])
            ->user($user->id)
            ->first();

        $this->assertEquals($user->id, $membership->userId);
        $this->assertEquals(current_account_id(), $membership->accountId);
    }

    public function testItCanQueryByMultipleUsers(): void
    {
        $user1 = $this->muffin(User::class, ['first_name' => 'name ', 'last_name' => ' lastname ']);
        $user2 = $this->muffin(User::class, ['first_name' => 'name ', 'last_name' => ' lastname ']);

        Membership::register(current_account(), $user1, 'en_GB');
        Membership::register(current_account(), $user2, 'en_GB');

        $memberships = $this->repository
            ->fields(['id', 'account_id', 'user_id'])
            ->user($user1->id, $user2->id)
            ->sort('user_id')
            ->get();

        $this->assertCount(2, $memberships);
        $this->assertEquals($user1->id, $memberships[0]->userId);
        $this->assertEquals(current_account_id(), $memberships[0]->accountId);

        $this->assertEquals($user2->id, $memberships[1]->userId);
        $this->assertEquals(current_account_id(), $memberships[1]->accountId);
    }
}
