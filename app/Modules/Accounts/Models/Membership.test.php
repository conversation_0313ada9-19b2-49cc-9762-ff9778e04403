<?php

namespace AwardForce\Modules\Accounts\Models;

use AwardForce\Modules\Seasons\Models\Season;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class MembershipTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testActiveInSeason(): void
    {
        $membership = $this->muffin(Membership::class);
        $season = $this->muffin(Season::class);
        $membership->setActiveInSeason($season);

        $this->assertSame($season->id, $membership->seasons()->first()->id);
    }

    public function testActiveInSeasonFromAccount(): void
    {
        $account = $this->muffin(Account::class);
        $membership = $this->muffin(Membership::class, ['account_id' => $account->id, 'season_id' => null]);

        $membership->setActiveInSeason();

        $this->assertSame($account->activeSeason()->id, $membership->seasons()->first()->id);
    }
}
