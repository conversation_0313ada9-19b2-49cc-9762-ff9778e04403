<?php

namespace AwardForce\Modules\Accounts\Models;

use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFlatValues;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member as OrganisationMember;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Platform\Search\HasValues;

/**
 * AwardForce\Modules\Accounts\Models\Membership
 *
 * @property int $id
 * @property int $accountId
 * @property int $userId
 * @property int|null $seasonId
 * @property string|null $language
 * @property string|null $timezone
 * @property bool $broadcastEmails
 * @property bool $notificationEmails
 * @property bool $notificationSms
 * @property bool $blocked
 * @property string|null $agreementToTerms
 * @property string|null $consentToNotifications
 * @property array|null $cookies
 * @property string|null $samlIssuer
 * @property string|null $samlNameId
 * @property string|null $confirmedAt
 * @property \Illuminate\Support\Carbon|null $deletedAt
 * @property string $createdAt
 * @property array|null $values
 * @property array|null $hashes
 * @property array|null $protected
 * @property-read \AwardForce\Modules\Accounts\Models\Account|null $account
 * @property-read \Platform\Database\Eloquent\Collection<int, Season> $seasons
 * @property-read int|null $seasonsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, \AwardForce\Modules\Accounts\Models\MembershipSetting> $settings
 * @property-read int|null $settingsCount
 * @property-read User|null $user
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Membership newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Membership newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Membership onlyTrashed()
 * @method static \Platform\Database\Eloquent\Builder|Membership preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Membership query()
 * @method static \Platform\Database\Eloquent\Builder|Membership whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereAgreementToTerms($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereBlocked($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereBroadcastEmails($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereConfirmedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereConsentToNotifications($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereCookies($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereHashes($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereLanguage($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereNotificationEmails($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereNotificationSms($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereProtected($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereSamlIssuer($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereSamlNameId($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereTimezone($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereUserId($value)
 * @method static \Platform\Database\Eloquent\Builder|Membership whereValues($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Membership withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Membership withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Membership extends Model implements HasValues, ValuesProvider
{
    use BelongsToAccount;
    use HasFlatValues;
    use SoftDeletes;

    /**
     * @var string
     */
    public $table = 'memberships';

    /**
     * @var bool
     */
    public $timestamps = false;

    /**
     * @var array
     */
    protected $casts = [
        'broadcast_emails' => 'boolean',
        'notification_emails' => 'boolean',
        'notification_sms' => 'boolean',
        'blocked' => 'boolean',
        'cookies' => 'array',
        'values' => 'array',
        'hashes' => 'array',
        'protected' => 'array',
    ];

    /**
     * Registers a new membership between an account and user.
     *
     * @param  string  $language
     * @return Membership
     */
    public static function register(Account $account, User $user, $language)
    {
        $membership = new self;
        $membership->accountId = $account->id;
        $membership->userId = $user->id;
        $membership->language = $language;
        $membership->seasonId = $account->activeSeason() ? $account->activeSeason()->id : null;

        $membership->save();

        $membership->setSetting('color', random_color());

        return $membership;
    }

    /**
     * Returns the user's colour setting or a random colour from the default colours.
     */
    public function getColour(): string
    {
        return $this->getSetting('colour') ?? $this->seedDefaultColour();
    }

    /**
     * Model event listener to set the created_at timestamp on creation
     */
    public static function boot()
    {
        parent::boot();

        static::creating(function ($membership) {
            $membership->created_at = $membership->freshTimestamp();
        });
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function settings()
    {
        return $this->hasMany(MembershipSetting::class);
    }

    /**
     * @param  string  $key
     * @param  mixed  $default
     * @return mixed
     */
    public function getSetting($key, $default = null)
    {
        $setting = $this->settings->where('key', $key)->first();

        return $setting ? $setting->value : $default;
    }

    /**
     * @param  string  $key
     * @param  mixed  $value
     */
    public function setSetting($key, $value)
    {
        $setting = $this->settings->where('key', $key)->first() ?: new MembershipSetting(['key' => $key]);

        $setting->value = $value;

        $this->settings()->save($setting);

        // Force Eloquent to forget cached settings collection.
        unset($this->relations['settings']);
    }

    public function block(): Membership
    {
        $this->blocked = true;
        $this->save();

        return $this;
    }

    public function unblock(): Membership
    {
        $this->blocked = false;
        $this->save();

        return $this;
    }

    public function seasons(): BelongsToMany
    {
        return $this->belongsTomany(Season::class);
    }

    public function setActiveInSeason(?Season $season = null)
    {
        if (! $season) {
            $season = $this->account->activeSeason();
        }

        if (! $this->seasons->contains($season->id)) {
            $this->seasons()->save($season);
        }
    }

    /**
     * Sets the user's colour setting to a default colour.
     */
    public function seedDefaultColour(): string
    {
        $colour = random_color();

        $this->setSetting('colour', $colour);

        return $colour;
    }

    public function organisationMembers(): HasMany
    {
        return $this->hasMany(OrganisationMember::class);
    }
}
