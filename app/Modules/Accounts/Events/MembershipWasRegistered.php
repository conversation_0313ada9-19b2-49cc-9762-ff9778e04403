<?php

namespace AwardForce\Modules\Accounts\Events;

use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Audit\Events\Activity;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Webhooks\Contracts\TriggersWebhooks;
use AwardForce\Modules\Webhooks\Traits\ResolveUserWebhooks;

class MembershipWasRegistered implements Activity, TriggersWebhooks
{
    use ResolveUserWebhooks;

    public function __construct(public readonly Membership $membership)
    {
    }

    public function user(): User
    {
        return $this->membership->user;
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource('membership'),
            'registered',
            'audit.membership.registered',
            $this->membership,
            $this->membership->id,
            (string) $this->user()->slug
        );
    }
}
