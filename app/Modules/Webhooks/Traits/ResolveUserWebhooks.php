<?php

namespace AwardForce\Modules\Webhooks\Traits;

use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Modules\Identity\Users\Models\User;

/**
 * Can be used in events that expose a user model when they implement UsesWebhooks
 */
trait ResolveUserWebhooks
{
    use ResolveWebhooks;

    public function webhookResource(): Model
    {
        return $this->user();
    }

    public function columnatorArea(): string
    {
        return 'users.search';
    }

    abstract public function user(): User;
}
