<?php

namespace AwardForce\Modules\Organisations;

use AwardForce\Modules\Organisations\Members\MemberServiceProvider;
use AwardForce\Modules\Organisations\Organisations\OrganisationsServiceProvider;
use Illuminate\Support\AggregateServiceProvider;

class OrganisationsAggregateServiceProvider extends AggregateServiceProvider
{
    protected $providers = [
        OrganisationsServiceProvider::class,
        MemberServiceProvider::class,
    ];
}
