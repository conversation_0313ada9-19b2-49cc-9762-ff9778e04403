<?php

use AwardForce\Modules\Identity\Roles\Services\PermissionsMigrationService;
use Illuminate\Database\Migrations\Migration;
use Platform\Authorisation\FeatureRoles\ProgramManager;

// TODO: Revisit this
return new class extends Migration
{
    public function up()
    {
        $service = app(PermissionsMigrationService::class);

        $service->roles(function ($role) {
            return ProgramManager::appliesTo($role);
        })->each(function ($role) use ($service) {
            $service->addResource($role, 'Organisations');
        });
    }

    public function down()
    {
        $service = app(PermissionsMigrationService::class);

        $service->forgetResource('Organisations');
    }
};
