<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organisation_events', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('account_id')->index();
            $table->binary('event_id', 16)->unique();
            $table->snowflake('aggregate_root_id')->index();
            $table->unsignedInteger('version')->nullable();
            $table->longText('payload');
            $table->unique(['aggregate_root_id', 'version']);

            $table->foreign('account_id')
                ->references('id')
                ->on('accounts')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organisation_events');
    }
};
