<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('organisations', function (Blueprint $table) {
            $table->snowflakeId();
            $table->unsignedInteger('account_id')->index();
            $table->foreignId('administrator_id')->nullable()->index()->constrained('users');
            $table->unsignedInteger('season_id');
            $table->unsignedInteger('version')->nullable();
            $table->string('name')->index();
            $table->enum('created_by', ['api', 'import', 'system', 'user'])->default('system');
            $table->foreignId('created_by_user_id')->nullable()->index()->constrained('users');
            $table->foreignId('logo_id')->nullable()->constrained('files')->nullOnDelete();
            $table->json('domains')->nullable();
            $table->json('values')->nullable();
            $table->json('hashes')->nullable();
            $table->json('protected')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->foreign('account_id')
                ->references('id')
                ->on('accounts')
                ->cascadeOnDelete();

            $table->foreign('season_id')
                ->references('id')
                ->on('seasons')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('organisations');
    }
};
