<?php

namespace AwardForce\Modules\Organisations\Organisations\Database\Repositories;

use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Created;
use AwardForce\Modules\Organisations\Organisations\Domain\Organisation;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class OrganisationRepositoryTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanCreateOrganisationAggregate(): void
    {
        $aggregate = app(OrganisationRepository::class)->new();
        $aggregate->create('Test Organisation', 123, [], CreatedBy::System);

        $this->assertInstanceOf(Organisation::class, $aggregate);
        $this->assertCount(1, $aggregate->recordedEvents());
        $this->assertInstanceOf(Created::class, $event = $aggregate->recordedEvents()[0]);

        $this->assertEquals('Test Organisation', $event->name);
        $this->assertEquals(123, $event->administratorId);
    }
}
