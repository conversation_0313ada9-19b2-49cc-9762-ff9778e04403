<?php

namespace AwardForce\Modules\Organisations\Organisations\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\WithValues;
use AwardForce\Modules\Forms\Fields\Database\Repositories\RepositoryWithValues;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use EventSauce\EventSourcing\AggregateRootId;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentOrganisationProjectionRepository extends Repository implements OrganisationProjectionRepository, RepositoryWithValues
{
    use Exportable;
    use HasQueryBuilder;
    use WithValues;

    public function __construct(Organisation $organisation)
    {
        $this->model = $organisation;
    }

    public function domain(string $domain): self
    {
        $this->query()->whereJsonContains('domains', $domain);

        return $this;
    }

    public function forAggregateRoot(AggregateRootId $id): static
    {
        $this->query()->where('id', $id->toString());

        return $this;
    }

    public function searchName(string $name): self
    {
        $this->query()->where('name', 'like', "%{$name}%");

        return $this;
    }

    public function membership(int $membershipId): self
    {
        $this->query()->whereHas('members', function ($query) use ($membershipId) {
            $query->where('membership_id', $membershipId);
        });

        return $this;
    }
}
