<?php

namespace AwardForce\Modules\Organisations\Organisations\Database\Repositories;

use AwardForce\Library\Database\EventSourcing\MysqlAggregateRepository;
use AwardForce\Modules\Forms\Formables\Boundary\FormableAggregate;
use AwardForce\Modules\Organisations\Organisations\Domain\Organisation;

class MysqlOrganisationRepository extends MysqlAggregateRepository implements OrganisationRepository
{
    public function new(): FormableAggregate
    {
        return Organisation::new();
    }
}
