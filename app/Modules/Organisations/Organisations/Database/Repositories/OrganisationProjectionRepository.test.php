<?php

namespace AwardForce\Modules\Organisations\Organisations\Database\Repositories;

use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class OrganisationProjectionRepositoryTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanQueryForAggregateRoot(): void
    {
        $repository = app(OrganisationProjectionRepository::class);
        $aggregateRootId = OrganisationId::create();
        $organisation = Organisation::factory()->create(['id' => $aggregateRootId]);

        $this->assertEquals(
            $organisation->id,
            $repository->forAggregateRoot($aggregateRootId)
                ->fields(['id'])
                ->first()
                ->id);
    }

    public function testItCanQueryDomain(): void
    {
        $repository = app(OrganisationProjectionRepository::class);
        $domains = ['example.com', 'example.org'];
        $organisation1 = Organisation::factory()->create(['domains' => $domains]);
        $organisation2 = Organisation::factory()->create(['domains' => ['example.net']]);
        $organisation3 = Organisation::factory()->create(['domains' => ['example.com']]);

        $results = $repository->domain('example.com')
            ->fields(['id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertContains($organisation1->id->toInteger(), $results->pluck('id')->map->toInteger());
        $this->assertContains($organisation3->id->toInteger(), $results->pluck('id')->map->toInteger());
    }

    public function testItCanSearchByName(): void
    {
        $repository = app(OrganisationProjectionRepository::class);
        $organisation1 = Organisation::factory()->create(['name' => 'Test Organisation']);
        $organisation2 = Organisation::factory()->create(['name' => 'Another Organisation']);
        $organisation3 = Organisation::factory()->create(['name' => 'Test']);

        $results = $repository->searchName('test')
            ->fields(['id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertContains($organisation1->id->toInteger(), $results->pluck('id')->map->toInteger());
        $this->assertContains($organisation3->id->toInteger(), $results->pluck('id')->map->toInteger());
    }

    public function testItCanLoadOrganisationByRelatedMembership(): void
    {
        $this->setupUserWithRole('Entrant', true);
        $repository = app(OrganisationProjectionRepository::class);
        $organisation1 = Organisation::factory()->create(['name' => 'Test Organisation']);
        $organisation2 = Organisation::factory()->create(['name' => 'Another Organisation']);
        $organisation3 = Organisation::factory()->create(['name' => 'Test']);

        Member::factory()->create([
            'organisation_id' => $organisation1->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);
        Member::factory()->create([
            'organisation_id' => $organisation2->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);

        $results = $repository->membership($this->user->currentMembership->id)
            ->fields(['id'])
            ->pluck('id');

        $this->assertCount(2, $results);
        $this->assertTrue($results->contains($organisation1->id));
        $this->assertTrue($results->contains($organisation2->id));
        $this->assertFalse($results->contains($organisation3->id));
    }
}
