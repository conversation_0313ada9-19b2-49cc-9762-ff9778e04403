<?php

namespace AwardForce\Modules\Organisations\Organisations\Database\Repositories;

use AwardForce\Library\Database\EventSourcing\ProjectionRepository;
use AwardForce\Modules\Exports\Models\ExportRepository;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Repository;

interface OrganisationProjectionRepository extends BuilderRepository, ExportRepository, ProjectionRepository, Repository
{
    public function domain(string $domain): self;

    public function searchName(string $name): self;

    public function membership(int $membershipId): self;
}
