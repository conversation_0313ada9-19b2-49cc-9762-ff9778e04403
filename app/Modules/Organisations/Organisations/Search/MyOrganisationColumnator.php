<?php

namespace AwardForce\Modules\Organisations\Organisations\Search;

use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\Administrator;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\Name;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\Thumbnail;
use AwardForce\Modules\Organisations\Organisations\Search\Filters\AdministratorJoinFilter;
use AwardForce\Modules\Organisations\Organisations\Search\Filters\MyOrganisationsFilter;
use AwardForce\Modules\Organisations\Organisations\Search\Filters\OrganisationKeywordFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;

class MyOrganisationColumnator extends Columnator
{
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;
        $columns = $this->columns($view);
        $dependencies->add((new ColumnFilter(...$columns))->with('organisations.id', 'logo_id'));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new AdministratorJoinFilter);
        $dependencies->add(new MyOrganisationsFilter);
        $dependencies->add(OrganisationKeywordFilter::fromKeywords($this->input['keywords'] ?? ''));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'organisations.updated')->uniqueColumn('organisations.id'));

        return $dependencies;
    }

    protected function baseColumns()
    {
        return new Columns([
            new Thumbnail('my-organisation.show'),
            new Name('my-organisation.show'),
            new Administrator,
        ]);
    }

    public static function key(): string
    {
        return 'my-organisations.search';
    }

    public static function exportKey(): string
    {
        return '';
    }

    public function repository(): Repository
    {
        return app(OrganisationProjectionRepository::class);
    }

    public function resource()
    {
        return '';
    }
}
