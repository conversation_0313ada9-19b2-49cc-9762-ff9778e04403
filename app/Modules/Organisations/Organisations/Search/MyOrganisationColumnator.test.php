<?php

namespace AwardForce\Modules\Organisations\Organisations\Search;

use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use Platform\Search\ColumnatorSearch;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class MyOrganisationColumnatorTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private function search(array $input = [])
    {
        $columnator = app(ColumnatorFactory::class)->forArea('my-organisations.search', $input);

        return (new ColumnatorSearch($columnator))->search();
    }

    public function testCanSearchMyOrganisations(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        $this->setupUserWithRole('Program manager', true);

        $organisation = Organisation::factory(3)->create();

        // Other user membership organisation should not be included in results
        Member::factory()->create([
            'organisation_id' => $organisation[0]->id,
            'membership_id' => $user->currentMembership->id,
        ]);

        Member::factory()->create([
            'organisation_id' => $organisation[1]->id,
            'membership_id' => $user->currentMembership->id,
        ]);

        Member::factory()->create([
            'organisation_id' => $organisation[1]->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);

        Member::factory()->create([
            'organisation_id' => $organisation[2]->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);

        $results = $this->search();

        $this->assertCount(2, $results);

        $this->assertEqualsCanonicalizing(
            [
                $organisation[1]->id->toInteger(),
                $organisation[2]->id->toInteger(),
            ],
            $results->pluck('id')->map(fn($id) => $id->toInteger())->all()
        );
    }

    public function testItCanSearchByOrganisationName(): void
    {
        $this->setupUserWithRole('Program manager', true);

        $organisation = Organisation::factory()->create(['name' => 'Test Organisation']);
        Member::factory()->create([
            'organisation_id' => $organisation->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);
        $other = Organisation::factory()->create(['name' => 'Other Organisation']);
        Member::factory()->create([
            'organisation_id' => $other->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);

        $results = $this->search(['keywords' => 'test']);

        $this->assertCount(1, $results);
        $this->assertEquals($organisation->id, $results[0]->id);
    }
}
