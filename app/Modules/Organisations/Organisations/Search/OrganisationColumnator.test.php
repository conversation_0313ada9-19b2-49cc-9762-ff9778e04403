<?php

namespace AwardForce\Modules\Organisations\Organisations\Search;

use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use Platform\Search\ColumnatorSearch;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class OrganisationColumnatorTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private function search(array $input = [])
    {
        $columnator = app(ColumnatorFactory::class)->forArea('organisations.search', $input);

        return (new ColumnatorSearch($columnator))->search();
    }

    public function testCanSearchOrganisations()
    {
        Organisation::factory(3)->create();

        $organisations = $this->search();

        $this->assertCount(3, $organisations);
    }

    public function testSearchDeletedOrganisation()
    {
        Organisation::factory(2);
        $deletedOrganisation = Organisation::factory()->create(['deleted_at' => now()]);

        $deletedOrganisationsSearch = $this->search(['trashed' => 'only']);
        $this->assertCount(1, $deletedOrganisationsSearch);
        $this->assertEquals($deletedOrganisation->id, $deletedOrganisationsSearch->first()->id);
    }

    public function testItCountsMembersCorrectly(): void
    {
        $organisations = Organisation::factory(3)->create();
        $memberships = $this->muffins(4, Membership::class);

        Member::factory()->create(['organisation_id' => $organisations->first()->id, 'membership_id' => $memberships[0]->id]);
        Member::factory()->create(['organisation_id' => $organisations->first()->id, 'membership_id' => $memberships[2]->id]);
        Member::factory()->create(['organisation_id' => $organisations->first()->id, 'membership_id' => $memberships[3]->id]);
        Member::factory()->create(['organisation_id' => $organisations->last()->id, 'membership_id' => $memberships[0]->id]);
        Member::factory()->create(['organisation_id' => $organisations->last()->id, 'membership_id' => $memberships[1]->id]);

        $results = $this->search();

        $this->assertEquals(3, $results->first(fn($organisation) => $organisation->id->toInteger() === $organisations[0]->id->toInteger())->membersCount);
        $this->assertEquals(2, $results->last()->membersCount);
        $this->assertEquals(0, $results->first(fn($organisation) => $organisation->id->toInteger() === $organisations[1]->id->toInteger())->membersCount);
    }

    public function testItCanSearchByOrganisationName(): void
    {
        $this->setupUserWithRole('Program manager', true);

        $organisation = Organisation::factory()->create(['name' => 'Test Organisation']);
        Organisation::factory()->create(['name' => 'Other Organisation']);

        $results = $this->search(['keywords' => 'test']);

        $this->assertCount(1, $results);
        $this->assertEquals($organisation->id, $results[0]->id);
    }
}
