<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Filters;

use Illuminate\Database\Query\Builder;
use Mockery as m;
use Tests\BaseTestCase;

class AdministratorJoinFilterTest extends BaseTestCase
{
    public function testItLeftJoinsUsersTable()
    {
        $query = m::mock(Builder::class);
        $query->shouldReceive('getQuery')
            ->once()
            ->andReturnSelf();

        $query->shouldReceive('leftJoin')
            ->once()
            ->with('users', 'users.id', '=', 'organisations.administrator_id')
            ->andReturnSelf();

        $filter = new AdministratorJoinFilter();
        $result = $filter->applyToEloquent($query);

        $this->assertSame($query, $result);
    }
}
