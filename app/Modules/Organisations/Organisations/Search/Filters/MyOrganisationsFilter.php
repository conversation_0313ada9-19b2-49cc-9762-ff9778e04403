<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Filters;

use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class MyOrganisationsFilter implements ColumnatorFilter, SearchFilter
{
    public function applyToEloquent($query)
    {
        return $query->whereHas('members', function ($query) {
            $query->where('membership_id', consumer()->user()->currentMembership->id)
                ->where('deleted_at', null);
        });
    }

    public function applies(): bool
    {
        return true;
    }
}
