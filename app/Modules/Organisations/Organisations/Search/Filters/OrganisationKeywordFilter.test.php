<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Filters;

use Illuminate\Database\Query\Builder;
use Mockery as m;
use Tests\BaseTestCase;

class OrganisationKeywordFilterTest extends BaseTestCase
{
    public function testItCanSearchByOrganisationName(): void
    {
        $query = m::mock(Builder::class);
        $query->shouldReceive('where')
            ->once()
            ->with(m::on(function ($callback) use ($query) {
                $query->shouldReceive('where')
                    ->once()
                    ->with('name', 'like', '%test%')
                    ->andReturnSelf();

                $query->shouldReceive('orWhere')
                    ->once()
                    ->andReturnSelf();
                $callback($query);

                return true;
            }))
            ->andReturnSelf();

        $filter = OrganisationKeywordFilter::fromKeywords('test');
        $result = $filter->applyToEloquent($query);

        $this->assertSame($query, $result);
    }

    public function testItCanSearchByUserFirstAndLastName(): void
    {
        $query = m::mock(Builder::class);
        $query->shouldReceive('where')
            ->once()
            ->with(m::on(function ($callback) use ($query) {
                $query->shouldReceive('where')
                    ->once()
                    ->with('name', 'like', '%test%')
                    ->andReturnSelf();
                $query->shouldReceive('orWhere')
                    ->once()
                    ->with(m::on(function ($callback) {
                        $userQuery = m::mock(Builder::class);
                        $userQuery->shouldReceive('whereRaw')
                            ->once()
                            ->with('MATCH(users.first_name, users.last_name, users.email) AGAINST (? IN BOOLEAN MODE)', 'test')
                            ->andReturnSelf();
                        $callback($userQuery);

                        return true;
                    }))
                    ->andReturn($query);
                $callback($query);

                return true;
            }))
            ->andReturnSelf();

        $filter = OrganisationKeywordFilter::fromKeywords('test');
        $result = $filter->applyToEloquent($query);

        $this->assertSame($query, $result);
    }
}
