<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Filters;

use AwardForce\Modules\Identity\Users\Search\Filters\UserKeywordFilter;

class OrganisationKeywordFilter extends UserKeywordFilter
{
    public function applyToEloquent($query)
    {
        return $query->where(function ($query) {
            $query->where('name', 'like', '%'.$this->keywords.'%')
                ->orWhere(function ($query) {
                    return parent::applyToEloquent($query);
                });
        });
    }

    public function applies(): bool
    {
        return ! empty($this->keywords);
    }
}
