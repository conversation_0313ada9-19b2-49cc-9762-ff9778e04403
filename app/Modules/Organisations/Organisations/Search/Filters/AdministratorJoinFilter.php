<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Filters;

use AwardForce\Library\Database\Eloquent\JoinAwareness;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;

class AdministratorJoinFilter implements ColumnatorFilter, SearchFilter
{
    use JoinAwareness;

    public function applies(): bool
    {
        return true;
    }

    public function applyToEloquent($query)
    {
        if ($this->tableJoined($query->getQuery(), 'users')) {
            return $query;
        }

        return $query
            ->leftJoin('users', 'users.id', '=', 'organisations.administrator_id');
    }
}
