<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Columns;

use HTML;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Columns\HasEagerLoadedRelations;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\IncludeFilterCollection;

class Administrator implements Column, HasEagerLoadedRelations
{
    public function title()
    {
        return trans('organisations.table.columns.administrator');
    }

    public function name(): string
    {
        return 'organisations.administrator';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return 'organisations.administrator_id';
    }

    public function value($record)
    {
        return $record->hasAdministrator() ? $record->administrator->fullName() : '—';
    }

    public function html($record)
    {
        if ($this->value($record) === '—') {
            return '—';
        }

        return new HtmlString(HTML::userLink($record->administrator));
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 20;
    }

    public function sortable(): bool
    {
        return true;
    }

    public function relations(): IncludeFilterCollection
    {
        return new IncludeFilterCollection([
            new IncludeFilter('administrator'),
        ]);
    }
}
