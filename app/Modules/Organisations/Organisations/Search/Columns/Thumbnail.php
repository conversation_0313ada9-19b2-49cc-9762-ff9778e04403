<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Columns;

use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Columns\HasEagerLoadedRelations;
use Platform\Search\Defaults;
use Platform\Search\Filters\IncludeFilterCollection;

class Thumbnail implements Column, HasEagerLoadedRelations
{
    public function __construct(private string $route)
    {
    }

    /**
     * Title for the column - used for headers.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function title()
    {
        return '';
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return 'logo.thumbnail';
    }

    /**
     * Returns the column's search filter dependencies.
     */
    public function dependencies(): Collection
    {
        return collect();
    }

    /**
     * Returns the name of the field in the query that should be present
     *
     * @return string|null
     */
    public function field()
    {
        return 'logo_id';
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    public function value($record)
    {
        return null;
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record)
    {
        return new HtmlString(view('organisation.search.thumbnail', [
            'thumbnail' => $record->logo?->thumbnail(),
            'alt' => trans('organisations.titles.logo'),
            'url' => route($this->route, ['organisation' => $record->id]),
        ]));
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return new Defaults('all');
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return true;
    }

    /**
     * Give columns with particularly important information a higher visibility priority.
     */
    public function priority(): int
    {
        return 1;
    }

    /**
     * Return true if the column can be used to order a result set.
     */
    public function sortable(): bool
    {
        return false;
    }

    public function relations(): IncludeFilterCollection
    {
        return (new IncludeFilterCollection)
            ->addIncludeFilter(['logo']);
    }
}
