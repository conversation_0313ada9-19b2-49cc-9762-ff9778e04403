<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Columns;

use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;

class EntryCount implements Column
{
    public function title()
    {
        return trans('organisations.table.columns.entry_count');
    }

    public function name(): string
    {
        return 'organisations.entry_count';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return null;
    }

    public function value($record)
    {
        //TODO: Implement
        return 11 .' (hardcoded)';
    }

    public function html($record)
    {
        return $this->value($record);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 13;
    }

    public function sortable(): bool
    {
        return true;
    }
}
