<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Columns;

use Illuminate\Support\Collection;
use Platform\Search\Column;
use Platform\Search\Defaults;

class Name implements Column
{
    public function __construct(private string $route = 'organisation.show')
    {
    }

    public function title()
    {
        return trans('organisations.titles.organisation');
    }

    public function name(): string
    {
        return 'organisations.name';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return 'organisations.name';
    }

    public function value($record)
    {
        return $record->name;
    }

    public function html($record)
    {
        return link_to_route($this->route, $record->name, ['organisation' => $record->id]);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 10;
    }

    public function sortable(): bool
    {
        return true;
    }
}
