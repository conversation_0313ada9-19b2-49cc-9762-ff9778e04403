<?php

namespace AwardForce\Modules\Organisations\Organisations\Search\Columns;

use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Search\Column;
use Platform\Search\Defaults;

class UserCount implements Column
{
    public function title()
    {
        return trans('organisations.table.columns.members_count');
    }

    public function name(): string
    {
        return 'organisations.members_count';
    }

    public function dependencies(): Collection
    {
        return collect();
    }

    public function field()
    {
        return DB::raw('(SELECT COUNT(*) FROM organisation_members
                    WHERE organisation_members.organisation_id = organisations.id
                    and exists(select null from memberships where memberships.id = organisation_members.membership_id and memberships.deleted_at is null)
                ) as members_count'
        );
    }

    public function value($record)
    {
        return $record->membersCount;
    }

    public function html($record)
    {
        return $this->value($record);
    }

    public function default(): Defaults
    {
        return new Defaults('all');
    }

    public function visible(): bool
    {
        return true;
    }

    public function fixed(): bool
    {
        return false;
    }

    public function priority(): int
    {
        return 12;
    }

    public function sortable(): bool
    {
        return true;
    }
}
