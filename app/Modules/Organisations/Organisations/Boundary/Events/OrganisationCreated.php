<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Events;

use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;

readonly class OrganisationCreated
{
    public function __construct(
        public OrganisationId $id,
        public string $name,
        public ?int $administratorId,
        public array $domains,
        public FieldValuesCollection $values
    ) {
    }
}
