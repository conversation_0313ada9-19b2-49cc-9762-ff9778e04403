<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary;

use AwardForce\Modules\Forms\Formables\Boundary\FormableAggregateRepository;
use AwardForce\Modules\Forms\Formables\Boundary\HandlesFormables;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;

class OrganisationFormableProvider implements HandlesFormables
{
    public function repository(): FormableAggregateRepository
    {
        return app(OrganisationRepository::class);
    }

    public function handles(Resource $resource): bool
    {
        return $resource->forOrganisation();
    }
}
