<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Projections;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrganisationFactory extends Factory
{
    public function definition(): array
    {
        return [
            'id' => OrganisationId::create(),
            'account_id' => current_account_id(),
            'season_id' => current_account()->activeSeason()->id,
            'administrator_id' => null,
            'domains' => [],
            'hashes' => [],
            'name' => str_random(10),
            'protected' => [],
            'values' => [],
        ];
    }
}
