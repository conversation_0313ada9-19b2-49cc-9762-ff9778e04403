<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Projections;

use AwardForce\Modules\Identity\Users\Models\User;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class OrganisationTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testOrganisationAdministrator()
    {
        $organisationWithOutAdministrator = Organisation::factory()->create(['administrator_id' => null]);
        $organisationWithAdministrator = Organisation::factory()->create(['administrator_id' => ($user = $this->muffin(User::class))->id]);

        $this->assertFalse($organisationWithOutAdministrator->hasAdministrator());

        $this->assertTrue($organisationWithAdministrator->hasAdministrator());
        $this->assertEquals($user->fullName(), $organisationWithAdministrator->administrator->fullName());
    }

    public function testItIsMine(): void
    {
        $this->setupUserWithRole('Program manager', true);

        $organisation = Organisation::factory()->create([
            'administrator_id' => $this->user->id,
        ]);

        $this->assertTrue($organisation->mine());
    }

    public function testItIsNotMine(): void
    {
        $this->setupUserWithRole('Program manager', true);
        $otherUser = $this->muffin(User::class);

        $organisation = Organisation::factory()->create([
            'administrator_id' => $otherUser->id,
        ]);

        $this->assertFalse($organisation->mine());
    }
}
