<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Services;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class OrganisationsTest extends BaseTestCase
{
    use Laravel;

    #[TestWith([true])]
    #[TestWith([false])]
    public function testItReturnsCorrectValueBasedOnOrganisationExistence(bool $exists)
    {
        $repository = m::mock(OrganisationProjectionRepository::class);
        $organisations = new Organisations($repository);
        $id = OrganisationId::create();

        $repository->shouldReceive('primary')->with($id)->once()->andReturnSelf();
        $repository->shouldReceive('count')->once()->andReturn((int) $exists);

        $this->assertEquals($exists, $organisations->exists($id));
    }

    public function testItReturnsTrueWhenAllIdsExist(): void
    {
        $ids = [
            OrganisationId::create(),
            OrganisationId::create(),
            OrganisationId::create(),
        ];

        $repository = m::mock(OrganisationProjectionRepository::class);
        $organisations = new Organisations($repository);

        $repository->shouldReceive('primary')->with(...$ids)->once()->andReturnSelf();
        $repository->shouldReceive('count')->once()->andReturn(count($ids));

        $this->assertTrue($organisations->exists(...$ids));
    }

    public function testItReturnsFalseWhenSomeIdsDoNotExist(): void
    {
        $ids = [
            OrganisationId::create(),
            OrganisationId::create(),
            OrganisationId::create(),
        ];

        $repository = m::mock(OrganisationProjectionRepository::class);
        $organisations = new Organisations($repository);

        $repository->shouldReceive('primary')->with(...$ids)->once()->andReturnSelf();
        $repository->shouldReceive('count')->once()->andReturn(count($ids) - 1);

        $this->assertFalse($organisations->exists(...$ids));
    }
}
