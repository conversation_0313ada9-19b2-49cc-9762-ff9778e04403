<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Services;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisations;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Mockery as m;
use Platform\Support\Values\Email;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class DomainLookupTest extends BaseTestCase
{
    use Laravel;

    public function testItCanSearchOrganisationsByDomainFromEmail(): void
    {
        $organisations = m::mock(OrganisationProjectionRepository::class);
        $lookup = new DomainLookup($organisations);
        $organisation = new Organisation;
        $organisation->id = OrganisationId::create();

        $organisations->shouldReceive('fields')
            ->with(['id', 'name'])
            ->once()
            ->andReturnSelf();
        $organisations->shouldReceive('domain')
            ->with('example.com')
            ->once()
            ->andReturnSelf();
        $organisations->shouldReceive('get')
            ->once()
            ->andReturn(new Organisations([$organisation]));

        $result = $lookup->forEmail(new Email('<EMAIL>'));

        $this->assertCount(1, $result);
        $this->assertEquals($organisation->id, $result->first()->id);
    }
}
