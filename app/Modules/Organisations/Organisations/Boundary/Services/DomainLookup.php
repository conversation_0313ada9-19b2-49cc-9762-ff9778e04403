<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Services;

use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisations;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Platform\Support\Values\Email;

class DomainLookup
{
    public function __construct(private OrganisationProjectionRepository $organisations)
    {
    }

    public function forEmail(Email $email): Organisations
    {
        $domain = explode('@', $email)[1];

        return $this->organisations->fields(['id', 'name'])
            ->domain($domain)
            ->get();
    }
}
