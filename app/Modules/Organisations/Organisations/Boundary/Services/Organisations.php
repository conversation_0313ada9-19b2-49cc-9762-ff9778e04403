<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Services;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;

class Organisations
{
    public function __construct(private OrganisationProjectionRepository $organisations)
    {
    }

    public function exists(OrganisationId ...$organisationId): bool
    {
        return $this->organisations->primary(...$organisationId)->count() === count($organisationId);
    }
}
