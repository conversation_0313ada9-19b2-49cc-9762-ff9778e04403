<?php

namespace AwardForce\Modules\Organisations\Organisations;

use AwardForce\Library\Database\EventSourcing\AccountableMessageDecorator;
use AwardForce\Library\Database\EventSourcing\EventsTableSchema;
use AwardForce\Library\Database\EventSourcing\SnowflakeEncoder;
use AwardForce\Library\EventSourcing\SynchronousProjectionMessageDispatcher;
use AwardForce\Library\Providers\ModuleServiceProvider;
use AwardForce\Modules\Forms\Formables\Boundary\FormableProvider;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationFormableProvider;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation as OrganisationProjection;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\OrganisationMessageDispatcher;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\EloquentOrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\MysqlOrganisationRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationMessageRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Organisation;
use EventSauce\EventSourcing\DefaultHeadersDecorator;
use EventSauce\EventSourcing\EventSourcedAggregateRootRepository;
use EventSauce\EventSourcing\MessageDecoratorChain;
use EventSauce\EventSourcing\Serialization\ConstructingMessageSerializer;
use EventSauce\EventSourcing\Serialization\PayloadSerializerSupportingObjectMapperAndSerializablePayload;
use EventSauce\EventSourcing\Snapshotting\ConstructingAggregateRootRepositoryWithSnapshotting;
use EventSauce\EventSourcing\Snapshotting\SnapshotRepository;
use EventSauce\IdEncoding\BinaryUuidIdEncoder;
use EventSauce\MessageRepository\IlluminateMessageRepository\IlluminateMessageRepository;

class OrganisationsServiceProvider extends ModuleServiceProvider
{
    protected array $repositories = [
        OrganisationProjectionRepository::class => EloquentOrganisationProjectionRepository::class,
    ];

    public function register(): void
    {
        parent::register();
        $this->registerMessageRepository();
        $this->registerAggregateRepository();
        $this->registerMessageDispatcher();
    }

    public function boot(): void
    {
        parent::boot();

        $this->bootFormableProvider();
        $this->bootProjectors();
    }

    private function registerAggregateRepository(): void
    {
        $this->app->scoped(OrganisationRepository::class, function ($app) {
            return new MysqlOrganisationRepository(
                new ConstructingAggregateRootRepositoryWithSnapshotting(
                    Organisation::class,
                    $app->make(OrganisationMessageRepository::class),
                    $app->make(SnapshotRepository::class),
                    $app->make(EventSourcedAggregateRootRepository::class, [
                        'aggregateRootClassName' => Organisation::class,
                        'messageRepository' => $app->make(OrganisationMessageRepository::class),
                        'decorator' => new MessageDecoratorChain(
                            new DefaultHeadersDecorator,
                            new AccountableMessageDecorator,
                        ),
                        'dispatcher' => $app->make(OrganisationMessageDispatcher::class),
                    ]),
                )
            );
        });
    }

    private function registerMessageRepository(): void
    {
        $this->app->scoped(OrganisationMessageRepository::class, function ($app) {
            return $app->make(IlluminateMessageRepository::class, [
                'tableName' => 'organisation_events',
                'tableSchema' => new EventsTableSchema,
                'serializer' => new ConstructingMessageSerializer(
                    payloadSerializer: new PayloadSerializerSupportingObjectMapperAndSerializablePayload()
                ),
                'aggregateRootIdEncoder' => new SnowflakeEncoder,
                'eventIdEncoder' => new BinaryUuidIdEncoder,
            ]);
        });
    }

    private function registerMessageDispatcher(): void
    {
        $this->app->scoped(OrganisationMessageDispatcher::class, fn() => new SynchronousProjectionMessageDispatcher);
    }

    private function bootFormableProvider(): void
    {
        $this->app->make(FormableProvider::class)->registerProvider(new OrganisationFormableProvider);
    }

    private function bootProjectors(): void
    {
        $this->app->make(OrganisationMessageDispatcher::class)->registerProjection(new OrganisationProjection);
    }
}
