<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Middleware;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Illuminate\Http\Request;
use Illuminate\Routing\Route;
use Mockery as m;
use Mockery\MockInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class IsMineTest extends BaseTestCase
{
    use Laravel;

    private MemberProjectionRepository|MockInterface $members;
    private IsMine $middleware;

    protected function init(): void
    {
        $this->members = m::mock(MemberProjectionRepository::class);
        $this->middleware = new IsMine($this->members);
        $membership = new Membership;
        $membership->id = 1;
        $user = m::mock(User::class);
        Consumer::shouldReceive('user')
            ->andReturn($user);

        $user->shouldReceive('getAttribute')
            ->with('currentMembership')
            ->andReturn($membership);
    }

    public function testHandleReturns404WhenOrganisationNotMine(): void
    {
        $request = new Request();
        $route = m::mock(Route::class);
        $request->setRouteResolver(fn() => $route);

        $organisationId = OrganisationId::create();

        $this->members->shouldReceive('membership')
            ->andReturnSelf()
            ->shouldReceive('organisation')
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn(false);

        $route->shouldReceive('parameter')->with('organisation')->andReturn($organisationId->toString());

        $this->expectException(NotFoundHttpException::class);

        $response = $this->middleware->handle($request, fn() => null);

        $this->assertEquals(404, $response->getStatusCode());
    }

    public function testHandleContinuesWhenOrganisationIsMine(): void
    {
        $membership = new Membership;
        $membership->id = 1;
        Consumer::shouldReceive('user->currentMembership')
            ->andReturn($membership);
        $request = new Request();
        $route = m::mock(Route::class);
        $request->setRouteResolver(fn() => $route);

        $organisationId = OrganisationId::create();

        $this->members->shouldReceive('membership')
            ->andReturnSelf()
            ->shouldReceive('organisation')
            ->andReturnSelf()
            ->shouldReceive('exists')
            ->andReturn(true);

        $route->shouldReceive('parameter')->with('organisation')->andReturn($organisationId->toString());

        $next = fn($req) => 'continue';

        $response = $this->middleware->handle($request, $next);

        $this->assertEquals('continue', $response);
    }
}
