<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Middleware;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Illuminate\Http\Request;

class MyOrganisation
{
    public function __construct(private OrganisationProjectionRepository $organisations)
    {
    }

    public function handle(Request $request, \Closure $next)
    {
        if ($this->myOrganisationsCount() !== 1) {
            return $next($request);
        }

        return redirect()->route('my-organisation.show', [
            'organisation' => $this->myOrganisationId()->toString(),
        ]);
    }

    private function myOrganisationsCount(): int
    {
        return $this->organisations
            ->membership(Consumer::user()->currentMembership->id)
            ->count();
    }

    private function myOrganisationId(): OrganisationId
    {
        return $this->organisations
            ->membership(Consumer::user()->currentMembership->id)
            ->pluck('id')
            ->first();
    }
}
