<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Middleware;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Illuminate\Http\Request;
use Mockery as m;
use Mockery\MockInterface;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class MyOrganisationTest extends BaseTestCase
{
    use Laravel;

    private OrganisationProjectionRepository|MockInterface $organisations;
    private $middleware;

    protected function init(): void
    {
        $this->organisations = m::mock(OrganisationProjectionRepository::class);
        $this->middleware = new MyOrganisation($this->organisations);
        $membership = new Membership;
        $membership->id = 1;
        $user = m::mock(User::class);
        Consumer::shouldReceive('user')
            ->andReturn($user);

        $user->shouldReceive('getAttribute')
            ->with('currentMembership')
            ->andReturn($membership);
    }

    public function testItRedirectsToOrganisationWhenSingleOrganisation(): void
    {
        $request = new Request();
        $next = fn($req) => $req;

        $this->organisations->shouldReceive('membership')
            ->with(Consumer::user()->currentMembership->id)
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(1)
            ->shouldReceive('pluck')
            ->with('id')
            ->andReturn(collect([$organisationId = OrganisationId::create()]))
            ->shouldReceive('first');

        $response = $this->middleware->handle($request, $next);

        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertEquals(route('my-organisation.show', ['organisation' => $organisationId->toString()]), $response->getTargetUrl());
    }

    public function testItProceedsWhenMultipleOrganisations(): void
    {
        $request = new Request();
        $next = fn($req) => $req;

        $this->organisations->shouldReceive('membership')
            ->with(Consumer::user()->currentMembership->id)
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(2);

        $response = $this->middleware->handle($request, $next);

        $this->assertSame($request, $response);
    }

    public function testProceedsWhenNoOrganisation(): void
    {
        $request = new Request();
        $next = fn($req) => $req;

        $this->organisations->shouldReceive('membership')
            ->with(Consumer::user()->currentMembership->id)
            ->andReturnSelf()
            ->shouldReceive('count')
            ->andReturn(0);

        $response = $this->middleware->handle($request, $next);

        $this->assertSame($request, $response);
    }
}
