<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Middleware;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;

class IsMine
{
    public function __construct(private MemberProjectionRepository $members)
    {
    }

    public function handle($request, \Closure $next)
    {
        if (! $this->isMine(OrganisationId::fromString($request->route()->parameter('organisation')))) {
            return abort(404);
        }

        return $next($request);
    }

    private function isMine(OrganisationId $organisationId): bool
    {
        return $this->members
            ->membership(Consumer::user()->currentMembership->id)
            ->organisation($organisationId)
            ->exists();
    }
}
