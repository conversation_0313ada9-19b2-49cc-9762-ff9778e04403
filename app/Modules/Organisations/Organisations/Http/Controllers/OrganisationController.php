<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Controllers;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Bus\CreateOrganisation as CreateOrganisationCommand;
use AwardForce\Modules\Organisations\Organisations\Domain\Bus\DeleteLogo;
use AwardForce\Modules\Organisations\Organisations\Domain\Bus\DeleteOrganisations;
use AwardForce\Modules\Organisations\Organisations\Domain\Bus\UndeleteOrganisations;
use AwardForce\Modules\Organisations\Organisations\Domain\Bus\UpdateLogo;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use AwardForce\Modules\Organisations\Organisations\Http\Requests\CreateOrganisation;
use AwardForce\Modules\Organisations\Organisations\Http\Views\EditOrganisation;
use AwardForce\Modules\Organisations\Organisations\Http\Views\NewOrganisation;
use AwardForce\Modules\Organisations\Organisations\Http\Views\Organisations;
use AwardForce\Modules\Organisations\Organisations\Http\Views\ViewOrganisation;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Platform\Http\Controller;

class OrganisationController extends Controller
{
    public static string $resource = 'Organisations';

    use DispatchesJobs;

    public function __construct(private OrganisationProjectionRepository $organisations)
    {
    }

    public function index(Organisations $view)
    {
        return $this->respond('organisation.index', $view);
    }

    public function show(ViewOrganisation $view)
    {
        return $this->respond('organisation.view', $view);
    }

    public function new(NewOrganisation $view)
    {
        return $this->respond('organisation.new', $view);
    }

    public function create(CreateOrganisation $request)
    {
        $this->dispatch(new CreateOrganisationCommand(
            $request->get('name'),
            $request->administratorId(),
            array_filter($request->array('domains')),
            $request->get('values', []),
            CreatedBy::User,
            consumer_id()
        ));

        return redirect()->route('organisation.index');
    }

    public function edit(EditOrganisation $view)
    {
        return $this->respond('organisation.edit', $view);
    }

    public function delete(Request $request)
    {
        $this->dispatch(new DeleteOrganisations($request->array('selected'), app(Manager::class)));

        return redirect()->route('organisation.index');
    }

    public function undelete(Request $request)
    {
        $this->dispatch(new UndeleteOrganisations($request->array('selected'), app(OrganisationProjectionRepository::class)));

        return redirect()->route('organisation.index');
    }

    public function updateLogo(Request $request, string $organisation)
    {
        $this->dispatch(new UpdateLogo(OrganisationId::fromString($organisation), $request->integer('file')));

        return response()->noContent();
    }

    public function deleteLogo(string $organisation)
    {
        $this->dispatch(new DeleteLogo(OrganisationId::fromString($organisation)));

        return response()->noContent();
    }

    public function autocomplete(Request $request): JsonResponse
    {
        $results = $this->organisations->searchName($request->string('keywords'))
            ->fields(['id', 'name'])
            ->get();

        return response()->json($results);
    }
}
