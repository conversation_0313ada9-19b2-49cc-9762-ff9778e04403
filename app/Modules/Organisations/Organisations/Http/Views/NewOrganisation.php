<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Forms\Fields\View\VueFields;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Illuminate\Http\Request;
use Platform\View\View;

class NewOrganisation extends View
{
    use VueFields;

    public function __construct(
        private Request $request,
    ) {
        VueData::registerTranslations([
            'organisations.form.domains.add',
            'validation.required',
        ]);
    }

    public function organisation(): Organisation
    {
        return new Organisation;
    }

}
