<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Modules\Search\Services\ColumnatorFactory;
use Illuminate\Contracts\Pagination\Paginator;
use Illuminate\Http\Request;
use Platform\Search\Columnator;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;

class MyOrganisationList extends View
{
    public function __construct(
        private Request $request,
        private ColumnatorFactory $columnators
    ) {
    }

    public function organisations(): Paginator
    {
        $search = new ColumnatorSearch($this->columnator);

        return translate($search->search());
    }

    public function columnator(): Columnator
    {
        return $this->columnators->forArea($this->area(), $this->request->all());
    }

    public function area(): string
    {
        return 'my-organisations.search';
    }

    public function organisationsIds(): array
    {
        return $this->organisations->pluck('id')->map->toString()->toArray();
    }
}
