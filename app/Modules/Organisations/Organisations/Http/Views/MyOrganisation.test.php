<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Modules\Organisations\Members\Boundary\Projections\JoinMethod;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class MyOrganisationTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanLoadMyOrganisation(): void
    {
        $this->setupUserWithRole('Program manager', true);

        $organisation = Organisation::factory()->create();

        request()->setRouteResolver(function () use ($organisation) {
            $router = m::mock('router');
            $router->shouldReceive('parameter')
                ->once()
                ->with('organisation', null)
                ->andReturn($organisation->id->toString());

            return $router;
        });
        $view = app(MyOrganisation::class);

        $result = $view->organisation();

        $this->assertEquals($organisation->id, $result->id);
    }

    public function testItCanCountMembers(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        $this->setupUserWithRole('Program manager', true);

        $organisation = Organisation::factory()->create();
        $membership = $user->currentMembership;

        Member::factory()->create([
            'organisation_id' => $organisation->id,
            'membership_id' => $membership->id,
            'join_email' => $user->email,
            'join_method' => JoinMethod::Registered,
        ]);

        Member::factory()->create([
            'organisation_id' => $organisation->id,
            'membership_id' => $this->user->currentMembership->id,
            'join_email' => $this->user->email,
            'join_method' => JoinMethod::Added,
        ]);

        request()->setRouteResolver(function () use ($organisation) {
            $router = m::mock('router');
            $router->shouldReceive('parameter')
                ->once()
                ->with('organisation', null)
                ->andReturn($organisation->id->toString());

            return $router;
        });
        $view = app(MyOrganisation::class);

        $result = $view->memberCount();

        $this->assertEquals(2, $result);
    }
}
