<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\OrganisationLogoUploadValidator;
use Platform\View\View;

class MyOrganisation extends View
{
    use Uploadable;

    public function __construct(
        private OrganisationProjectionRepository $organisations,
        private OrganisationLogoUploadValidator $validator,
    ) {
        VueData::registerTranslations([
            'organisations.form',
            'organisations.table.columns',
            'organisations.my',
            'organisations.titles',
            'shared.overview',
            'buttons.cancel',
            'buttons.done',
            'buttons.delete',
            'buttons.delete_permanently',
            'buttons.edit',
            'buttons.save',
            'files.buttons.drag_and_drop',
            'files.status',
            'files.buttons.single',
            'miscellaneous.alerts.delete.item',
            'miscellaneous.search.or',
        ]);

        VueData::registerRoutes([
            'organisation.logo.delete',
            'organisation.logo.update',
        ]);
    }

    public function organisation(): Organisation
    {
        return $this->organisations
            ->fields(['id', 'name', 'administrator_id', 'domains', 'logo_id'])
            ->primary(request()->route('organisation'))
            ->first();
    }

    public function memberCount(): int
    {
        return $this->organisation
            ->members()
            ->count();
    }

    public function uploadOptions(): array
    {
        return $this->organisation->mine() ? $this->validator->setupUploader($this->setupUploader())->setMaxFileSize(1)->options() : [];
    }
}
