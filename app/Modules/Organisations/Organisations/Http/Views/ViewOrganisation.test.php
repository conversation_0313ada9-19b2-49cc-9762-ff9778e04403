<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\OrganisationLogoUploadValidator;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class ViewOrganisationTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function init()
    {
        $session = m::instanceMock(app('session'));
        $session->shouldReceive('token')->andReturn(Str::random());
        app()->instance('session', $session);
    }

    public function testItCanLoadOrganisationFromRouteParameter(): void
    {
        $organisation = Organisation::factory()->create();
        $request = m::mock(Request::class);
        $request->shouldReceive('route')->with('organisation')->andReturn($organisation->id);

        $view = app(ViewOrganisation::class, ['request' => $request]);

        $resolved = $view->organisation();

        $this->assertEquals($organisation->id, $resolved->id);
    }

    public function testUploadOptionContainsResource()
    {
        $organisation = Organisation::factory()->create();
        $request = m::mock(Request::class);
        $request->shouldReceive('route')->with('organisation')->andReturn($organisation->id);

        $view = app(ViewOrganisation::class, ['request' => $request]);

        $this->assertEquals(File::RESOURCE_ORGANISATION_LOGO, ($uploadOptions = $view->uploadOptions())['resource']);
    }

    public function testUploadOptionsContainsAllowedTypes()
    {
        $validator = app(OrganisationLogoUploadValidator::class);
        $organisation = Organisation::factory()->create();
        $request = m::mock(Request::class);
        $request->shouldReceive('route')->with('organisation')->andReturn($organisation->id);

        $view = app(ViewOrganisation::class, ['request' => $request]);

        $this->assertEquals(File::RESOURCE_ORGANISATION_LOGO, ($uploadOptions = $view->uploadOptions())['resource']);
        $this->assertCount(1, $types = Arr::get($uploadOptions, 's3.filters.mime_types'));
        $this->assertEquals('Images', $types[0]['title']);
        $this->assertEquals(implode(',', $validator->allowedTypes()['images']), $types[0]['extensions']);
    }

    public function testOrganisationLogoSize()
    {
        $file = new File;
        $file->file = 'file.png';
        $file->resource = File::RESOURCE_ORGANISATION_LOGO;
        $file->save();
        $organisation = Organisation::factory()->create(['logo_id' => $file->id]);
        $request = m::mock(Request::class);
        $request->shouldReceive('route')->with('organisation')->andReturn($organisation->id);

        $view = app(ViewOrganisation::class, ['request' => $request]);

        $logo = $view->organisationLogo();

        $this->assertArrayHasKey('image', $logo);
        $this->assertStringContainsString('w=80', $logo['image']);
        $this->assertStringContainsString('h=80', $logo['image']);
    }

    public function testOrganisationLogoShouldBeEmptyWhenNoFileIsAssociated()
    {
        $organisation = Organisation::factory()->create();
        $request = m::mock(Request::class);
        $request->shouldReceive('route')->with('organisation')->andReturn($organisation->id);

        $view = app(ViewOrganisation::class, ['request' => $request]);

        $logo = $view->organisationLogo();

        $this->assertEmpty($logo);
    }

    public function testUploadOptionsMaxFileSizeShouldBeTheSameAsAccountMaxFileSize()
    {
        current_account()->maxFileSize = $configuredMaxSize = 133;
        $organisation = Organisation::factory()->create();
        $request = m::mock(Request::class);
        $request->shouldReceive('route')->with('organisation')->andReturn($organisation->id);

        $view = app(ViewOrganisation::class, ['request' => $request]);
        $uploadOptions = $view->uploadOptions();

        $this->assertArrayHasKey('maxFileSize', $uploadOptions);
        $this->assertEquals($configuredMaxSize, $uploadOptions['maxFileSize']);
        $this->assertArrayHasKey('s3', $uploadOptions);
        $this->assertArrayHasKey('filters', $uploadOptions['s3']);
        $this->assertArrayHasKey('max_file_size', $uploadOptions['s3']['filters']);
        $this->assertEquals(($configuredMaxSize * 1024).'KB', $uploadOptions['s3']['filters']['max_file_size']);
    }

    public function testUploadOptionsMaxFileSizeShouldNotBePresentIfAccountDoesNotHaveItConfigured()
    {
        current_account()->maxFileSize = 0;
        $organisation = Organisation::factory()->create();
        $request = m::mock(Request::class);
        $request->shouldReceive('route')->with('organisation')->andReturn($organisation->id);

        $view = app(ViewOrganisation::class, ['request' => $request]);
        $uploadOptions = $view->uploadOptions();

        $this->assertArrayNotHasKey('maxFileSize', $uploadOptions);
        $this->assertArrayHasKey('s3', $uploadOptions);
        $this->assertArrayHasKey('filters', $uploadOptions['s3']);
        $this->assertArrayNotHasKey('max_file_size', $uploadOptions['s3']['filters']);
    }
}
