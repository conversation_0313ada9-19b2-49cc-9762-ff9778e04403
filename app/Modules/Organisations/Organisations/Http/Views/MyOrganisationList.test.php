<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class MyOrganisationListTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanLoadMyOrganisations(): void
    {
        $this->setupUserWithRole('Program manager', true);

        $organisations = Organisation::factory(3)->create();
        Member::factory()->create([
            'organisation_id' => $organisations[0]->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);
        Member::factory()->create([
            'organisation_id' => $organisations[1]->id,
            'membership_id' => $this->user->currentMembership->id,
        ]);

        $myOrganisations = app(MyOrganisationList::class)->organisations();

        $this->assertCount(2, $myOrganisations);
        $this->assertTrue($myOrganisations->pluck('id')->contains($organisations[0]->id));
        $this->assertTrue($myOrganisations->pluck('id')->contains($organisations[1]->id));
    }
}
