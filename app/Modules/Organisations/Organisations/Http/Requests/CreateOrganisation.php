<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Requests;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Api\V2\Rules\IsSlug;
use AwardForce\Modules\Api\V2\Rules\SlugExists;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Http\Validation\ValidatesFields;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;

class CreateOrganisation extends FormRequest
{
    use ValidatesFields {
        getFields as getValidationFields;
    }

    public function __construct(private FieldRepository $fields)
    {
    }

    protected function resource()
    {
        return Field::RESOURCE_ORGANISATIONS;
    }

    public function messages()
    {
        return array_merge(parent::messages(), $this->fieldMessages());
    }

    public function rules()
    {
        $rules = [
            'name' => 'required',
            'administrator' => [
                'bail',
                'sometimes',
                new IsSlug([$this->administrator]),
                new SlugExists([$this->administrator], app(UserRepository::class)),
            ],
            'values' => 'sometimes|required|array',
            'domains' => 'sometimes|array',
            'domains.*' => ['domain'],
        ];

        return array_merge(
            $rules,
            // Field values to be implemented later
            // $this->fieldRules()
        );
    }

    protected function getFields($resource)
    {
        return $this->fields->getByResource(Field::RESOURCE_ORGANISATIONS);
    }

    public function administratorId(): ?int
    {
        return $this->string('administrator') ? app(UserRepository::class)->getBySlug($this->string('administrator'))?->id : null;
    }
}
