<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Platform\Events\EventDispatcher;

class DeleteOrganisationsHandler
{
    use EventDispatcher;

    public function __construct(private Manager $organisations)
    {
    }

    public function handle(DeleteOrganisations $command): void
    {
        foreach ($command->ids as $organisationId) {
            $this->organisations->delete(OrganisationId::fromInteger($organisationId));
        }

        $this->dispatchAll($this->organisations->releaseEvents());
    }
}
