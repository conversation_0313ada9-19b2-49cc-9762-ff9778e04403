<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Platform\Events\EventDispatcher;

class DeleteLogoHandler
{
    use EventDispatcher;

    public function __construct(private Manager $organisations)
    {
    }

    public function handle(DeleteLogo $command)
    {
        $this->organisations->deleteLogo($command->organisationId);

        $this->dispatchAll($this->organisations->releaseEvents());
    }
}
