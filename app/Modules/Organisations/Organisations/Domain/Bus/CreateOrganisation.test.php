<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Faker\Factory;
use GuzzleHttp\Promise\Create;
use PHPUnit\Framework\Attributes\TestWith;
use Platform\Events\EventDispatcher;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class CreateOrganisationTest extends BaseTestCase
{
    use Database;
    use EventDispatcher;
    use Laravel;

    private OrganisationProjectionRepository $organisationRepository;
    private UserRepository $userRepository;
    private ValuesService $valuesService;
    private CreateOrganisationHandler $handler;

    public function init()
    {
        $this->organisationRepository = app(OrganisationProjectionRepository::class);
        $this->userRepository = app(UserRepository::class);
        $this->valuesService = app(ValuesService::class);
        $this->handler = app(CreateOrganisationHandler::class);
    }

    public function testCreateOrganisation(): void
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $command = new CreateOrganisation(
            $organisationName,
            null,
            [],
            []
        );

        $this->handler->handle($command);

        $organisation = $this->organisationRepository->getByName($organisationName)->first();

        $this->assertEquals($organisationName, $organisation->name);
        $this->assertEmpty($organisation->values);
        $this->assertEmpty($organisation->domains);
        $this->assertNull($organisation->administratorId);
        $this->assertEquals(CreatedBy::System, $organisation->createdBy);
        $this->assertNull($organisation->createdByUserId);
    }

    public function testCreateOrganisationWithAdministrator(): void
    {
        $organisationAdministrator = $this->setupUserWithRole('entrant');
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $command = new CreateOrganisation(
            $organisationName,
            $organisationAdministrator->id,
            [],
            []
        );

        $this->handler->handle($command);

        $organisation = $this->organisationRepository->getByName($organisationName)->first();

        $this->assertEquals($organisationAdministrator->id, $organisation->administratorId);

        $this->assertEquals($organisationAdministrator->fullName(), $organisation->administrator->fullName());
    }

    public function testCreateOrganisationWithDomains()
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $command = new CreateOrganisation(
            $organisationName,
            null,
            $allowedDomains = ['domainOne.com', 'domainTwo.com'],
            []
        );

        $this->handler->handle($command);

        $organisation = $this->organisationRepository->getByName($organisationName)->first();

        $this->assertEquals($allowedDomains, $organisation->domains);
    }

    public function testOrganisationWasCreatedDispatched()
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $command = new CreateOrganisation(
            $organisationName,
            null,
            [],
            []
        );

        $this->handler->handle($command);
    }

    #[TestWith(['api'])]
    #[TestWith(['import'])]
    #[TestWith(['system'])]
    #[TestWith(['user'])]
    public function testItHandlesCreatedBy(string $value): void
    {
        $user = $this->muffin(User::class);
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $command = new CreateOrganisation(
            $organisationName,
            null,
            [],
            [],
            $createdBy = CreatedBy::from($value),
            $user->id
        );

        $this->handler->handle($command);

        $organisation = $this->organisationRepository->getByName($organisationName)->first();

        $this->assertEquals($createdBy, $organisation->createdBy);
        $expectedUserId = $createdBy->value === 'user' ? $user->id : null;
        $this->assertEquals($expectedUserId, $organisation->createdByUserId);
    }
    //    public function testItAddsFieldValues(): void
    //    {
    //        $fields = $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_ORGANISATIONS, 'type' => 'text']);
    //        $organisationName = 'Test Organisation - '.Factory::create()->word;
    //        $fieldValues = [
    //            (string) $fields[0]->slug => 'valueOne',
    //            (string) $fields[1]->slug => 'valueTwo',
    //        ];
    //
    //        $command = new CreateOrganisation(
    //            $organisationName,
    //            null,
    //            [],
    //            $fieldValues
    //        );
    //
    //        $this->handler->handle($command);
    //
    //        $organisation = $this->organisationRepository->getByName($organisationName)->first();
    //
    //        $this->assertEquals($fieldValues, $organisation->values);
    //    }
}
