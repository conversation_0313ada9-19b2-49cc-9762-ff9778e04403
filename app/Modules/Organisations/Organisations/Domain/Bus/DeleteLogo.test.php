<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class DeleteLogoTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private OrganisationProjectionRepository $organisationRepository;
    private Manager|m\MockInterface $manager;
    private DeleteLogoHandler|m\MockInterface $handler;

    public function init()
    {
        $this->organisationRepository = app(OrganisationProjectionRepository::class);
        $this->manager = m::mock(Manager::class);
        $this->handler = new DeleteLogoHandler($this->manager);
    }

    public function testItShouldDeleteOrganisationLogo()
    {
        $this->manager->shouldReceive('deleteLogo')
            ->once()
            ->with(m::type(OrganisationId::class));
        $this->manager->shouldReceive('releaseEvents')
            ->once()
            ->andReturn([]);

        $command = new DeleteLogo((Organisation::factory()->create())->id);
        $this->handler->handle($command);
    }
}
