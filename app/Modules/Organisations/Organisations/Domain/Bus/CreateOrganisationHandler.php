<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Forms\FieldValues\RequestToFieldValues;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Platform\Events\EventDispatcher;

class CreateOrganisationHandler
{
    use EventDispatcher;

    public function __construct(private Manager $organisations, private RequestToFieldValues $requestToFieldValues)
    {
    }

    public function handle(CreateOrganisation $command): void
    {
        $fieldValues = $this->requestToFieldValues->fromRequest($command->values);
        //        Pipeline
        //        $requestData = Pipeline::send($command->toArray())->through([
        //            function ($data, $next) {
        //                return $next($data);
        //            },
        //        ])->thenReturn();
        $this->organisations->create(
            $command->name,
            $command->administratorId,
            $command->domains,
            $fieldValues,
            $command->createdBy,
            $command->createdByUserId
        );
        $this->dispatch($this->organisations->releaseEvents());
    }
}
