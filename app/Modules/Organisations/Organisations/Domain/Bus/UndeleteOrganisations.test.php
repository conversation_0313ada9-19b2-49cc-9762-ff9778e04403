<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationUndeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation as OrganisationProjection;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Organisation;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class UndeleteOrganisationsTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanUndeleteOrganisations(): void
    {
        $aggregates = [];
        for ($i = 0; $i < 3; $i++) {
            $aggregates[$i] = Organisation::new();
            app(OrganisationRepository::class)->save($aggregates[$i]);
        }

        $projections = array_map(
            fn($aggregate) => OrganisationProjection::factory()
                ->create(['id' => $aggregate->aggregateRootId(), 'deleted_at' => now()]),
            $aggregates
        );

        $command = new UndeleteOrganisations([$projections[0]->id->toInteger(), $projections[1]->id->toInteger()]);

        $manager = app(Manager::class);
        $handler = new UndeleteOrganisationsHandler($manager);

        Event::fake(OrganisationUndeleted::class);
        $handler->handle($command);
        Event::assertDispatchedTimes(OrganisationUndeleted::class, 2);

        $results = app(OrganisationProjectionRepository::class)->getAll();

        $this->assertCount(2, $results);
        $this->assertEqualsCanonicalizing(
            [$projections[0]->id->toInteger(), $projections[1]->id->toInteger()],
            $results->pluck('id')->map->toInteger()->toArray()
        );
    }
}
