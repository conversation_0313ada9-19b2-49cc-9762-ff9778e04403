<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Platform\Events\EventDispatcher;

class UpdateLogoHandler
{
    use EventDispatcher;

    public function __construct(private Manager $organisations)
    {
    }

    public function handle(UpdateLogo $command)
    {
        $this->organisations->updateLogo($command->organisationId, $command->logoId);

        $this->dispatchAll($this->organisations->releaseEvents());
    }
}
