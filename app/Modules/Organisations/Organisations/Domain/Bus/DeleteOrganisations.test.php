<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationDeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation as OrganisationProjection;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class DeleteOrganisationsTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanDeleteOrganisations(): void
    {
        $aggregates = [];
        for ($i = 0; $i < 3; $i++) {
            $aggregates[$i] = \AwardForce\Modules\Organisations\Organisations\Domain\Organisation::new();
            app(OrganisationRepository::class)->save($aggregates[$i]);
        }

        $projections = array_map(fn($aggregate) => OrganisationProjection::factory()->create(['id' => $aggregate->aggregateRootId()]), $aggregates);

        $command = new DeleteOrganisations([$projections[0]->id->toInteger(), $projections[1]->id->toInteger()]);

        $manager = app(Manager::class);
        $handler = new DeleteOrganisationsHandler($manager);

        Event::fake(OrganisationDeleted::class);
        $handler->handle($command);
        Event::assertDispatchedTimes(OrganisationDeleted::class, 2);

        $results = app(OrganisationProjectionRepository::class)->getAll();

        $this->assertCount(1, $results);
        $this->assertEquals($projections[2]->id, $results->first()->id);
    }
}
