<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class UpdateLogoTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private OrganisationProjectionRepository $organisationRepository;
    private Manager|m\MockInterface $manager;
    private UpdateLogoHandler|m\MockInterface $handler;

    public function init()
    {
        $this->organisationRepository = app(OrganisationProjectionRepository::class);
        $this->manager = m::mock(Manager::class);
        $this->handler = new UpdateLogoHandler($this->manager);
    }

    public function testUpdatesLogo()
    {
        $this->manager->shouldReceive('updateLogo')
            ->once()
            ->with(m::type(OrganisationId::class), m::type('int'));
        $this->manager->shouldReceive('releaseEvents')
            ->once()
            ->andReturn([]);

        $command = new UpdateLogo((Organisation::factory()->create())->id, 321);
        $this->handler->handle($command);
    }
}
