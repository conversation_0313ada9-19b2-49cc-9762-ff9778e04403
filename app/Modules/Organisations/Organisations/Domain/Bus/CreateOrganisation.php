<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;

readonly class CreateOrganisation
{
    public function __construct(
        public string $name,
        public ?int $administratorId,
        public array $domains,
        public array $values,
        public CreatedBy $createdBy = CreatedBy::System,
        public ?int $createdByUserId = null
    ) {
    }
}
