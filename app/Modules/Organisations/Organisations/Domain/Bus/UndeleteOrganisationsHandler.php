<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\Manager;
use Platform\Events\EventDispatcher;

class UndeleteOrganisationsHandler
{
    use EventDispatcher;

    public function __construct(private Manager $organisations)
    {
    }

    public function handle(UndeleteOrganisations $command): void
    {
        foreach ($command->ids as $organisationId) {
            $this->organisations->undelete(OrganisationId::fromInteger($organisationId));
        }

        $this->dispatchAll($this->organisations->releaseEvents());
    }
}
