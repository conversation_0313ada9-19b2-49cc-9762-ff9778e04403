<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Services;

use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\ResourceValidator;

class OrganisationLogoUploadValidator extends ResourceValidator
{
    public function resource(): string
    {
        return File::RESOURCE_ORGANISATION_LOGO;
    }

    public function allowedTypes(): array
    {
        return ['images' => ['jpg', 'jpeg', 'png']];
    }
}
