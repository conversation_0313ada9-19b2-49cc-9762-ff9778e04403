<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Events;

use AwardForce\Library\EventSourcing\SourcedEvent;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;

readonly class Created implements SourcedEvent
{
    public function __construct(
        public string $name,
        public ?int $administratorId,
        public array $domains,
        public int $seasonId,
        public CreatedBy $createdBy,
        public ?int $createdByUserId
    ) {
    }
}
