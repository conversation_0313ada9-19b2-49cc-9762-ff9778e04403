<?php

namespace AwardForce\Modules\Organisations\Members\Boundary\Events;

use AwardForce\Modules\Organisations\Members\Boundary\MemberId;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\JoinMethod;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Platform\Support\Values\Email;

readonly class MemberJoinedOrganisation
{
    public function __construct(
        public JoinMethod $joinMethod,
        public MemberId $memberId,
        public OrganisationId $organisationId,
        public int $membershipId,
        public ?Email $email = null,
        public ?int $addedBy = null,
    ) {
    }
}
