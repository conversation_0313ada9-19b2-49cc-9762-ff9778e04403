<?php

namespace AwardForce\Modules\Organisations\Members\Boundary\Projections;

use AwardForce\Modules\Organisations\Members\Domain\Events\Added;
use AwardForce\Modules\Organisations\Members\Domain\Events\AddedAsAdministrator;
use AwardForce\Modules\Organisations\Members\Domain\Events\Registered;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Platform\Support\Values\Email;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class MemberTest extends BaseTestCase
{
    use Laravel;

    public function testItAppliesJoinedOnRegistrationEvent(): void
    {
        $member = new Member;
        $organisationId = OrganisationId::create();
        $email = new Email('<EMAIL>');

        $event = new Registered($email, $organisationId, 1);
        $member->applyRegistered($event);

        $this->assertEquals($organisationId, $member->organisationId);
        $this->assertEquals(1, $member->membershipId);
        $this->assertEquals(JoinMethod::Registered, $member->joinMethod);
        $this->assertEquals('<EMAIL>', $member->joinEmail);
    }

    public function testItAppliesAddedByManagerEvent(): void
    {
        $member = new Member;
        $organisationId = OrganisationId::create();

        $event = new Added(1, $organisationId, 2);
        $member->applyAdded($event);

        $this->assertEquals($organisationId, $member->organisationId);
        $this->assertEquals(2, $member->membershipId);
        $this->assertEquals(JoinMethod::Added, $member->joinMethod);
        $this->assertEquals(1, $member->addedById);
    }

    public function testItAppliesAssignedAdministratorEvent(): void
    {
        $member = new Member;
        $organisationId = OrganisationId::create();

        $event = new AddedAsAdministrator($organisationId, 1);
        $member->applyAddedAsAdministrator($event);

        $this->assertEquals($organisationId, $member->organisationId);
        $this->assertEquals(1, $member->membershipId);
    }
}
