<?php

namespace AwardForce\Modules\Organisations\Members\Boundary\Projections;

use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Database\EventSourcing\ProjectionRepository;
use AwardForce\Library\EventSourcing\ProjectionLifecycle;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Members\Boundary\MemberId;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberProjectionRepository;
use AwardForce\Modules\Organisations\Members\Domain\Events\Added;
use AwardForce\Modules\Organisations\Members\Domain\Events\AddedAsAdministrator;
use AwardForce\Modules\Organisations\Members\Domain\Events\Registered;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Concerns\AsPivot;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property MemberId $id
 * @property OrganisationId $organisationId
 * @property int $membershipId
 * @property int $addedById
 * @property string $joinEmail
 * @property JoinMethod $joinMethod
 * @property-read Organisation $organisation
 * @property-read Membership $membership
 * @property-read User $addedBy
 */
class Member extends Model implements MemberProjection
{
    use AsPivot;
    use HasFactory;
    use ProjectionLifecycle;
    use SoftDeletes;

    public $table = 'organisation_members';
    public $incrementing = false;
    protected $fillable = [
        'id',
        'organisation_id',
        'membership_id',
        'created_at',
        'updated_at',
    ];
    protected $casts = [
        'id' => MemberId::class,
        'organisation_id' => OrganisationId::class,
        'membership_id' => 'integer',
        'join_method' => JoinMethod::class,
    ];

    public function organisation(): BelongsTo
    {
        return $this->belongsTo(Organisation::class);
    }

    public function membership(): BelongsTo
    {
        return $this->belongsTo(Membership::class);
    }

    public function addedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'added_by_id');
    }

    public function applyRegistered(Registered $event): void
    {
        $this->organisationId = $event->organisationId;
        $this->membershipId = $event->membershipId;
        $this->joinEmail = $event->email;
        $this->joinMethod = JoinMethod::Registered;
    }

    public function applyAdded(Added $event): void
    {
        $this->organisationId = $event->organisationId;
        $this->membershipId = $event->membershipId;
        $this->addedById = $event->managerId;
        $this->joinMethod = JoinMethod::Added;
    }

    public function applyAddedAsAdministrator(AddedAsAdministrator $event): void
    {
        $this->organisationId = $event->organisationId;
        $this->membershipId = $event->membershipId;
        $this->joinMethod = JoinMethod::AdministratorAssigned;
    }

    public static function repository(): ProjectionRepository
    {
        return app(MemberProjectionRepository::class);
    }
}
