<?php

namespace AwardForce\Modules\Organisations\Members\Boundary\Projections;

use AwardForce\Modules\Organisations\Members\Boundary\MemberId;
use Illuminate\Database\Eloquent\Factories\Factory;

class MemberFactory extends Factory
{
    public function definition(): array
    {
        return [
            'id' => MemberId::create(),
            'organisation_id' => null,
            'membership_id' => null,
            'account_id' => current_account_id(),
            'join_method' => JoinMethod::Joined,
        ];
    }
}
