<?php

namespace AwardForce\Modules\Organisations\Members\Boundary\Projections;

use AwardForce\Library\EventSourcing\Projection;
use AwardForce\Modules\Organisations\Members\Domain\Events\Added;
use AwardForce\Modules\Organisations\Members\Domain\Events\Registered;

interface MemberProjection extends Projection
{
    public function applyRegistered(Registered $event): void;

    public function applyAdded(Added $event): void;
}
