<?php

namespace AwardForce\Modules\Organisations\Members\Domain;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Platform\Support\Values\Email;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class MemberTest extends BaseTestCase
{
    use Laravel;

    public function testCanJoinOnRegistration(): void
    {
        $member = Member::new();
        $member->register(new Email('<EMAIL>'), $organisationId = OrganisationId::create(), 1);

        $data = $member->toArray();

        $this->assertEquals($organisationId->toInteger(), $data['organisation_id']);
        $this->assertEquals(1, $data['membership_id']);
        $this->assertEquals('<EMAIL>', $data['join_email']);
    }

    public function testCanBeAddedByManager(): void
    {
        $member = Member::new();
        $member->add(1, $organisationId = OrganisationId::create(), 2);

        $data = $member->toArray();

        $this->assertEquals($organisationId->toInteger(), $data['organisation_id']);
        $this->assertEquals(1, $data['added_by']);
        $this->assertEquals(2, $data['membership_id']);
    }

    public function testCanAddAssignedAdministrator(): void
    {
        $member = Member::new();
        $member->assignAdministrator(1, $organisationId = OrganisationId::create());

        $data = $member->toArray();

        $this->assertEquals(1, $data['membership_id']);
        $this->assertEquals($organisationId->toInteger(), $data['organisation_id']);
    }
}
