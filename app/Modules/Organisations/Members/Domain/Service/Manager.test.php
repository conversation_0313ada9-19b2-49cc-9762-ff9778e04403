<?php

namespace AwardForce\Modules\Organisations\Members\Domain\Service;

use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Organisations\Members\Boundary\Events\MemberJoinedOrganisation;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\JoinMethod;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberProjectionRepository;
use AwardForce\Modules\Organisations\Members\Exceptions\MemberExists;
use AwardForce\Modules\Organisations\Members\Exceptions\MembershipNotFound;
use AwardForce\Modules\Organisations\Members\Exceptions\OrganisationNotFound;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Boundary\Services\Organisations;
use Illuminate\Support\Facades\Log;
use Mockery as m;
use Platform\Support\Values\Email;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class ManagerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private MemberProjectionRepository $members;
    private Manager $manager;

    public function init()
    {
        $this->members = app(MemberProjectionRepository::class);
        $this->manager = app(Manager::class);
    }

    public function testItCanBeAddedByManager(): void
    {
        $organisation = Organisation::factory()->create();
        $manager = $this->setupUserWithRole('Program manager');
        $user = $this->setupUserWithRole('Entrant');
        $membership = $user->currentMembership;

        $this->manager->add($manager->id, $organisation->id, $membership->id);

        $member = $this->members
            ->fields(['*'])
            ->organisation($organisation->id)
            ->membership($membership->id)
            ->first();

        $this->assertEquals($organisation->id, $member->organisationId);
        $this->assertEquals($membership->id, $member->membershipId);
        $this->assertEquals(JoinMethod::Added, $member->joinMethod);
        $this->assertEquals($manager->id, $member->addedById);
    }

    public function testAddedByManagerThrowsExceptionWhenOrganisationDoesNotExist(): void
    {
        $organisations = m::mock(Organisations::class);
        $organisations->shouldReceive('exists')
            ->once()
            ->andReturn(false);

        $manager = app(Manager::class, ['organisations' => $organisations]);

        $this->expectException(OrganisationNotFound::class);

        $manager->add(1, OrganisationId::create(), 1);
    }

    public function testAddedByManagerThrowsExceptionWhenMembershipDoesNotExist(): void
    {
        $organisations = m::mock(Organisations::class);
        $organisations->shouldReceive('exists')
            ->once()
            ->andReturn(true);

        $memberships = m::mock(MembershipRepository::class);
        $memberships->shouldReceive('primary')
            ->once()
            ->andReturnSelf();
        $memberships->shouldReceive('count')->once()->andReturn(0);

        $manager = app(Manager::class, ['organisations' => $organisations, 'memberships' => $memberships]);

        $this->expectException(MembershipNotFound::class);

        $manager->add(1, OrganisationId::create(), 1);
    }

    public function testCanJoinOnRegistration(): void
    {
        $organisation = Organisation::factory()->create();
        $user = $this->setupUserWithRole('Entrant');
        $membership = $user->currentMembership;

        $this->manager->join(new Email($user->email), $organisation->id, $membership->id);

        $member = $this->members
            ->fields(['*'])
            ->organisation($organisation->id)
            ->membership($membership->id)
            ->first();

        $this->assertEquals($organisation->id, $member->organisationId);
        $this->assertEquals($membership->id, $member->membershipId);
        $this->assertEquals($user->email, $member->joinEmail);
        $this->assertEquals(JoinMethod::Registered, $member->joinMethod);
    }

    public function testJoinOnRegistrationThrowsExceptionWhenOrganisationDoesNotExist(): void
    {
        $organisations = m::mock(Organisations::class);
        $organisations->shouldReceive('exists')
            ->once()
            ->andReturn(false);

        $manager = app(Manager::class, ['organisations' => $organisations]);

        $this->expectException(OrganisationNotFound::class);

        $manager->join(new Email('<EMAIL>'), OrganisationId::create(), 1);
    }

    public function testJoinOnRegistrationThrowsExceptionWhenMembershipDoesNotExist(): void
    {
        $organisations = m::mock(Organisations::class);
        $organisations->shouldReceive('exists')
            ->once()
            ->andReturn(true);

        $memberships = m::mock(MembershipRepository::class);
        $memberships->shouldReceive('primary')
            ->once()
            ->andReturnSelf();
        $memberships->shouldReceive('count')->once()->andReturn(0);

        $manager = app(Manager::class, ['organisations' => $organisations, 'memberships' => $memberships]);

        $this->expectException(MembershipNotFound::class);

        $manager->join(new Email('<EMAIL>'), OrganisationId::create(), 1);
    }

    public function testJoinOnRegistrationThrowsExceptionWhenMemberAlreadyExists(): void
    {
        $organisation = Organisation::factory()->create();
        $user = $this->setupUserWithRole('Entrant');
        $membership = $user->currentMembership;

        Member::factory()->create([
            'organisation_id' => $organisation->id,
            'membership_id' => $membership->id,
            'join_email' => $user->email,
            'join_method' => JoinMethod::Registered,
        ]);

        $this->expectException(MemberExists::class);

        $this->manager->join(new Email($user->email), $organisation->id, $membership->id);
    }

    public function testItCanAddMultipleMembersToMultipleOrganisations(): void
    {
        $manager = $this->setupUserWithRole('Program manager');
        $organisation1 = Organisation::factory()->create();
        $organisation2 = Organisation::factory()->create();
        $user1 = $this->setupUserWithRole('Entrant');
        $user2 = $this->setupUserWithRole('Entrant');
        $membership1 = $user1->currentMembership;
        $membership2 = $user2->currentMembership;

        $this->manager->addMany(
            $manager->id,
            [
                $organisation1->id,
                $organisation2->id,
            ],
            [
                $membership1->id,
                $membership2->id,
            ]);

        foreach ([$organisation1, $organisation2] as $organisation) {
            $member = $this->members
                ->fields(['*'])
                ->organisation($organisation->id)
                ->get();

            $this->assertCount(2, $member);
            $this->assertEquals($organisation->id, $member[0]->organisationId);
            $this->assertEquals($organisation->id, $member[1]->organisationId);
            $this->assertEquals($membership1->id, $member[0]->membershipId);
            $this->assertEquals($membership2->id, $member[1]->membershipId);
            $this->assertEquals($manager->id, $member[0]->addedById);
            $this->assertEquals($manager->id, $member[1]->addedById);
        }

        $events = $this->manager->releaseEvents();
        $this->assertCount(4, $events);
        foreach ($events as $event) {
            $this->assertInstanceOf(MemberJoinedOrganisation::class, $event);
        }

    }

    public function testAddManySkipsExistingPairs(): void
    {
        $manager = $this->setupUserWithRole('Program manager');
        $organisation = Organisation::factory()->create();
        $user = $this->setupUserWithRole('Entrant');
        $membership = $user->currentMembership;

        Member::factory()->create([
            'organisation_id' => $organisation->id,
            'membership_id' => $membership->id,
        ]);

        $this->manager->addMany($manager->id, [$organisation->id], [$membership->id]);

        $members = $this->members
            ->fields(['*'])
            ->organisation($organisation->id)
            ->count();

        $this->assertEquals(1, $members);
    }

    public function testAddManyLogsExceptions(): void
    {
        $organisationId = OrganisationId::create();
        $organisations = m::mock(Organisations::class);
        $organisations->shouldReceive('exists')
            ->once()
            ->with($organisationId)
            ->andReturn(false);

        Log::shouldReceive('error')->once()->with(m::on(function ($message) use ($organisationId) {
            $this->assertStringContainsString('OrganisationNotFound', $message);
            $this->assertStringContainsString('Organisation not found: '.$organisationId, $message);

            return true;
        }));

        $manager = app(Manager::class, ['organisations' => $organisations]);

        $manager->addMany(1, [$organisationId], [1]);
    }

    public function testAddedByAssignedAdministrator(): void
    {
        $organisation = Organisation::factory()->create();
        $administrator = $this->setupUserWithRole('Entrant');
        $membership = $administrator->currentMembership;

        $this->manager->assignAdministrator($administrator->id, $organisation->id);

        $event = $this->manager->releaseEvents()[0];

        $this->assertEquals($organisation->id, $event->organisationId);
        $this->assertEquals($membership->id, $event->membershipId);
        $this->assertEquals(JoinMethod::AdministratorAssigned, $event->joinMethod);
    }
}
