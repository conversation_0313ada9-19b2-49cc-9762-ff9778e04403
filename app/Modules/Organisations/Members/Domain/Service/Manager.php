<?php

namespace AwardForce\Modules\Organisations\Members\Domain\Service;

use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Organisations\Members\Boundary\Events\MemberJoinedOrganisation;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\JoinMethod;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberProjectionRepository;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberRepository;
use AwardForce\Modules\Organisations\Members\Domain\Member;
use AwardForce\Modules\Organisations\Members\Exceptions\MemberExists;
use AwardForce\Modules\Organisations\Members\Exceptions\MembershipNotFound;
use AwardForce\Modules\Organisations\Members\Exceptions\OrganisationNotFound;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Services\Organisations;
use Illuminate\Support\Facades\Log;
use Platform\Events\Raiseable;
use Platform\Support\Values\Email;

class Manager
{
    use Raiseable;

    public function __construct(
        private Organisations $organisations,
        private MembershipRepository $memberships,
        private MemberRepository $aggregates,
        private MemberProjectionRepository $members
    ) {
    }

    public function join(Email $email, OrganisationId $organisationId, int $membershipId): void
    {
        $this->validateExistence($organisationId, $membershipId);

        if ($this->memberExists($organisationId, $membershipId)) {
            throw new MemberExists;
        }

        $memberId = $this->aggregates->transaction(function (MemberRepository $repository) use ($email, $organisationId, $membershipId) {

            $member = Member::new();
            $member->register($email, $organisationId, $membershipId);

            $repository->save($member);

            return $member->aggregateRootId();
        });

        $this->raise(
            new MemberJoinedOrganisation(
                JoinMethod::Registered,
                $memberId,
                $organisationId,
                $membershipId,
                $email
            )
        );
    }

    public function add(int $managerId, OrganisationId $organisationId, int $membershipId): void
    {
        $this->validateExistence($organisationId, $membershipId);

        if ($this->memberExists($organisationId, $membershipId)) {
            throw new MemberExists;
        }

        $memberId = $this->aggregates->transaction(function (MemberRepository $repository) use ($managerId, $organisationId, $membershipId) {

            $member = Member::new();
            $member->add($managerId, $organisationId, $membershipId);

            $repository->save($member);

            return $member->aggregateRootId();
        });

        $this->raise(new MemberJoinedOrganisation(
            JoinMethod::Added,
            $memberId,
            $organisationId,
            $membershipId,
            addedBy: $managerId
        ));
    }

    public function addMany(int $managerId, array $organisationIds, array $membershipIds): void
    {
        try {
            $this->validateOrganisationExistence(...$organisationIds);
            $this->validateMembershipExistence(...$membershipIds);
        } catch (OrganisationNotFound|MembershipNotFound $e) {
            Log::error(class_basename($e).': '.$e->getMessage().'\n'.$e->getTraceAsString());

            return;
        }

        $events = $this->aggregates->transaction(function (MemberRepository $repository) use ($managerId, $organisationIds, $membershipIds) {
            $events = [];
            foreach ($organisationIds as $organisationId) {
                foreach ($this->filterMemberships($organisationId, $membershipIds) as $membershipId) {
                    $member = Member::new();
                    $member->add($managerId, $organisationId, $membershipId);

                    $repository->save($member);
                    $events[] = new MemberJoinedOrganisation(
                        JoinMethod::Added,
                        $member->aggregateRootId(),
                        $organisationId,
                        $membershipId,
                        addedBy: $managerId
                    );
                }
            }

            return $events;
        });

        $this->raiseAll($events);
    }

    public function assignAdministrator(int $administratorId, OrganisationId $organisationId): void
    {
        $membershipId = $this->memberships->user($administratorId)
            ->fields(['id'])
            ->require()
            ->id;
        $this->validateOrganisationExistence($organisationId);

        $memberId = $this->aggregates->transaction(function (MemberRepository $repository) use ($membershipId, $organisationId) {

            $member = Member::new();
            $member->assignAdministrator($membershipId, $organisationId);

            $repository->save($member);

            return $member->aggregateRootId();
        });

        $this->raise(new MemberJoinedOrganisation(
            JoinMethod::AdministratorAssigned,
            $memberId,
            $organisationId,
            $membershipId,
        ));
    }

    private function validateExistence(OrganisationId $organisationId, int $membershipId): void
    {
        $this->validateOrganisationExistence($organisationId);
        $this->validateMembershipExistence($membershipId);
    }

    private function validateOrganisationExistence(OrganisationId ...$organisationId): void
    {
        if (! $this->organisations->exists(...$organisationId)) {
            throw new OrganisationNotFound(
                'Organisation not found: '.implode(', ', array_map(fn($id) => $id->toString(), $organisationId))
            );
        }
    }

    private function validateMembershipExistence(int ...$membershipId): void
    {
        if ($this->memberships->primary(...$membershipId)->count() !== count($membershipId)) {
            throw new MembershipNotFound('Membership not found: '.implode(', ', $membershipId));
        }
    }

    private function memberExists(OrganisationId $organisationId, int $membershipId): bool
    {
        return $this->members
            ->organisation($organisationId)
            ->membership($membershipId)
            ->exists();
    }

    private function filterMemberships(OrganisationId $organisationId, array $membershipIds): array
    {
        $members = $this->members
            ->fields(['id', 'membership_id'])
            ->organisation($organisationId)
            ->membership(...$membershipIds)
            ->get();

        return array_filter($membershipIds, fn($id) => ! $members->contains('membership_id', $id));
    }
}
