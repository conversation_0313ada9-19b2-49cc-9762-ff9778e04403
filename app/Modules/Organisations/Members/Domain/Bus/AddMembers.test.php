<?php

namespace AwardForce\Modules\Organisations\Members\Domain\Bus;

use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Organisations\Members\Domain\Service\Manager;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class AddMembersTest extends BaseTestCase
{
    use Laravel;

    public function testItCanAddMembersToOrganisations(): void
    {

        $orgId1 = OrganisationId::create();
        $orgId2 = OrganisationId::create();
        $command = new AddMembers(
            1,
            [2, 3],
            [$orgId1->toString(), $orgId2->toString()]
        );
        $membership1 = new Membership;
        $membership1->setAttribute('id', 10);
        $membership2 = new Membership;
        $membership2->setAttribute('id', 20);

        $account = new Account;
        $account->setAttribute('id', 1);
        CurrentAccount::shouldReceive('get')->andReturn($account);

        $manager = m::mock(Manager::class);
        $manager->shouldReceive('addMany')->once()->with(
            1,
            [
                $orgId1,
                $orgId2,
            ],
            [
                10,
                20,
            ]);

        $manager->shouldReceive('releaseEvents')->once()->andReturn([]);

        $memberships = m::mock(MembershipRepository::class);
        $memberships->shouldReceive('user')->with(2, 3)->andReturnSelf();
        $memberships->shouldReceive('fields')->with(['id'])->andReturnSelf();
        $memberships->shouldReceive('just')->with('id')->andReturn([10, 20]);

        $handler = new AddMembersHandler($manager, $memberships);
        $handler->handle($command);
    }
}
