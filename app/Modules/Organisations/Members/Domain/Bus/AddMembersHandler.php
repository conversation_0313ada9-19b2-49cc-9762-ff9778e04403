<?php

namespace AwardForce\Modules\Organisations\Members\Domain\Bus;

use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Organisations\Members\Domain\Service\Manager;
use Platform\Events\EventDispatcher;

class AddMembersHandler
{
    use EventDispatcher;

    public function __construct(private Manager $members, private MembershipRepository $memberships)
    {
    }

    public function handle(AddMembers $command): void
    {
        $membershipIds = $this->memberships->user(...$command->userIds)
            ->fields(['id'])
            ->just('id');
        $this->members->addMany($command->managerId, $command->organisationIds, $membershipIds);

        $this->dispatchAll($this->members->releaseEvents());
    }
}
