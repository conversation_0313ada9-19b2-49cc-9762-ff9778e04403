<?php

namespace AwardForce\Modules\Organisations\Members\Domain\Bus;

use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;

readonly class AddMembers
{
    /**)
     * @var OrganisationId[]
     */
    public array $organisationIds;

    public function __construct(public int $managerId, public array $userIds, array $organisationIds)
    {
        $this->organisationIds = array_map(fn($id) => OrganisationId::fromString($id), $organisationIds);
    }
}
