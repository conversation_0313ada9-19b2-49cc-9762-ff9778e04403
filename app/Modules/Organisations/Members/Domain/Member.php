<?php

namespace AwardForce\Modules\Organisations\Members\Domain;

use AwardForce\Library\EventSourcing\HasSnapshots;
use AwardForce\Library\EventSourcing\SnapshottableAggregateRoot;
use AwardForce\Modules\Organisations\Members\Boundary\MemberId;
use AwardForce\Modules\Organisations\Members\Domain\Events\Added;
use AwardForce\Modules\Organisations\Members\Domain\Events\AddedAsAdministrator;
use AwardForce\Modules\Organisations\Members\Domain\Events\Registered;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\EventSourcing\AggregateRootWithAggregates;
use EventSauce\EventSourcing\Snapshotting\SnapshottingBehaviour;
use Illuminate\Contracts\Support\Arrayable;
use Platform\Support\Values\Email;

class Member implements Arrayable, SnapshottableAggregateRoot
{
    use AggregateRootWithAggregates;
    use HasSnapshots;
    use SnapshottingBehaviour;

    private OrganisationId $organisationId;
    private int $membershipId;
    private ?Email $joinEmail = null;
    private ?int $addedBy = null;

    public static function new(): static
    {
        return static::createNewInstance(MemberId::create());
    }

    public function register(Email $email, OrganisationId $organisationId, int $membershipId): void
    {
        $this->recordThat(new Registered($email, $organisationId, $membershipId));
    }

    public function add(int $managerId, OrganisationId $organisationId, int $membershipId): void
    {
        $this->recordThat(new Added($managerId, $organisationId, $membershipId));
    }

    public function assignAdministrator(int $administratorId, OrganisationId $organisationId): void
    {
        $this->recordThat(new AddedAsAdministrator($organisationId, $administratorId));
    }

    public function applyRegistered(Registered $joined): void
    {
        $this->organisationId = $joined->organisationId;
        $this->membershipId = $joined->membershipId;
        $this->joinEmail = $joined->email;
    }

    public function applyAdded(Added $added): void
    {
        $this->organisationId = $added->organisationId;
        $this->membershipId = $added->membershipId;
        $this->addedBy = $added->managerId;
    }

    public function applyAddedAsAdministrator(AddedAsAdministrator $added): void
    {
        $this->organisationId = $added->organisationId;
        $this->membershipId = $added->membershipId;
    }

    public function toArray(): array
    {
        return [
            'organisation_id' => $this->organisationId->toInteger(),
            'membership_id' => $this->membershipId,
            'join_email' => (string) $this->joinEmail,
            'added_by' => $this->addedBy,
        ];
    }

    protected function createSnapshotState(): mixed
    {
        return $this->toArray();
    }

    protected static function reconstituteFromSnapshotState(AggregateRootId $id, $state): static
    {
        $instance = new static($id);
        $instance->organisationId = OrganisationId::fromInteger($state['organisation_id']);
        $instance->membershipId = $state['membership_id'];
        $instance->joinEmail = $state['join_email'] ? new Email($state['join_email']) : null;
        $instance->addedBy = $state['added_by'];

        return $instance;
    }
}
