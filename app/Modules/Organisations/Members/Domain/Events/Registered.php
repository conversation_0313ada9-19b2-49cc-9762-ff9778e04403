<?php

namespace AwardForce\Modules\Organisations\Members\Domain\Events;

use AwardForce\Library\EventSourcing\SourcedEvent;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Platform\Support\Values\Email;

readonly class Registered implements SourcedEvent
{
    public function __construct(public Email $email, public OrganisationId $organisationId, public int $membershipId)
    {
    }
}
