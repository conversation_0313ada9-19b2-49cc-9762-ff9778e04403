<?php

namespace AwardForce\Modules\Organisations\Members\Http\Controllers;

use AwardForce\Modules\Organisations\Members\Domain\Bus\AddMembers;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Platform\Http\Controller;

class MemberController extends Controller
{
    public static string $resource = 'Organisations';

    use DispatchesJobs;

    public function add(Request $request): RedirectResponse
    {
        $this->dispatch(new AddMembers(consumer_id(), $selected = $request->array('selected'), $organisations = $request->array('organisations')));

        $translationKey = 'organisations.selector.success_message_'.(count($organisations) > 1 ? 'plural' : 'singular');

        return redirect()
            ->back()
            ->with([
                'message' => trans_choice($translationKey, count($selected), ['count_organisations' => count($organisations)]),
                'type' => 'info',
            ]);
    }
}
