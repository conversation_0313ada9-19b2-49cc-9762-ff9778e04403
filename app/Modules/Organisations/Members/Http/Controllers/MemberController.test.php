<?php

namespace AwardForce\Modules\Organisations\Members\Http\Controllers;

use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class MemberControllerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanAddMembersToOrganisations(): void
    {
        $memberships = [
            $this->setupUserWithRole('Entrant')->currentMembership,
            $this->setupUserWithRole('Entrant')->currentMembership,
        ];
        $organisations = Organisation::factory(3)->create();
        $manager = $this->setupUserWithRole('Program manager', true);

        $request = new Request([
            'selected' => [$memberships[0]->userId, $memberships[1]->userId],
            'organisations' => [$organisations[0]->id->toString(), $organisations[1]->id->toString()],
        ]);

        $controller = app(MemberController::class);
        $controller->add($request);

        $total = app(MemberProjectionRepository::class)
            ->countAll();

        // One Member per Organisation - Membership pair
        $this->assertEquals(4, $total);

        $organisationOneMembers = app(MemberProjectionRepository::class)
            ->fields(['id', 'membership_id', 'organisation_id', 'added_by_id'])
            ->organisation($organisations[0]->id)
            ->get();

        $this->assertCount(2, $organisationOneMembers);
        $this->assertEquals($organisations[0]->id, $organisationOneMembers[0]->organisationId);
        $this->assertEquals($organisations[0]->id, $organisationOneMembers[1]->organisationId);
        $this->assertEquals($memberships[0]->id, $organisationOneMembers[0]->membershipId);
        $this->assertEquals($memberships[1]->id, $organisationOneMembers[1]->membershipId);
        $this->assertEquals($manager->id, $organisationOneMembers[0]->addedById);
        $this->assertEquals($manager->id, $organisationOneMembers[1]->addedById);

        $organisationTwoMembers = app(MemberProjectionRepository::class)
            ->fields(['id', 'membership_id', 'organisation_id', 'added_by_id'])
            ->organisation($organisations[1]->id)
            ->get();

        $this->assertCount(2, $organisationTwoMembers);
        $this->assertEquals($organisations[1]->id, $organisationTwoMembers[0]->organisationId);
        $this->assertEquals($organisations[1]->id, $organisationTwoMembers[1]->organisationId);
        $this->assertEquals($memberships[0]->id, $organisationTwoMembers[0]->membershipId);
        $this->assertEquals($memberships[1]->id, $organisationTwoMembers[1]->membershipId);
        $this->assertEquals($manager->id, $organisationTwoMembers[0]->addedById);
        $this->assertEquals($manager->id, $organisationTwoMembers[1]->addedById);
    }

    #[TestWith(['membershipCount' => 1, 'organisationCount' => 1, 'translationKey' => 'singular'])]
    #[TestWith(['membershipCount' => 2, 'organisationCount' => 1, 'translationKey' => 'singular'])]
    #[TestWith(['membershipCount' => 1, 'organisationCount' => 2, 'translationKey' => 'plural'])]
    #[TestWith(['membershipCount' => 2, 'organisationCount' => 2, 'translationKey' => 'plural'])]
    public function testItReturnsCorrectSingularResponseMessage(int $membershipCount, int $organisationCount, string $translationKey): void
    {
        $this->setupUserWithRole('Program manager', true);
        $memberships = [];
        for ($i = 0; $i < $membershipCount; $i++) {
            $memberships[] = $this->setupUserWithRole('Entrant')->currentMembership;
        }

        $organisations = [];
        for ($i = 0; $i < $organisationCount; $i++) {
            $organisations[] = Organisation::factory()->create();
        }

        $request = new Request([
            'selected' => array_map(fn($membership) => $membership->userId, $memberships),
            'organisations' => array_map(fn($organisation) => $organisation->id->toString(), $organisations),
        ]);

        $controller = app(MemberController::class);
        $response = $controller->add($request);

        $this->assertEquals(
            trans_choice('organisations.selector.success_message_'.$translationKey, count($memberships), ['count_organisations' => count($organisations)]),
            $response->getSession()->get('message')
        );
    }
}
