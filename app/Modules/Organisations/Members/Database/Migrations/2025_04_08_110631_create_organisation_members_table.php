<?php

use AwardForce\Modules\Organisations\Members\Boundary\Projections\JoinMethod;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('organisation_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('organisation_id')->constrained()->cascadeOnDelete();
            $table->foreignId('membership_id')->constrained('memberships')->cascadeOnDelete();
            $table->unsignedInteger('account_id')->index();
            $table->enum('join_method', array_map(fn($case) => $case->value, JoinMethod::cases()));
            $table->string('join_email')->nullable();
            $table->foreignId('added_by_id')->nullable()->constrained('users')->onDelete('no action');
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['organisation_id', 'membership_id'], 'organisation_membership_unique');

            $table->foreign('account_id')
                ->references('id')
                ->on('accounts')
                ->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('organisation_members');
    }
};
