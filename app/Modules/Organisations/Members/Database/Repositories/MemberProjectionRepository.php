<?php

namespace AwardForce\Modules\Organisations\Members\Database\Repositories;

use AwardForce\Library\Database\EventSourcing\ProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Repository;

interface MemberProjectionRepository extends BuilderRepository, ProjectionRepository, Repository
{
    public function organisation(OrganisationId ...$organisationId): static;

    public function membership(int ...$membershipId): static;
}
