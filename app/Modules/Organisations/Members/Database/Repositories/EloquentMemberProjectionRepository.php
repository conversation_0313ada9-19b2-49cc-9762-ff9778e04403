<?php

namespace AwardForce\Modules\Organisations\Members\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use EventSauce\EventSourcing\AggregateRootId;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentMemberProjectionRepository extends Repository implements MemberProjectionRepository
{
    use HasQueryBuilder;

    public function __construct(Member $model)
    {
        $this->model = $model;
    }

    public function organisation(OrganisationId ...$organisationId): static
    {
        $this->query()->whereIn('organisation_id', array_map(fn($id) => $id->toInteger(), $organisationId));

        return $this;
    }

    public function membership(int ...$membershipId): static
    {
        $this->query()->where('membership_id', $membershipId);

        return $this;
    }

    public function forAggregateRoot(AggregateRootId $id): static
    {
        $this->query()->where('id', $id->toString());

        return $this;
    }
}
