<?php

namespace AwardForce\Modules\Organisations\Members\Database\Repositories;

use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class MemberProjectionRepositoryTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private MemberProjectionRepository $memberRepository;
    private Organisation $organisation;
    private Membership $membership;

    protected function init()
    {
        $this->memberRepository = app(MemberProjectionRepository::class);
        $this->organisation = Organisation::factory()->create();
        $this->membership = $this->muffin(Membership::class);
        Member::factory()
            ->create([
                'organisation_id' => $this->organisation->id,
                'membership_id' => $this->membership->id,
            ]);
    }

    public function testItCanQueryOrganisation(): void
    {
        $member = $this->memberRepository
            ->fields(['*'])
            ->organisation($this->organisation->id)
            ->first();

        $this->assertEquals($this->organisation->id, $member->organisationId);
        $this->assertEquals($this->membership->id, $member->membershipId);
    }

    public function testItCanQueryMembership(): void
    {
        $member = $this->memberRepository
            ->fields(['*'])
            ->membership($this->membership->id)
            ->first();

        $this->assertEquals($this->organisation->id, $member->organisationId);
        $this->assertEquals($this->membership->id, $member->membershipId);
    }
}
