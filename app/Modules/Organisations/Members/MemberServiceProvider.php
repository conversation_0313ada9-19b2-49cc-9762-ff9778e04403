<?php

namespace AwardForce\Modules\Organisations\Members;

use AwardForce\Library\Database\EventSourcing\AccountableMessageDecorator;
use AwardForce\Library\Database\EventSourcing\EventsTableSchema;
use AwardForce\Library\Database\EventSourcing\SnowflakeEncoder;
use AwardForce\Library\EventSourcing\SynchronousProjectionMessageDispatcher;
use AwardForce\Library\Providers\ModuleServiceProvider;
use AwardForce\Modules\Accounts\Events\MembershipWasRegistered;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member as MemberProjection;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\MemberMessageDispatcher;
use AwardForce\Modules\Organisations\Members\Database\Repositories\EloquentMemberProjectionRepository;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberMessageRepository;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberProjectionRepository;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MemberRepository;
use AwardForce\Modules\Organisations\Members\Database\Repositories\MySqlMemberRepository;
use AwardForce\Modules\Organisations\Members\Domain\Member;
use AwardForce\Modules\Organisations\Members\Listeners\Memberships;
use AwardForce\Modules\Organisations\Members\Listeners\Organisations;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\AdministratorAssigned;
use EventSauce\EventSourcing\DefaultHeadersDecorator;
use EventSauce\EventSourcing\EventSourcedAggregateRootRepository;
use EventSauce\EventSourcing\MessageDecoratorChain;
use EventSauce\EventSourcing\Serialization\ConstructingMessageSerializer;
use EventSauce\EventSourcing\Serialization\PayloadSerializerSupportingObjectMapperAndSerializablePayload;
use EventSauce\EventSourcing\Snapshotting\ConstructingAggregateRootRepositoryWithSnapshotting;
use EventSauce\EventSourcing\Snapshotting\SnapshotRepository;
use EventSauce\IdEncoding\BinaryUuidIdEncoder;
use EventSauce\MessageRepository\IlluminateMessageRepository\IlluminateMessageRepository;

class MemberServiceProvider extends ModuleServiceProvider
{
    protected array $repositories = [
        MemberProjectionRepository::class => EloquentMemberProjectionRepository::class,
    ];
    protected array $listeners = [
        MembershipWasRegistered::class => [
            Memberships::class.'@whenMembershipWasRegistered',
        ],
        AdministratorAssigned::class => [
            Organisations::class.'@administratorAssigned',
        ],
    ];

    public function register(): void
    {
        parent::register();
        $this->registerMessageRepository();
        $this->registerAggregateRepository();
        $this->registerMessageDispatcher();
    }

    public function boot(): void
    {
        parent::boot();
        $this->bootProjectors();
    }

    private function registerAggregateRepository(): void
    {
        $this->app->scoped(MemberRepository::class, function ($app) {
            return new MySqlMemberRepository(
                new ConstructingAggregateRootRepositoryWithSnapshotting(
                    Member::class,
                    $app->make(MemberMessageRepository::class),
                    $app->make(SnapshotRepository::class),
                    $app->make(EventSourcedAggregateRootRepository::class, [
                        'aggregateRootClassName' => Member::class,
                        'messageRepository' => $app->make(MemberMessageRepository::class),
                        'decorator' => new MessageDecoratorChain(
                            new DefaultHeadersDecorator,
                            new AccountableMessageDecorator,
                        ),
                        'dispatcher' => $app->make(MemberMessageDispatcher::class),
                    ]),
                )
            );
        });
    }

    private function registerMessageRepository(): void
    {
        $this->app->scoped(MemberMessageRepository::class, function ($app) {
            return $app->make(IlluminateMessageRepository::class, [
                'tableName' => 'organisation_member_events',
                'tableSchema' => new EventsTableSchema,
                'serializer' => new ConstructingMessageSerializer(
                    payloadSerializer: new PayloadSerializerSupportingObjectMapperAndSerializablePayload()
                ),
                'aggregateRootIdEncoder' => new SnowflakeEncoder,
                'eventIdEncoder' => new BinaryUuidIdEncoder,
            ]);
        });
    }

    private function registerMessageDispatcher(): void
    {
        $this->app->scoped(MemberMessageDispatcher::class, fn() => new SynchronousProjectionMessageDispatcher);
    }

    private function bootProjectors(): void
    {
        $this->app->make(MemberMessageDispatcher::class)->registerProjection(new MemberProjection);
    }
}
