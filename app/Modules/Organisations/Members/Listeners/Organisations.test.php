<?php

namespace AwardForce\Modules\Organisations\Members\Listeners;

use AwardForce\Modules\Organisations\Members\Domain\Service\Manager;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\AdministratorAssigned;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class OrganisationsTest extends BaseTestCase
{
    use Laravel;

    private Manager|m\MockInterface $members;

    protected function init(): void
    {
        $this->members = m::mock(Manager::class);
    }

    public function testItCreatesMemberForAssignedAdministrator(): void
    {
        $event = new AdministratorAssigned(
            OrganisationId::create(),
            1
        );

        $this->members->shouldReceive('assignAdministrator')
            ->once()
            ->with($event->administratorId, $event->organisationId);

        $listener = new Organisations($this->members);
        $listener->administratorAssigned($event);
    }
}
