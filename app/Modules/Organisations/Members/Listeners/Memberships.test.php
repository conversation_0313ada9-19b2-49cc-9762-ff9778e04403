<?php

namespace AwardForce\Modules\Organisations\Members\Listeners;

use AwardForce\Modules\Accounts\Events\MembershipWasRegistered;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Members\Domain\Service\Manager;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Boundary\Services\DomainLookup;
use AwardForce\Modules\Settings\Services\Settings;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class MembershipsTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private Memberships $listener;
    private m\MockInterface|Memberships $mockedListener;
    private m\MockInterface|DomainLookup $domainLookup;
    private m\MockInterface|Settings $settingsService;
    private m\MockInterface|Manager $manager;

    protected function init(): void
    {
        $this->manager = m::mock(Manager::class);
        $this->domainLookup = m::mock(DomainLookup::class);
        $this->settingsService = m::mock(Settings::class);
        $this->mockedListener = new Memberships($this->manager, $this->settingsService, $this->domainLookup);
        $this->listener = app(Memberships::class, ['settings' => $this->settingsService]);
    }

    public function testWhenRegisteredWithNoEmailDoesNothing(): void
    {
        $event = m::mock(MembershipWasRegistered::class);
        $event->shouldReceive('user->getAttribute')->with('email')->andReturn(null);

        $this->domainLookup->shouldNotReceive('forEmail');
        $this->mockedListener->whenMembershipWasRegistered($event);
    }

    public function testWhenRegisteredOrganisationSettingsDisallowJoiningDoesNothing(): void
    {
        $this->settingsService->shouldReceive('organisationSettings')->andReturn(
            new OrganisationsSettings(['canJoinOnRegisteredEmail' => false])
        );
        $event = m::mock(MembershipWasRegistered::class);
        $event->shouldReceive('user->getAttribute')->with('email')->andReturn('<EMAIL>');

        $this->domainLookup->shouldNotReceive('forEmail');
        $this->mockedListener->whenMembershipWasRegistered($event);
    }

    public function testWhenRegisteredWithValidEmailAndSettingsSyncsOrganisations(): void
    {
        $organisation1 = Organisation::factory()->create(['domains' => ['example.com']]);
        $organisation2 = Organisation::factory()->create(['domains' => ['example.org']]);
        $organisation3 = Organisation::factory()->create(['domains' => ['example.net', 'example.com']]);

        Feature::shouldReceive('enabled')->with('organisations')->andReturnTrue();
        $this->settingsService->shouldReceive('organisationSettings')->andReturn(new OrganisationsSettings([
            'enabled' => true,
            'joinOnRegisteredEmail' => true,
        ]));

        $user = $this->muffin(User::class)->create(['email' => '<EMAIL>']);
        $user->registerMembership(current_account(), 'en_GB');
        $membership = $user->currentMembership;
        $event = new MembershipWasRegistered($membership);

        $this->listener->whenMembershipWasRegistered($event);

        $members = $membership->organisationMembers()->get();

        $this->assertCount(2, $members);
        $this->assertContains($organisation1->id->toInteger(), $members->pluck('organisation_id')->map->toInteger());
        $this->assertContains($organisation3->id->toInteger(), $members->pluck('organisation_id')->map->toInteger());
    }
}
