<?php

namespace AwardForce\Modules\Organisations\Members\Listeners;

use AwardForce\Modules\Organisations\Members\Domain\Service\Manager;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\AdministratorAssigned;

class Organisations
{
    public function __construct(private Manager $members)
    {
    }

    public function administratorAssigned(AdministratorAssigned $event): void
    {
        $this->members->assignAdministrator($event->administratorId, $event->organisationId);
    }
}
