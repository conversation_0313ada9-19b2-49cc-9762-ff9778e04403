<?php

namespace AwardForce\Modules\Organisations\Members\Listeners;

use AwardForce\Modules\Accounts\Events\MembershipWasRegistered;
use AwardForce\Modules\Organisations\Members\Domain\Service\Manager;
use AwardForce\Modules\Organisations\Organisations\Boundary\Services\DomainLookup;
use AwardForce\Modules\Settings\Services\Settings;
use Platform\Support\Values\Email;

class Memberships
{
    public function __construct(
        private Manager $manager,
        private Settings $settings,
        private DomainLookup $domainLookup
    ) {
    }

    public function whenMembershipWasRegistered(MembershipWasRegistered $event): void
    {
        // If the user has no email, we can't do anything
        if (! $event->user()->email) {
            return;
        }

        // If the organisation settings don't allow joining on registered email, we can't do anything
        if (! $this->settings->organisationSettings()->canJoinOnRegisteredEmail()) {
            return;
        }

        $organisations = $this->domainLookup->forEmail(new Email($event->user()->email));

        foreach ($organisations as $organisation) {
            $this->manager->join(
                new Email($event->user()->email),
                $organisation->id,
                $event->membership->id
            );
        }
    }
}
