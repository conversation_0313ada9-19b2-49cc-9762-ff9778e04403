<?php

namespace AwardForce\Modules\GrantReports\Models;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Encrypter\Traits\HasEncrypter;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Documents\Contracts\HasDocuments as HasDocumentsContract;
use AwardForce\Modules\Documents\Contracts\HasMergeFields;
use AwardForce\Modules\Documents\Models\HasDocuments;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Contracts\Collaborative;
use AwardForce\Modules\Forms\Collaboration\Exceptions\GrantReportOwnerCannotBeChanged;
use AwardForce\Modules\Forms\Collaboration\Traits\HasCollaborators;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\Fieldable;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFields;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFlatValues;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use AwardForce\Modules\Forms\Formables\Boundary\Formable;
use AwardForce\Modules\Forms\Formables\ResourceId;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Relations\HasSubmission;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Forms\Forms\FormId;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Events\GrantReportDueDateWasUpdated;
use AwardForce\Modules\GrantReports\Events\GrantReportWasCreated;
use AwardForce\Modules\GrantReports\Events\GrantReportWasSubmitted;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Traits\Seasonal;
use AwardForce\Modules\Tags\Contracts\TaggableModel;
use AwardForce\Modules\Tags\Traits\Taggable;
use Carbon\Carbon;
use Carbon\CarbonImmutable;
use Eloquence\Behaviours\HasSlugs;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Platform\Database\Eloquent\Archivisation;
use Platform\Database\Eloquent\TranslatableModel;
use Platform\Search\HasValues;
use Tectonic\Localisation\Contracts\Translatable;
use Tectonic\Localisation\Translator\Translations;

/**
 * AwardForce\Modules\GrantReports\Models\GrantReport
 *
 * @property int $id
 * @property string $slug
 * @property int $accountId
 * @property int $seasonId
 * @property int $formId
 * @property int $entryId
 * @property int|null $localId
 * @property CarbonImmutable $dueDate
 * @property \Illuminate\Support\Carbon|null $deletedAt
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property string|null $submittedAt
 * @property array|null $values
 * @property array|null $hashes
 * @property array|null $protected
 * @property-read \AwardForce\Modules\Accounts\Models\Account $account
 * @property-read \Platform\Database\Eloquent\Collection<int, \AwardForce\Modules\Documents\Models\Document> $documents
 * @property-read int|null $documentsCount
 * @property-read Entry $entry
 * @property-read Form|null $form
 * @property-read \AwardForce\Modules\Categories\Models\Category|null $category
 * @property array $fieldValues
 * @property \AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields $fields
 * @property-read string $name
 * @property-read string $status
 * @property-read string $title
 * @property-read mixed $userId
 * @property-read \AwardForce\Modules\Seasons\Models\Season $season
 * @property-read \Platform\Database\Eloquent\Collection<int, \AwardForce\Modules\Tags\Models\Tag> $tags
 * @property-read int|null $tagsCount
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Tectonic\LaravelLocalisation\Database\Translation> $translations
 * @property-read int|null $translationsCount
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|GrantReport archived()
 * @method static \Platform\Database\Eloquent\Builder|GrantReport current()
 * @method static \Platform\Database\Eloquent\Builder|GrantReport forSeason($seasonId)
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|GrantReport joinTaggables()
 * @method static \Platform\Database\Eloquent\Builder|GrantReport newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|GrantReport newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|GrantReport onlyTrashed()
 * @method static \Platform\Database\Eloquent\Builder|GrantReport preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport query()
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereDueDate($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereEntryId($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereFormId($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereHashes($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereLocalId($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereProtected($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereSubmittedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|GrantReport whereValues($value)
 * @method static \Illuminate\Database\Eloquent\Builder|GrantReport withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|GrantReport withoutTrashed()
 *
 * @mixin \Eloquent
 */
class GrantReport extends Model implements Collaborative, Formable, HasDocumentsContract, HasFields, HasMergeFields, HasValues, Submittable, TaggableModel, Translatable, ValuesProvider
{
    use Archivisation;
    use BelongsToAccount;
    use Fieldable;
    use HasCollaborators;
    use HasDocuments;
    use HasEncrypter;
    use HasFlatValues;
    use HasSlugs;
    use HasSubmission;
    use Seasonal;
    use SoftDeletes;
    use Taggable;
    use TranslatableModel;
    use Translations;

    protected $table = 'grant_reports';
    protected $casts = [
        'values' => 'array',
        'hashes' => 'array',
        'protected' => 'array',
        'due_date' => 'immutable_datetime',
    ];
    protected $fillable = ['due_date'];

    public function getTranslatableFields(): array
    {
        return [];
    }

    public static function add(
        int $accountId,
        int $formId,
        int $seasonId,
        int $entryId,
        string $dueDate,
        ?string $timezone = null
    ): GrantReport {
        $grantReport = new self;
        $grantReport->accountId = $accountId;
        $grantReport->formId = $formId;
        $grantReport->seasonId = $seasonId;
        $grantReport->entryId = $entryId;
        $grantReport->dueDate = (new CarbonImmutable($dueDate, $timezone))->toISOString();

        $grantReport->save();

        $grantReport->raise(new GrantReportWasCreated($grantReport));

        return $grantReport;
    }

    public function entry(): BelongsTo
    {
        return $this->belongsTo(Entry::class);
    }

    public function form(): BelongsTo
    {
        return $this->belongsTo(Form::class, 'form_id')->withTrashed();
    }

    public function getUserIdAttribute()
    {
        return $this->entry?->userId;
    }

    public function getChapters()
    {
        return $this->form->getChapters();
    }

    public function getCategoryAttribute(): ?Category
    {
        return translate($this->form->categories?->first());
    }

    public function user(): BelongsTo
    {
        return $this->entry->user();
    }

    public function forVue()
    {
        return [
            'id' => $this->id,
            'slug' => (string) $this->slug,
            'name' => translate($this->form)->name,
            'dueDate' => $this->dueDate->format('Y-m-d H:i'),
            'dueDateTimezone' => $this->timezone(),
            'status' => $this->status,
            'deletedForm' => ! is_null($this->form->deletedAt),
        ];
    }

    public function timezone(): string
    {
        return $this->dueDate->timezoneName;
    }

    public function getStatusAttribute(): string
    {
        return Status::fromModel($this)->value;
    }

    public function submit(): GrantReport
    {
        $this->submittedAt = new Carbon;
        $this->raise(new GrantReportWasSubmitted($this));

        return $this;
    }

    public function updateDueDate(string $dueDate, ?string $timezone = null)
    {
        $this->dueDate = CarbonImmutable::createFromTimeString($dueDate, $timezone)->toISOString();
        $this->save();

        $this->raise(new GrantReportDueDateWasUpdated($this));
    }

    public function getChapter(): ?Chapter
    {
        return $this->entry?->getChapter();
    }

    public function formableResourceId(): ResourceId
    {
        return new ResourceId($this->id);
    }

    public function formableFormId(): FormId
    {
        return new FormId($this->formId);
    }

    public function formableResource(): Resource
    {
        return Resource::GrantReport;
    }

    public function formType(): string
    {
        return Form::FORM_TYPE_REPORT;
    }

    public function getTitleAttribute(): string
    {
        return $this->entry?->title ?? '';
    }

    public function getDueDateAttribute(): CarbonImmutable
    {
        return CarbonImmutable::createFromTimeString($this->attributes['due_date'])
            ->setTimezone(Consumer::timezone() ?? setting('timezone'));
    }

    public function tabIsEligible(Tab $tab): bool
    {
        return false;
    }

    public function isOverdue(): bool
    {
        return Status::fromModel($this)->isOverdue();
    }

    public function isInvited(): bool
    {
        return false;
    }

    public function getLocalIdAttribute(): ?int
    {
        return $this->entry?->localId;
    }

    public function categoryShortcode(): ?string
    {
        return translate($this->entry?->category)?->shortcode;
    }

    public function isEntry(): bool
    {
        return false;
    }

    public function isGrantReport(): bool
    {
        return true;
    }

    public function inCart(): bool
    {
        return (bool) $this->entry?->inCart();
    }

    public function hasOrderAwaitingPayment(): bool
    {
        return (bool) $this->entry?->hasOrderAwaitingPayment();
    }

    public function getNameAttribute(): string
    {
        return translate($this->form)->name;
    }

    public function getMergeFields(): array
    {
        return array_merge($this->entry?->getMergeFields() ?? [], [
            'report_name' => htmlspecialchars(lang(translate($this->form), 'name')),
            'report_due' => $this->dueDate,
            'report_url' => route('grant-report.entrant.edit', (string) $this->slug),
            'report_slug' => (string) $this->slug,
        ]);
    }

    public function setOwner(User $user): self
    {
        throw new GrantReportOwnerCannotBeChanged;
    }

    public function ownedBy(User $user): bool
    {
        return $this->getUserId() === $user->id;
    }

    public function ownerName(): string
    {
        return $this->entry->entrant->fullName();
    }

    public function editRoute(): string
    {
        return route('grant-report.entrant.edit', ['grantReport' => (string) $this->slug]);
    }

    public function verticalKey(): string
    {
        return 'grant_report';
    }

    public function getCategoryId(): ?int
    {
        return $this->category->id;
    }

    public function getChapterId(): ?int
    {
        return $this->entry?->chapterId;
    }

    public function displayId(): bool
    {
        return $this->entry->form->settings->displayId;
    }
}
