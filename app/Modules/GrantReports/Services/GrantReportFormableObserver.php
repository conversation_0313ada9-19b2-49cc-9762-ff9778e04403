<?php

namespace AwardForce\Modules\GrantReports\Services;

use AwardForce\Modules\Forms\Formables\Boundary\Formable;
use AwardForce\Modules\Forms\Formables\Boundary\FormableObserver;
use AwardForce\Modules\Forms\Formables\ResourceId;
use AwardForce\Modules\GrantReports\Models\GrantReport;

class GrantReportFormableObserver implements FormableObserver
{
    public function create(array $resourceData): Formable
    {
        return new GrantReport;
    }

    public function update(ResourceId $formableId, array $requestData): Formable
    {
        // TODO: Implement update() method.
    }

    public function delete(ResourceId $formableId): bool
    {
        // TODO: Implement delete() method.
    }

    public function created(): void
    {
        // TODO: Implement created() method.
    }

    public function validating(): void
    {
        // TODO: Implement validating() method.
    }
}
