<?php

namespace AwardForce\Modules\Settings\Events;

use AwardForce\Modules\Audit\Events\Activity;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;

class OrganisationsSettingsWereUpdated implements Activity, SettingsWereUpdated
{
    public function __construct(private array $changes = [])
    {
    }

    public function changes(): array
    {
        return $this->changes;
    }

    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource('settings'),
            'updated',
            'audit.settings.organisations.updated',
            $this->changes
        );
    }
}
