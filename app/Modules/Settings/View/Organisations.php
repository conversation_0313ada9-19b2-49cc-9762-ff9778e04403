<?php

namespace AwardForce\Modules\Settings\View;

use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Platform\View\View;

class Organisations extends View
{
    public function __construct(private SettingRepository $repository)
    {
        VueData::registerRoutes([
            'setting.organisations.toggle',
            'setting.organisations.update',
        ]);

        VueData::registerTranslations([
            'setting.form.organisations',
            'setting.tabs.organisations',
            'buttons.off',
            'buttons.on',
            'buttons.save',
            'buttons.cancel',
        ]);
    }

    public function settings(): OrganisationsSettings
    {
        return $this->repository->organisationSettings();
    }
}
