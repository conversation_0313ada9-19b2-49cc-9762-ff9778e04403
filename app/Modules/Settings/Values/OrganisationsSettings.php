<?php

namespace AwardForce\Modules\Settings\Values;

use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Contracts\Support\Jsonable;

readonly class OrganisationsSettings implements Arrayable, Jsonable
{
    public bool $enabled;
    public bool $joinOnRegisteredEmail;
    public bool $userSelect;
    public bool $userAdd;
    public bool $administratorInvite;
    public bool $managerAdd;

    public function __construct(array $settings = [])
    {
        $this->enabled = (bool) array_get($settings, 'enabled', false);
        $this->joinOnRegisteredEmail = (bool) array_get($settings, 'joinOnRegisteredEmail', true);
        //        $this->userSelect = (bool) array_get($settings, 'userSelect', true);
        //        $this->userAdd = (bool) array_get($settings, 'userAdd', true);
        //        $this->administratorInvite = (bool) array_get($settings, 'administratorInvite', true);
        $this->managerAdd = (bool) array_get($settings, 'managerAdd', true);
    }

    public function toArray(): array
    {
        return [
            'enabled' => $this->enabled,
            'joinOnRegisteredEmail' => $this->joinOnRegisteredEmail,
            //            'userSelect' => $this->userSelect,
            //            'userAdd' => $this->userAdd,
            //            'administratorInvite' => $this->administratorInvite,
            'managerAdd' => $this->managerAdd,
        ];
    }

    public function toJson($options = 0): string
    {
        return json_encode($this->toArray(), $options);
    }

    public function canJoinOnRegisteredEmail(): bool
    {
        return feature_enabled('organisations') && $this->enabled && $this->joinOnRegisteredEmail;
    }
}
