<?php

namespace AwardForce\Modules\Settings\Values;

use AwardForce\Modules\Features\Facades\Feature;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class OrganisationsSettingsTest extends BaseTestCase
{
    use Laravel;

    public function testItSetsCorrectDefaultValues(): void
    {
        $settings = new OrganisationsSettings;

        // The value of `enabled` should be `false` by default, while the rest should be `true`.
        foreach ($settings->toArray() as $key => $value) {
            $this->assertEquals($key !== 'enabled', $value);
        }
    }

    #[TestWith([true, true, true, true])]
    #[TestWith([true, true, false, false])]
    #[TestWith([true, false, true, false])]
    #[TestWith([false, true, true, false])]
    public function testCanJoinOnRegisteredEmail($featureEnabled, $enabled, $joinOnRegisteredEmail, $expectedResult): void
    {
        Feature::clearResolvedInstance(\AwardForce\Modules\Features\Services\Feature::class);
        Feature::shouldReceive('enabled')->with('organisations')->andReturn($featureEnabled);

        $settings = new OrganisationsSettings([
            'enabled' => $enabled,
            'joinOnRegisteredEmail' => $joinOnRegisteredEmail,
        ]);

        $this->assertEquals($expectedResult, $settings->canJoinOnRegisteredEmail());
    }
}
