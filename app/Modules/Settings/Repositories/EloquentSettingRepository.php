<?php

namespace AwardForce\Modules\Settings\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Models\Setting;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Platform\Database\Eloquent\HasQueryBuilder;

/**
 * Class SettingRepository
 */
class EloquentSettingRepository extends Repository implements SettingRepository
{
    use HasQueryBuilder;

    /**
     * Stores a cache of settings per request.
     *
     * @var array
     */
    private $cache = [];

    public function __construct(Setting $setting)
    {
        $this->model = $setting;
    }

    /**
     * Retrieve a setting by its name.
     *
     * @param  string  $key
     * @param  mixed  $default
     * @return mixed
     */
    public function getValueByKey($key, $default = null)
    {
        $setting = $this->getByKey($key);

        return $setting && $setting->isNotEmpty() ? $setting->getValue() : $default;
    }

    /**
     * Retrieves a saved object by the key.
     *
     * @param  string  $key
     * @return Setting|null
     */
    public function getByKey($key)
    {
        return $this->getFromCache($key);
    }

    /**
     * Retrieves all the settings available for the account, but returns the result as an associative array
     * of settingKey => settingValue.
     *
     * @return array
     */
    public function getAllAsKeyValue()
    {
        $settings = $this->getFromCache();
        $formatted = [];

        foreach ($settings as $s) {
            $formatted[$s->key] = $s->value;
        }

        return $formatted;
    }

    /**
     * Save setting with updated value.
     *
     * @return void
     *
     * @throws \Exception
     */
    public function saveSetting(string $settingKey, ?string $settingValue)
    {
        if (is_null($settingValue)) {
            $this->removeByKey($settingKey);
            unset($this->cache[$settingKey]);

            return;
        }

        $setting = Setting::firstOrNew(['account_id' => current_account_id(), 'key' => $settingKey]);
        $setting->value = $settingValue;
        $this->save($setting);

        $this->cache[$settingKey] = $setting;
    }

    /**
     * Removes (forgets) setting by setting key name.
     *
     * @return mixed
     *
     * @throws \Exception
     */
    public function removeByKey(string $key)
    {
        return $this->getQuery()
            ->where('key', $key)
            ->delete();
    }

    /**
     * Returns all non-null settings for the key across all accounts.
     *
     * @param  string  $key
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getForAllAccountsWithKey($key)
    {
        return $this->relaxed(function () use ($key) {
            return $this->getQuery(false)
                ->whereNotNull('value')
                ->where('value', '!=', '')
                ->where('key', $key)
                ->get();
        });
    }

    /**
     * Caches the settings provided.
     *
     * @param  mixed  $settings
     */
    private function cache($settings)
    {
        foreach ($settings as $setting) {
            $this->cache[$setting->key] = $setting;
        }
    }

    /**
     * Return a setting value from the cache.
     *
     * @param  string|null  $key
     * @return null|Setting
     */
    private function getFromCache($key = null)
    {
        if (empty($this->cache)) {
            $this->cache($this->getAll());
        }

        if (! is_null($key)) {
            return array_get($this->cache, $key);
        }

        return $this->cache;
    }

    /**
     * Helper method for checking if `paid-entries` is enabled, alongside the `payments` feature.
     */
    public function paidEntries(): bool
    {
        return feature_enabled('order_payments')
                && setting('paid-entries');
    }

    /**
     * Helper method for checking if entry payments are due on start, by checking
     * if the `payments` feature and `paid-entries` setting are both enabled,
     * before checking the specific `entry-payment` setting.
     */
    public function entryPaymentOnStart(): bool
    {
        return $this->paidEntries()
            && setting('entry-payment') == 'start';
    }

    public function organisationSettings(): OrganisationsSettings
    {
        $existing = $this->getByKey('organisations');

        return new OrganisationsSettings($existing ? json_decode($existing->value, true) : []);
    }

    public function account(int $accountId): self
    {
        $this->relaxed(fn() => $this->query()->where('account_id', $accountId));

        return $this;
    }

    public function keys(array $keys): self
    {
        $this->query()->whereIn('key', $keys);

        return $this;
    }

    public function saveWithCache(Setting $setting): ?bool
    {
        $this->cache[$setting->key] = $setting;

        return $this->save($setting);
    }
}
