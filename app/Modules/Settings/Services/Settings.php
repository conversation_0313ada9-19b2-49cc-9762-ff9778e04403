<?php

namespace AwardForce\Modules\Settings\Services;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Payments\Events\PaymentSettingsWereUpdated;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Events\GeneralSettingsWereUpdated;
use AwardForce\Modules\Settings\Events\OrganisationsSettingsWereUpdated;
use AwardForce\Modules\Settings\Models\Setting;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Illuminate\Support\Arr;
use Platform\Events\Raiseable;

class Settings
{
    use Raiseable;

    public function __construct(protected SettingRepository $settings)
    {
    }

    /**
     * Handle updating general settings that are handled by SettingsRepository.
     *
     * @throws \Exception
     */
    public function updateGeneralSettings(array $newSettings)
    {
        $this->update($newSettings, GeneralSettingsWereUpdated::class);
    }

    /**
     * Handle updating payment settings that are handled by SettingsRepository.
     *
     * @throws \Exception
     */
    public function updatePaymentSettings(array $newSettings)
    {
        $this->update($newSettings, PaymentSettingsWereUpdated::class);
    }

    /**
     * Handles updating of a single general setting by key.
     *
     *
     * @throws \Exception
     */
    public function updateSingleGeneralSetting(string $key, ?string $value)
    {
        $oldValue = $this->settings->getByKey($key)->value ?? null;

        if ($oldValue !== $value) {
            $this->settings->saveSetting($key, $value);

            $settingsThatChanged = [$key => $value];

            $this->raise(new GeneralSettingsWereUpdated($settingsThatChanged));
        }
    }

    public function disableSocialAuthentication(string $provider): void
    {
        $existingProviders = explode(',', $this->settings->getValueByKey('social-authentication', ''));

        if (($key = array_search($provider, $existingProviders)) !== false) {
            unset($existingProviders[$key]);
            $this->settings->saveSetting('social-authentication', implode(',', $existingProviders));
        }
    }

    /**
     * Handle generic settings update, common for both General Settings and Payment Settings.
     */
    private function update(array $newSettings, string $eventToRaiseClass)
    {
        $settingsThatChanged = array_diff_assoc($newSettings, $this->settings->getAllAsKeyValue());
        $event = new $eventToRaiseClass($settingsThatChanged);

        $this->saveSettings($newSettings, current_account());

        $this->raise($event);
    }

    public function samlAuthOnly(): bool
    {
        return (bool) $this->settings->getValueByKey('enable-saml') &&
            (bool) $this->settings->getValueByKey('disable-login-form') &&
            ! $this->settings->getValueByKey('social-authentication');
    }

    public function getValueByKey(string $key)
    {
        return $this->settings->getValueByKey($key);
    }

    public function saveSmsSettings(Account $account, array $settings): void
    {
        $this->saveSettings($settings, $account);

        $this->raise(new GeneralSettingsWereUpdated($settings));
    }

    /**
     * @param  array  $keys Key-value pairs of settings to save [key => value]
     */
    public function saveSettings(array $keys, Account $account): void
    {
        $existingSettings = $this->settings
            ->account($account->id)
            ->keys(array_keys($keys))
            ->fields(['settings.id', 'settings.key', 'settings.value'])
            ->get()
            ->keyBy('key');

        foreach ($keys as $key => $value) {
            $setting = Arr::get($existingSettings, $key, new Setting(['key' => $key, 'account_id' => $account->id]));
            $setting->value = $value;
            $this->saveSetting($setting);
        }
    }

    private function saveSetting(Setting $setting): void
    {
        // Delete setting if value is null
        if (is_null($setting->value)) {
            $this->settings->delete($setting);

            return;
        }

        // Save setting
        $this->settings->saveWithCache($setting);
    }

    public function getByAccountAndKey(Account $account, string $key): ?Setting
    {
        return $this->settings
            ->account($account->id)
            ->keys([$key])
            ->fields(['settings.id', 'settings.key', 'settings.value'])
            ->first();
    }

    public function updateOrganisationsSettings(OrganisationsSettings $newSettings): void
    {
        $this->update(['organisations' => $newSettings->toJson()], OrganisationsSettingsWereUpdated::class);
    }

    public function organisationSettings(): OrganisationsSettings
    {
        return $this->settings->organisationSettings();
    }
}
