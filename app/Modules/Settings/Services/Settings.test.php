<?php

namespace AwardForce\Modules\Settings\Services;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Payments\Events\PaymentSettingsWereUpdated;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Events\GeneralSettingsWereUpdated;
use AwardForce\Modules\Settings\Events\OrganisationsSettingsWereUpdated;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Platform\Test\EventAssertions;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class SettingsTest extends BaseTestCase
{
    use Database;
    use EventAssertions;
    use Laravel;

    /** @var SettingRepository */
    protected $settings;

    /** @var Settings */
    private $settingsService;

    protected function setUp(): void
    {
        parent::setUp();

        $this->settings = $this->app->make(SettingRepository::class);
        $this->settingsService = $this->app->make(Settings::class);

        $this->settingsService->saveSettings([
            'app-site-name' => 'AwardForce',
            'paid-entries' => '0',
            'timezone' => 'Europe/Warsaw',
            'social-authentication' => 'facebook,twitter,google,wordpress',
        ], current_account());
    }

    /**
     * @throws \Exception
     */
    public function testGeneralSettingsAreSaved(): void
    {
        $this->settingsService->updateGeneralSettings([
            'app-site-name' => '',
            'paid-entries' => '0',
            'timezone' => 'Europe/London',
            'social-authentication' => 'facebook,twitter,google,wordpress',
        ]);

        // Ensure settings were saved
        $this->assertEquals($this->settings->getAllAsKeyValue(), [
            'app-site-name' => '',
            'paid-entries' => '0',
            'timezone' => 'Europe/London',
            'social-authentication' => 'facebook,twitter,google,wordpress',
        ]);
    }

    public function testSingleGeneralSettingIsSaved(): void
    {
        $this->settingsService->updateSingleGeneralSetting('timezone', 'Europe/Paris');

        // Ensure setting was saved
        $this->assertEquals($this->settings->getAllAsKeyValue(), [
            'app-site-name' => 'AwardForce',
            'paid-entries' => '0',
            'timezone' => 'Europe/Paris',
            'social-authentication' => 'facebook,twitter,google,wordpress',
        ]);

        // Ensure the event was raised and that service properly identified changes for the audit log
        $this->assertRaised($this->settingsService, GeneralSettingsWereUpdated::class, function ($event) {
            return $event->changes() === [
                'timezone' => 'Europe/Paris',
            ];
        });

        $this->settingsService->updateSingleGeneralSetting('app-site-name', null);

        // Ensure setting was removed
        $this->assertEquals($this->settings->getAllAsKeyValue(), [
            'paid-entries' => '0',
            'timezone' => 'Europe/Paris',
            'social-authentication' => 'facebook,twitter,google,wordpress',
        ]);
    }

    /**
     * @throws \Exception
     */
    public function testLogGeneralSettingsThatChanged(): void
    {
        $this->settingsService->updateGeneralSettings([
            'app-site-name' => '',
            'timezone' => 'Europe/Stockholm',
        ]);

        // Ensure that the event was raised and that service properly identified changes for the audit log
        $this->assertRaised($this->settingsService, GeneralSettingsWereUpdated::class, function ($event) {
            return $event->changes() === [
                'app-site-name' => '',
                'timezone' => 'Europe/Stockholm',
            ];
        });
    }

    /**
     * @throws \Exception
     */
    public function testPaymentSettingsAreSaved(): void
    {
        // Update settings using service, not repository
        $this->settingsService->updatePaymentSettings([
            'paid-entries' => '1',
        ]);

        // Ensure settings were saved
        $this->assertEquals([
            'app-site-name' => 'AwardForce',
            'paid-entries' => '1',
            'timezone' => 'Europe/Warsaw',
            'social-authentication' => 'facebook,twitter,google,wordpress',
        ], $this->settings->getAllAsKeyValue());
    }

    /**
     * @throws \Exception
     */
    public function testLogPaymentSettingsThatChanged(): void
    {
        // Update settings using service, not repository
        $this->settingsService->updatePaymentSettings([
            'paid-entries' => '1',
        ]);

        // Ensure that the event was raised and that service properly identified changes for the audit log
        $this->assertRaised($this->settingsService, PaymentSettingsWereUpdated::class, function ($event) {
            return $event->changes() === [
                'paid-entries' => '1',
            ];
        });
    }

    public function testItCanDisableSocialAuthentication(): void
    {
        $this->settingsService->disableSocialAuthentication('wordpress');

        $this->assertEquals($this->settings->getAllAsKeyValue(), [
            'app-site-name' => 'AwardForce',
            'paid-entries' => '0',
            'timezone' => 'Europe/Warsaw',
            'social-authentication' => 'facebook,twitter,google',
        ]);
    }

    public function testSamlAuthIsTrue(): void
    {
        $this->settingsService->saveSettings([
            'enable-saml' => '1',
            'disable-login-form' => '1',
            'social-authentication' => '',
        ], current_account());

        $this->assertTrue($this->settingsService->samlAuthOnly());
    }

    public function testSamlAuthIsFalse(): void
    {
        $this->settingsService->saveSettings([
            'enable-saml' => '0',
            'disable-login-form' => '1',
            'social-authentication' => '',
        ], current_account());

        $this->assertFalse($this->settingsService->samlAuthOnly());

        $this->settingsService->saveSettings([
            'enable-saml' => '1',
            'disable-login-form' => '0',
            'social-authentication' => '',
        ], current_account());

        $this->assertFalse($this->settingsService->samlAuthOnly());

        $this->settingsService->saveSettings([
            'enable-saml' => '1',
            'disable-login-form' => '1',
            'social-authentication' => 'facebook',
        ], current_account());

        $this->assertFalse($this->settingsService->samlAuthOnly());
    }

    public function testItCreatesNewSettingsWhenTheyDontExist()
    {
        $account = $this->muffin(Account::class);
        $this->settingsService->saveSmsSettings($account, [
            'twilio-api-key' => 'twilio-api-key-value',
            'twilio-api-secret' => 'twilio-api-secret-value',
            'twilio-account-sid' => 'twilio-account-sid-value',
        ]);

        $this->assertSame('twilio-api-key-value', $this->settingsService->getByAccountAndKey($account, 'twilio-api-key')->value);
        $this->assertSame('twilio-api-secret-value', $this->settingsService->getByAccountAndKey($account, 'twilio-api-secret')->value);
        $this->assertSame('twilio-account-sid-value', $this->settingsService->getByAccountAndKey($account, 'twilio-account-sid')->value);

    }

    public function testItUpdatesExistingSettings()
    {
        $account = $this->muffin(Account::class);

        CurrentAccount::set($account);

        $this->settingsService->saveSettings([
            'twilio-api-key' => 'twilio-api-key-value',
            'twilio-api-secret' => 'twilio-api-secret-value',
            'twilio-account-sid' => 'twilio-account-sid-value',
        ], current_account());

        $this->settingsService->saveSmsSettings($account, [
            'twilio-api-key' => 'twilio-api-key-value-updated',
            'twilio-api-secret' => 'twilio-api-secret-value-updated',
        ]);

        $this->assertSame('twilio-api-key-value-updated', $this->settingsService->getByAccountAndKey($account, 'twilio-api-key')->value);
        $this->assertSame('twilio-api-secret-value-updated', $this->settingsService->getByAccountAndKey($account, 'twilio-api-secret')->value);
        $this->assertSame('twilio-account-sid-value', $this->settingsService->getByAccountAndKey($account, 'twilio-account-sid')->value);
    }

    public function testItDeletesExistingSettingsWhenReceivesNullValue()
    {
        CurrentAccount::set($account = $this->muffin(Account::class));

        $this->settingsService->saveSettings([
            'twilio-api-key' => 'twilio-api-key-value',
            'twilio-api-secret' => 'twilio-api-secret-value',
            'twilio-account-sid' => 'twilio-account-sid-value',
        ], current_account());

        $this->settingsService->saveSmsSettings($account, [
            'twilio-api-key' => null,
            'twilio-api-secret' => null,
        ]);

        $this->assertNull($this->settingsService->getByAccountAndKey($account, 'twilio-api-key'));
        $this->assertNull($this->settingsService->getByAccountAndKey($account, 'twilio-api-secret'));
        $this->assertSame('twilio-account-sid-value', $this->settingsService->getByAccountAndKey($account, 'twilio-account-sid')->value);
    }

    public function testOrganisationsSettingsAreSaved(): void
    {
        $organisationsSettings = new OrganisationsSettings([
            'enabled' => true,
            'joinOnRegisteredEmail' => false,
            'userSelect' => false,
            'userAdd' => true,
            'administratorInvite' => false,
            'managerAdd' => false,
        ]);

        $this->settingsService->updateOrganisationsSettings($organisationsSettings);

        $this->assertRaised($this->settingsService, OrganisationsSettingsWereUpdated::class, function ($event) use ($organisationsSettings) {
            $this->assertEquals(['organisations' => $organisationsSettings->toJson()], $event->changes());

            return true;
        });
    }

    public function testItReturnsOrganisationSettings(): void
    {
        $this->settingsService->saveSettings(['organisations' => json_encode(['enabled' => false, 'joinOnRegisteredEmail' => true])], current_account());

        $settings = $this->settingsService->organisationSettings();

        $this->assertInstanceOf(OrganisationsSettings::class, $settings);
        $this->assertFalse($settings->enabled);
        $this->assertTrue($settings->joinOnRegisteredEmail);
    }
}
