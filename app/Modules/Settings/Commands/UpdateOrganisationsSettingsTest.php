<?php

namespace AwardForce\Modules\Settings\Commands;

use AwardForce\Modules\Settings\Services\Settings;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Mockery as m;
use Tests\BaseTestCase;

class UpdateOrganisationsSettingsTest extends BaseTestCase
{
    public function testItUpdatesOrganisationSettings(): void
    {
        $organisationSettings = new OrganisationsSettings([
            'enabled' => true,
            'joinOnRegisteredEmail' => true,
        ]);
        $command = new UpdateOrganisationsSettings($organisationSettings);
        $handler = new UpdateOrganisationsSettingsHandler($settings = m::mock(Settings::class));

        $settings->shouldReceive('updateOrganisationsSettings')->once()->with($organisationSettings);
        $handler->handle($command);
    }
}
