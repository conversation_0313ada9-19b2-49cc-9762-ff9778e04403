<?php

namespace AwardForce\Modules\Settings\Contracts;

use AwardForce\Modules\Settings\Models\Setting;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Repository;

interface SettingRepository extends BuilderRepository, Repository
{
    /**
     * Retrieve a setting by its name.
     *
     * @param  string  $key
     * @param  mixed  $default
     * @return mixed
     */
    public function getValueByKey($key, $default = null);

    /**
     * Retrieves a saved object by the key.
     *
     * @param  string  $key
     * @return Setting|null
     */
    public function getByKey($key);

    /**
     * Returns all of the settings, but returns an array as a key => value result.
     *
     * @return array
     */
    public function getAllAsKeyValue();

    /**
     * Save setting with updated value.
     *
     * @return void
     */
    public function saveSetting(string $settingKey, ?string $settingValue);

    /**
     * Removes (forgets) setting by setting key name.
     *
     * @return mixed
     *
     * @throws \Exception
     */
    public function removeByKey(string $key);

    /**
     * Returns all non-null settings for the key across all accounts.
     *
     * @param  string  $key
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getForAllAccountsWithKey($key);

    /**
     * Helper method for checking if `paid-entries` is enabled, alongside the `payments` feature.
     */
    public function paidEntries(): bool;

    /**
     * Helper method for checking if entry payments are due on start, by checking
     * if the `payments` feature and `paid-entries` setting are both enabled,
     * before checking the specific `entry-payment` setting.
     */
    public function entryPaymentOnStart(): bool;

    public function account(int $accountId): self;

    public function keys(array $keys): self;

    public function saveWithCache(Setting $setting): ?bool;

    public function organisationSettings(): OrganisationsSettings;
}
