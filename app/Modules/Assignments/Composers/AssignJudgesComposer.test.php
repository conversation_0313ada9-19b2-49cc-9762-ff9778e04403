<?php

namespace AwardForce\Modules\Assignments\Composers;

use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class AssignJudgesComposerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testComposerArguments(): void
    {
        $judges = [
            $this->muffin(User::class, ['email' => null, 'mobile' => null]),
            $this->muffin(User::class, ['email' => null, 'mobile' => null]),
        ];
        $judgesFormatted = array_map(fn($judge) => [
            'id' => $judge->id,
            'name' => $judge->name,
            'title' => '-',
        ], $judges);
        $judgeIds = array_map(fn($judge) => $judge->id, $judges);
        $scoreSet = $this->muffin(ScoreSet::class);
        session()->put('manual-assignment.score-set-id', $scoreSet->id);
        $round = $this->muffin(Round::class, ['round_type' => 'judge']);
        session()->put('manual-assignment.round-id', $round->id);
        $role = $this->muffin(Role::class);
        session()->put('manual-assignment.role-id', $role->id);
        session()->put('manual-assignment.role-search-keywords', $keywords = Str::random());
        session()->put('manual-assignment.judges', $judgeIds);
        $notification = $this->muffin(Notification::class, ['trigger' => 'assignment.created']);
        session()->put('manual-assignment.send-notifications', false);
        session()->put('manual-assignment.notification-id', $notification->id);

        $userRepository = $this->mock(UserRepository::class);
        $userRepository->shouldReceive('getByIds')->once()->with($judgeIds)->andReturn(collect($judges));

        $composer = new AssignJudgesComposer(
            app(ScoreSetRepository::class),
            app(RoundRepository::class),
            app(RoleRepository::class),
            app(NotificationRepository::class),
            app(UserRepository::class)
        );

        $composer->compose($view = $this->spy(View::class));

        $view->shouldHaveReceived('with')->once()
            ->withArgs(function ($args) use ($scoreSet, $round, $role, $judgesFormatted, $notification, $keywords) {
                $this->assertArrayHasKey('scoreSets', $args);
                $this->assertArrayHasKey('scoreSetId', $args);
                $this->assertArrayHasKey('rounds', $args);
                $this->assertArrayHasKey('roundId', $args);
                $this->assertArrayHasKey('roles', $args);
                $this->assertArrayHasKey('roleId', $args);
                $this->assertArrayHasKey('roleSearchKeywords', $args);
                $this->assertArrayHasKey('judges', $args);
                $this->assertArrayHasKey('notifications', $args);
                $this->assertArrayHasKey('sendNotifications', $args);
                $this->assertArrayHasKey('notificationId', $args);

                $this->assertEquals($scoreSet->id, collect($args['scoreSets'])->first()->id);
                $this->assertEquals($scoreSet->id, $args['scoreSetId']);

                $this->assertEquals($round->id, collect($args['rounds'])->first()->id);
                $this->assertEquals($round->id, $args['roundId']);
                $this->assertEquals($role->id, collect($args['roles'])->first()->id);
                $this->assertEquals($role->id, $args['roleId']);
                $this->assertEquals($keywords, $args['roleSearchKeywords']);
                $this->assertEquals($judgesFormatted, $args['judges']);
                $this->assertEquals($notification->id, collect($args['notifications'])->first()->id);
                $this->assertFalse($args['sendNotifications']);
                $this->assertEquals($notification->id, $args['notificationId']);

                return true;
            });
    }

    public function testJudgesNotQueriedWhenEmpty(): void
    {
        session()->put('manual-assignment.judges', []);

        $userRepository = $this->mock(UserRepository::class);
        $userRepository->shouldNotReceive('getByIds');

        $composer = app(AssignJudgesComposer::class, ['userRepository' => $userRepository]);
        $composer->judges();
    }
}
