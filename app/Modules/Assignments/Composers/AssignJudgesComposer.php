<?php

namespace AwardForce\Modules\Assignments\Composers;

use AwardForce\Library\Composers\CacheableComposer;
use AwardForce\Library\Composers\ComposeCache;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Consumer;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;

class AssignJudgesComposer implements CacheableComposer
{
    use ComposeCache;

    private Form $form;

    public function __construct(
        private ScoreSetRepository $scoreSets,
        private RoundRepository $rounds,
        private RoleRepository $roles,
        private NotificationRepository $notifications,
        private UserRepository $userRepository,
    ) {
        $this->form = FormSelector::selectedOrDefault();
    }

    public function compose(View $view): void
    {
        $view->with([
            'scoreSets' => $this->scoreSets(),
            'scoreSetId' => $this->scoreSetId(),
            'rounds' => $this->rounds(),
            'roundId' => $this->roundId(),
            'roles' => $this->roles(),
            'roleId' => $this->roleId(),
            'roleSearchKeywords' => $this->roleSearchKeywords(),
            'judges' => $this->judges(),
            'notifications' => $this->notifications(),
            'sendNotifications' => $this->sendNotifications(),
            'notificationId' => $this->notificationId(),
            'translations' => $this->translations(),
        ]);
    }

    private function scoreSets(): Collection
    {
        return translate($this->scoreSets->getForForm($this->form->id));
    }

    private function scoreSetId(): ?int
    {
        return session('manual-assignment.score-set-id');
    }

    private function rounds(): Collection
    {
        return translate($this->rounds->getJudgingForForm($this->form->id));
    }

    private function roundId(): ?int
    {
        return session('manual-assignment.round-id');
    }

    private function roles(): Collection
    {
        return translate($this->roles->requestCache()->getAll());
    }

    private function roleId(): ?int
    {
        return session('manual-assignment.role-id');
    }

    private function roleSearchKeywords(): ?string
    {
        return session('manual-assignment.role-search-keywords');
    }

    public function judges(): array
    {
        if (empty($judgeIds = session('manual-assignment.judges', []))) {
            return [];
        }

        $judges = $this->userRepository->getByIds($judgeIds);

        return $judges->map(
            fn(User $judge) => [
                'id' => $judge->id,
                'name' => $judge->name,
                'title' => $judge->preferredContact() ?? '-',
            ]
        )->toArray();
    }

    private function notifications(): Collection
    {
        return translate($this->notifications->getByTrigger('assignment.created', $this->form->seasonId));
    }

    private function sendNotifications(): bool
    {
        return (bool) session('manual-assignment.send-notifications', true);
    }

    private function notificationId(): ?int
    {
        return session('manual-assignment.notification-id');
    }

    private function translations(): array
    {
        return translations_for_vue(Consumer::languageCode(), [
            'assignments.form.judges.capped',
            'buttons.search',
            'multiselect.randomizer.button',
            'multiselect.randomizer.helpicon-judges',
            'multiselect.randomizer.how_many',
            'multiselect.randomizer.show',
            'multiselect.select_all',
            'panels.form.advanced',
            'panels.form.judges.guest_role_warning',
            'panels.form.judges.options',
            'panels.form.judges.placeholder.roles',
            'panels.form.reset',
            'panels.form.role.label',
            'panels.form.selected-judges.header',
            'search.nothing-found',
        ]);
    }
}
