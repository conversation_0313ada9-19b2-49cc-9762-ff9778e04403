<?php

namespace AwardForce\Modules\Menu;

use AwardForce\Modules\Menu\Documents\Links\Document;
use AwardForce\Modules\Menu\Forms\Links\Form;
use AwardForce\Modules\Menu\Grants\GrantReports;
use AwardForce\Modules\Menu\Grants\Grants;
use AwardForce\Modules\Menu\Judging\Judging;
use AwardForce\Modules\Menu\Judging\Links\JudgeDashboard;
use AwardForce\Modules\Menu\Judging\Links\JudgeLeaderboard;
use AwardForce\Modules\Menu\Links\ActionTasks;
use AwardForce\Modules\Menu\Links\Contracts;
use AwardForce\Modules\Menu\Links\Dashboard;
use AwardForce\Modules\Menu\Links\GuidesAndTours;
use AwardForce\Modules\Menu\Organisations\Links\Organisation;
use AwardForce\Modules\Menu\Payments\Payments;
use AwardForce\Modules\Menu\Users\Links\User;
use Platform\Menu\Context\Menu;

class MainMenu extends Menu
{
    public function menuItems(): array
    {
        return [
            new Dashboard,
            new GuidesAndTours,
            new Entries\Entries,
            new ActionTasks,
            new ReviewFlow\ReviewFlow,
            new Document,
            new Judging,
            new JudgeDashboard,
            new JudgeLeaderboard,
            new Grants,
            new GrantReports,
            new Payments,
            new Contracts,
            new User,
            new Form,
            new Settings\Settings,
            new Organisation,
        ];
    }

    public function icon(): string
    {
        return '';
    }

    public function name(): string
    {
        return 'main-menu';
    }

    public function text(): string
    {
        return '';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
