<?php

namespace AwardForce\Modules\Menu\Forms\Links;

use AwardForce\Library\Authorization\Consumer;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class Form extends Link
{
    public function name(): string
    {
        return 'form';
    }

    public function text(): string
    {
        return trans('form.titles.menu');
    }

    public function link(): string
    {
        return route('forms.index');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return 'accounts';
    }

    public function applies(): bool
    {
        $grantOrMultiform = is_goodgrants() || feature_enabled('multiform');

        return Consumer::can('view', 'Forms') && $grantOrMultiform;
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 16;
    }
}
