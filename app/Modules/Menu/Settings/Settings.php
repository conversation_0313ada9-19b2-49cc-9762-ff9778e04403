<?php

namespace AwardForce\Modules\Menu\Settings;

use AwardForce\Modules\Menu\Grants\GrantsSettings;
use Platform\Menu\Context\Menu;

class Settings extends Menu
{
    public function name(): string
    {
        return 'settings';
    }

    public function text(): string
    {
        return trans('menu.settings');
    }

    public function menuItems(): array
    {
        return [
            new General,
            new Content,
            new Entries,
            new Judging,
            new GrantsSettings,
            new Communications,
            new Payments,
            new Users,
            new Organisations,
            new Integrations,
            new Audit,
        ];
    }

    public function icon(): string
    {
        return 'settings';
    }

    public function extras(): array
    {
        return ['class' => 'menu-bottom-fixed'];
    }

    public function position(): int
    {
        return 99;
    }
}
