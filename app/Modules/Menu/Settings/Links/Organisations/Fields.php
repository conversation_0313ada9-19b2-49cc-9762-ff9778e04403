<?php

namespace AwardForce\Modules\Menu\Settings\Links\Organisations;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class Fields extends Link
{
    public function name(): string
    {
        return 'organisation-fields';
    }

    public function text(): string
    {
        return trans('fields.titles.main');
    }

    public function link(): string
    {
        return route('field.index').'?resource='.Field::RESOURCE_ORGANISATIONS;
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        return feature_enabled('organisations') && Consumer::can('view', 'Fields');
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
