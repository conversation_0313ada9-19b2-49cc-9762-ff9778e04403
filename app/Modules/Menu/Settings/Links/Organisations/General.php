<?php

namespace AwardForce\Modules\Menu\Settings\Links\Organisations;

use AwardForce\Library\Authorization\Consumer;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class General extends Link
{
    public function name(): string
    {
        return 'organisation';
    }

    public function text(): string
    {
        return trans('setting.tabs.general');
    }

    public function link(): string
    {
        return route('setting.organisations');
    }

    public function target(): string
    {
        return '';
    }

    public function icon(): string
    {
        return '';
    }

    public function applies(): bool
    {
        return feature_enabled('organisations') && Consumer::can('view', 'Settings');
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
