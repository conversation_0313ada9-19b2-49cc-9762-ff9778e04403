<?php

namespace AwardForce\Modules\Menu\Settings;

use AwardForce\Modules\Menu\Settings\Links\Organisations\Fields;
use AwardForce\Modules\Menu\Settings\Links\Organisations\General;
use Platform\Menu\Context\Menu;

class Organisations extends Menu
{
    public function name(): string
    {
        return 'organisation-settings';
    }

    public function text(): string
    {
        return trans('setting.tabs.organisations');
    }

    public function menuItems(): array
    {
        return [
            new General,
            new Fields,
        ];
    }

    public function icon(): string
    {
        return '';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
