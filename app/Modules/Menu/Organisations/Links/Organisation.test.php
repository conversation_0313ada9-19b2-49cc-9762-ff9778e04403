<?php

namespace AwardForce\Modules\Menu\Organisations\Links;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Features\Facades\Feature;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class OrganisationTest extends BaseTestCase
{
    use Laravel;

    #[TestWith([true, false])]
    #[TestWith([true, true])]
    #[TestWith([false, true])]
    #[TestWith([false, false])]
    public function testItAppliesCorrectly(bool $featureEnabled, bool $canView): void
    {
        $link = new Organisation;

        Feature::shouldReceive('enabled')
            ->with('organisations')
            ->andReturn($featureEnabled);

        Consumer::shouldReceive('can')
            ->with('view', 'Organisations')
            ->andReturn($canView);

        $this->assertEquals($featureEnabled && $canView, $link->applies());

    }
}
