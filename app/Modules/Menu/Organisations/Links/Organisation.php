<?php

namespace AwardForce\Modules\Menu\Organisations\Links;

use Consumer;
use Platform\Menu\Context\Items\Manage;
use Platform\Menu\Context\Link;

class Organisation extends Link
{
    public function link(): string
    {
        return route('organisation.index');
    }

    public function target(): ?string
    {
        return '';
    }

    public function applies(): bool
    {
        return feature_enabled('organisations') && Consumer::can('view', 'Organisations');
    }

    public function contexts(): array
    {
        return [new Manage];
    }

    public function name(): string
    {
        return 'organisations';
    }

    public function text(): string
    {
        return trans('organisations.titles.main');
    }

    public function icon(): string
    {
        return 'organisation';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 15;
    }
}
