<?php

namespace AwardForce\Modules\Files\Commands;

use AwardForce\Library\Filesystem\Storage;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Events\FileWasDeleted;
use Illuminate\Queue\InteractsWithQueue;
use Platform\Events\EventDispatcher;

class DeleteFileCommandHandler
{
    use EventDispatcher;
    use InteractsWithQueue;

    /** @var Storage */
    private $filesystem;

    /** @var FileRepository */
    private $files;

    public function __construct(Storage $filesystem, FileRepository $files)
    {
        $this->filesystem = $filesystem;
        $this->files = $files;
    }

    public function handle(DeleteFileCommand $command)
    {
        $file = $command->file;

        if ($file->caption) {
            $this->deleteFile($file->caption);
        }

        $this->deleteFile($file);
    }

    private function deleteFile($file)
    {
        if ($this->filesystem->exists($remote = $file->file)) {
            $this->filesystem->delete($remote);
        }

        $this->dispatch([new FileWasDeleted($file)]);
        $this->files->delete($file);
    }
}
