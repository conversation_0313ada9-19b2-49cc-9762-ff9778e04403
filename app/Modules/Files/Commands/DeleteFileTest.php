<?php

namespace AwardForce\Modules\Files\Commands;

use AwardForce\Modules\Files\Models\File;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Factory\MockStorage;

final class DeleteFileTest extends BaseTestCase
{
    use Database;
    use Laravel;
    use MockStorage;

    /** @var DeleteFileCommandHandler */
    private $handler;

    public function init()
    {
        $this->mockStorage();
        $this->handler = app(DeleteFileCommandHandler::class);
    }

    public function testDeleteFile(): void
    {
        $file = $this->addFile();

        $this->assertNotNull(File::find($file->id));

        $this->storage->shouldReceive('exists')->with($file->file)->andReturnTrue();
        $this->storage->shouldReceive('delete')->with($file->file)->andReturnTrue();

        $this->handler->handle(new DeleteFileCommand($file));

        $this->storage->shouldHaveReceived('exists')->with($file->file);
        $this->storage->shouldHaveReceived('delete')->with($file->file);

        $this->assertNull(File::find($file->id));
    }

    public function testDeleteFileWithCaption(): void
    {
        $file = $this->addFile();
        $caption = $this->addFile(true, $file->id);

        $this->assertNotNull(File::find($file->id));
        $this->assertNotNull(File::find($caption->id));

        $this->storage->shouldReceive('exists')->with($caption->file)->andReturnTrue();
        $this->storage->shouldReceive('exists')->with($file->file)->andReturnTrue();
        $this->storage->shouldReceive('delete')->with($caption->file)->andReturnTrue();
        $this->storage->shouldReceive('delete')->with($file->file)->andReturnTrue();

        $this->handler->handle(new DeleteFileCommand($file));

        $this->storage->shouldHaveReceived('exists')->with($caption->file);
        $this->storage->shouldHaveReceived('delete')->with($file->file);
        $this->storage->shouldHaveReceived('exists')->with($caption->file);
        $this->storage->shouldHaveReceived('delete')->with($file->file);

        $this->assertNull(File::find($caption->id));
        $this->assertNull(File::find($file->id));
    }

    private function addFile($caption = false, $foreignId = 0)
    {
        if (! $caption) {
            $resource = File::RESOURCE_FILE_FIELD;
            $resourceId = 123;
            $fileFile = 'file.mp4';
        } else {
            $resource = File::RESOURCE_CAPTION;
            $resourceId = null;
            $fileFile = 'file.vtt';
        }
        $file = new File;
        $file->accountId = current_account_id();
        $file->resource = $resource;
        $file->resourceId = $resourceId;
        $file->file = $fileFile;
        $file->original = 'original-'.$fileFile;
        $file->size = 129492;
        $file->foreignId = $foreignId;
        $file->mime = '';

        $file->save();

        return $file;
    }
}
