<?php

namespace AwardForce\Modules\Files;

use AwardForce\Application;
use AwardForce\Library\Cloud\Aws\Adapters\AwsCredentialsAdapter;
use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\Categories\Events\CategoryImageWasDeleted;
use AwardForce\Modules\Chapters\Events\ChapterImageWasDeleted;
use AwardForce\Modules\Contract\Events\ContractAttachmentDeleted;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Contracts\Transcoder;
use AwardForce\Modules\Files\Contracts\TranscodeRepository;
use AwardForce\Modules\Files\Contracts\Transcriber;
use AwardForce\Modules\Files\Contracts\TranscriptionRepository;
use AwardForce\Modules\Files\Contracts\VideoLogRepository;
use AwardForce\Modules\Files\Events\FileProcessed;
use AwardForce\Modules\Files\Events\FilesWereDestroyed;
use AwardForce\Modules\Files\Events\FileUploaded;
use AwardForce\Modules\Files\Events\FileWasCopied;
use AwardForce\Modules\Files\Events\FileWasDeleted;
use AwardForce\Modules\Files\Events\FileWasOrphaned;
use AwardForce\Modules\Files\Events\TranscodeStatusWasUpdated;
use AwardForce\Modules\Files\Events\TranscodeWasLogged;
use AwardForce\Modules\Files\Events\TranscriptionCompleted;
use AwardForce\Modules\Files\Listeners\DeleteFileListener;
use AwardForce\Modules\Files\Listeners\FileCopier;
use AwardForce\Modules\Files\Listeners\MetadataListener;
use AwardForce\Modules\Files\Listeners\TranscodingListener;
use AwardForce\Modules\Files\Listeners\TranscribingListener;
use AwardForce\Modules\Files\Repositories\EloquentFileRepository;
use AwardForce\Modules\Files\Repositories\EloquentTranscodeRepository;
use AwardForce\Modules\Files\Repositories\EloquentTranscriptionRepository;
use AwardForce\Modules\Files\Repositories\EloquentVideoLogRepository;
use AwardForce\Modules\Files\Services\AWSMediaConvert;
use AwardForce\Modules\Files\Services\AWSTranscriber;
use AwardForce\Modules\Files\Services\FileProcessor;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\EmbedExtractor;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\Extractor;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\InstagramVideoDetector;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\TikTokDetector;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\TwitchDetector;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\VideoPlatformDetector;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\VimeoDetector;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\WistiaDetector;
use AwardForce\Modules\Files\Services\Thumbnails\Detectors\YoutubeDetector;
use AwardForce\Modules\Files\Services\Thumbnails\ThumbnailsListener;
use AwardForce\Modules\Forms\Forms\Events\FormCoverImageWasDeleted;
use AwardForce\Modules\ScoreSets\Events\ScoreSetImageWasDeleted;
use Aws\Credentials\Credentials;
use Aws\TranscribeService\TranscribeServiceClient;

class FilesServiceProvider extends ServiceProvider
{
    /**
     * @var bool
     */
    public $defer = true;

    /**
     * Define the listeners for this module.
     *
     * @var array
     */
    protected $listeners = [
        CategoryImageWasDeleted::class => DeleteFileListener::class,
        ChapterImageWasDeleted::class => DeleteFileListener::class,
        ContractAttachmentDeleted::class => DeleteFileListener::class,
        FileProcessed::class => [
            TranscodingListener::class.'@whenFileProcessed',
            TranscribingListener::class.'@whenFileProcessed',
        ],
        FilesWereDestroyed::class => DeleteFileListener::class.'@whenFilesWereDestroyed',
        FileWasCopied::class => FileCopier::class.'@whenFileWasCopied',
        FileWasDeleted::class => [
            TranscodingListener::class.'@whenFileWasDeleted',
            ThumbnailsListener::class.'@whenFileWasDeleted',
        ],
        FileWasOrphaned::class => DeleteFileListener::class,
        ScoreSetImageWasDeleted::class => DeleteFileListener::class,
        FormCoverImageWasDeleted::class => DeleteFileListener::class,
        TranscodeStatusWasUpdated::class => TranscodingListener::class.'@whenTranscodeStatusWasUpdated',
        TranscodeWasLogged::class => TranscodingListener::class.'@whenTranscodeWasLogged',
        TranscriptionCompleted::class => TranscribingListener::class.'@whenTranscriptionWasCompleted',
        FileUploaded::class => MetadataListener::class.'@whenFileUploaded',
    ];

    /**
     * The repository bindings for the Entries module.
     *
     * @var array
     */
    protected $repositories = [
        FileRepository::class => EloquentFileRepository::class,
        TranscodeRepository::class => EloquentTranscodeRepository::class,
        VideoLogRepository::class => EloquentVideoLogRepository::class,
        TranscriptionRepository::class => EloquentTranscriptionRepository::class,
    ];

    public function register()
    {
        parent::register();

        $this->registerTranscoder();
        $this->registerTranscriber();
        $this->registerVideoPlatformDetectors();
        $this->registerTranscribeClient();
    }

    public function boot()
    {
        $this->bootFileProcessor();
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return array_merge(
            array_keys($this->repositories),
            [FileProcessor::class]
        );
    }

    /**
     * Get the events that trigger this service provider to register.
     *
     * @return array
     */
    public function when()
    {
        return array_keys($this->listeners);
    }

    /**
     * Register the transcoder to be used for all our transcoding requirements.
     */
    private function registerTranscoder()
    {
        $this->app->singleton(Transcoder::class, AWSMediaConvert::class);
    }

    private function registerTranscriber()
    {
        $this->app->singleton(Transcriber::class, AWSTranscriber::class);
    }

    private function bootFileProcessor()
    {
        FileProcessor::registerValidators([
            \AwardForce\Modules\Awards\Services\UploadValidator::class,
            \AwardForce\Modules\Categories\Services\UploadValidator::class,
            \AwardForce\Modules\Chapters\Services\UploadValidator::class,
            \AwardForce\Modules\Comments\Validators\CommentUploadValidator::class,
            \AwardForce\Modules\Entries\Services\AttachmentUploadValidator::class,
            \AwardForce\Modules\Entries\Services\UploadValidator::class,
            \AwardForce\Modules\Entries\Services\CaptionValidator::class,
            \AwardForce\Modules\Entries\Services\ContractSignatureValidator::class,
            \AwardForce\Modules\Forms\Fields\Services\UploadValidator::class,
            \AwardForce\Modules\Forms\Fields\Services\ApiUploadValidator::class,
            \AwardForce\Modules\Forms\Forms\Services\UploadValidator::class,
            \AwardForce\Modules\Identity\Users\Services\ImportUploadValidator::class,
            \AwardForce\Modules\Settings\Services\UploadValidator::class,
            \AwardForce\Modules\Theme\Services\UploadValidator::class,
            \AwardForce\Modules\ScoreSets\Services\UploadValidator::class,
            \AwardForce\Modules\DocumentTemplates\Services\UploadValidator::class,
            \AwardForce\Modules\Identity\Users\Services\ProfilePhotoUploadValidator::class,
            \AwardForce\Modules\Organisations\Organisations\Domain\Services\OrganisationLogoUploadValidator::class,
        ]);
    }

    private function registerVideoPlatformDetectors()
    {
        $this->app->bind(Extractor::class, fn(Application $app) => $app->make(EmbedExtractor::class));

        // $app->make for those that are using Embed
        $this->app->bind(VideoPlatformDetector::class, fn(Application $app) => new VideoPlatformDetector([
            $app->make(InstagramVideoDetector::class),
            $app->make(TikTokDetector::class),
            $app->make(TwitchDetector::class),
            $app->make(VimeoDetector::class),
            $app->make(WistiaDetector::class),
            new YoutubeDetector,
        ]));
    }

    private function registerTranscribeClient()
    {
        $this->app->singleton(TranscribeServiceClient::class, function () {
            $region = $this->app['config']->get('awardforce.region');
            $awsCredentials = $this->app->get(AwsCredentialsAdapter::class);

            return new TranscribeServiceClient([
                'region' => config("filesystems.disks.s3-$region.region"),
                'version' => 'latest',
                'credentials' => new Credentials($awsCredentials->key(), $awsCredentials->secret(), $awsCredentials->token()),
            ]);

        });
    }
}
