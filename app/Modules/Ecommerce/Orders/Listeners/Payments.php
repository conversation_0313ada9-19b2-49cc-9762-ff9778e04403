<?php

namespace AwardForce\Modules\Ecommerce\Orders\Listeners;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Ecommerce\Cart\Events\CartWasProcessed;
use AwardForce\Modules\Ecommerce\Cart\Services\LocksPayment;
use AwardForce\Modules\Ecommerce\Orders\Commands\CreateOrderCommand;
use AwardForce\Modules\Ecommerce\Orders\Commands\CreateTransactionCommand;
use AwardForce\Modules\Payments\Events\PaymentStatus;
use AwardForce\Modules\Payments\Events\PaymentWasSuccessful;
use AwardForce\Modules\Payments\Events\PaymentWasUnsuccessful;
use Illuminate\Foundation\Bus\DispatchesJobs;

class Payments
{
    use DispatchesJobs;

    public function whenCartWasProcessed(CartWasProcessed $event): void
    {
        if ($event->cart->free() || $event->cart->manualConversion()) {
            $this->dispatch(new CreateOrderCommand($event->cart, '', $event->cart->total()->value(), $event->cart->currency(), $this->season()));
        }
    }

    public function whenPaymentWasSuccessful(PaymentWasSuccessful $event): void
    {
        if (! $event->cart()->free()) {
            $this->dispatch(new CreateOrderCommand($event->cart(), $event->response()->transactionRef(), $event->response()->amount(), $event->response()->currency(), $this->season()));
        }
    }

    public function whenPaymentWasUnsuccessful(PaymentWasUnsuccessful $event): void
    {
        $this->dispatch(new CreateTransactionCommand($event->response(), $event->cart(), $this->season()));
    }

    private function season(): int
    {
        return CurrentAccount::activeSeasonId();
    }

    public function whenPaymentProcessFinished(PaymentStatus $event): void
    {
        LocksPayment::create(current_account_id(), consumer_id())->releaseLock();
    }
}
