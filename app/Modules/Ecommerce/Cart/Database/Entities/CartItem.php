<?php

namespace AwardForce\Modules\Ecommerce\Cart\Database\Entities;

use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Ecommerce\Cart\Costing\EntryAmount;
use AwardForce\Modules\Ecommerce\Cart\Costing\NullEntryPrice;
use AwardForce\Modules\Ecommerce\Cart\EntrantItem;
use AwardForce\Modules\Ecommerce\Cart\EntryItem;
use AwardForce\Modules\Ecommerce\Cart\Item;
use AwardForce\Modules\Ecommerce\Cart\TagItem;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Payments\Exceptions\InvalidPrice;
use AwardForce\Modules\Payments\Models\Price;

class CartItem extends Model
{
    protected $fillable = [
        'price_id',
        'entry_id',
        'amount',
        'discount',
        'quantity',
        'submit',
    ];
    protected $casts = [
        'price_id' => 'integer',
        'entry_id' => 'integer',
        'amount' => 'integer',
        'discount' => 'integer',
        'quantity' => 'integer',
        'submit' => 'boolean',
    ];
    protected $with = ['entry', 'price'];

    public function price()
    {
        return $this->belongsTo(Price::class, 'price_id');
    }

    public function entry()
    {
        return $this->belongsTo(Entry::class, 'entry_id');
    }

    public static function fromSimpleItem(Item $item): CartItem
    {
        return new CartItem([
            'price_id' => $item->price()->id,
            'entry_id' => $item->entryId(),
            'amount' => $item->amount()->valueToCents(),
            'discount' => $item->discount()->valueToCents(),
            'quantity' => $item->quantity(),
            'submit' => $item->submit(),
        ]);
    }

    public function toSimpleItem(Currency $currency): Item
    {
        $price = translate($this->price ?: new NullEntryPrice);

        if ($price->isEntryFee()) {
            $item = new EntryItem(new EntryAmount($this->entry, $currency), $price->getTitle(), $price, $this->submit);
        } elseif ($price->isTagFee()) {
            $item = new TagItem($price->title, $this->entry, $price, new Amount(0.00, $currency));
        } elseif ($price->isEntrantFee()) {
            $item = new EntrantItem($price->title, $price, new Amount(0.00, $currency));
        }

        if (is_null($item)) {
            throw new InvalidPrice("Unknown price type: {$price->type}");
        }

        $item->setAmount(Amount::fromCents($this->amount, $currency));
        $item->setDiscount(Amount::fromCents($this->discount, $currency));
        $item->setName($price->getTitle());

        return $item;
    }
}
