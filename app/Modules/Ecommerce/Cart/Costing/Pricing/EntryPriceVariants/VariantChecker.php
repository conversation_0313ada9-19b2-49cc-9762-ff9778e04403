<?php

namespace AwardForce\Modules\Ecommerce\Cart\Costing\Pricing\EntryPriceVariants;

use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Payments\Models\PriceAmount;
use Illuminate\Support\Collection;

class VariantChecker
{
    /**
     * Stores the variants in the order they should be executed.
     */
    private array $variants;

    private bool $hasFieldVariant = false;
    private float $sumFieldVariant = 0;
    private ?Variant $lastVariant = null;

    public function __construct()
    {
        $this->variants = [
            app(FieldVariant::class),
            new ChapterCategoryVariant,
            new ChapterVariant,
            new CategoryVariant,
            new DefaultVariant,
        ];
    }

    private function check(PriceAmount $amount, Entry $entry, Currency $currency): ?float
    {
        foreach ($this->variants as $variant) {
            if ($variant->appliesTo($amount, $entry)) {
                $price = $amount->amountForCurrency($currency);
                $this->processVariant($variant, $price);

                return $price;
            }
        }

        return null;
    }

    public function getPrice(Collection $amounts, Entry $entry, Currency $currency): ?float
    {
        $variantPrice = null;

        foreach ($amounts as $amount) {
            $variantPrice = $this->check($amount, $entry, $currency);

            if (! is_null($price = $this->variantPrice($variantPrice))) {
                $variantPrice = $price;
                break;
            }
        }

        $this->resetVariantChecker();

        return $variantPrice;
    }

    private function variantPrice(?float $price): ?float
    {
        if (! $this->hasFieldVariant && ! is_null($price)) {
            return $price;
        }

        if ($this->hasFieldVariant && ! ($this->lastVariant instanceof FieldVariant)) {
            return $this->sumFieldVariant;
        }

        return null;
    }

    private function processVariant(Variant $variant, $variantPrice): void
    {
        $this->lastVariant = $variant;
        if (! is_null($variantPrice) && $variant instanceof FieldVariant) {
            $this->hasFieldVariant = true;
            $this->sumFieldVariant += $variantPrice;
        }
    }

    private function resetVariantChecker(): void
    {
        $this->hasFieldVariant = false;
        $this->sumFieldVariant = 0;
        $this->lastVariant = null;
    }
}
