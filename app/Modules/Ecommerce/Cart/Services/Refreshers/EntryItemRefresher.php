<?php

namespace AwardForce\Modules\Ecommerce\Cart\Services\Refreshers;

use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Costing\DefaultCostingStrategy;
use AwardForce\Modules\Ecommerce\Cart\Costing\EntryAmount;
use AwardForce\Modules\Ecommerce\Cart\Costing\EntryCost;
use AwardForce\Modules\Ecommerce\Cart\EntryItem;
use AwardForce\Modules\Ecommerce\Cart\Item;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Payments\Services\Prices;

class EntryItemRefresher implements ItemRefresher
{
    /** @var Prices */
    private $prices;

    /** @var DefaultCostingStrategy */
    private $costingStrategy;

    public function __construct(Prices $prices, DefaultCostingStrategy $costingStrategy)
    {
        $this->prices = $prices;
        $this->costingStrategy = $costingStrategy;
    }

    public function canRefresh(Item $item): bool
    {
        return $item instanceof EntryItem;
    }

    public function shouldRemove(Item $item, Cart $cart): bool
    {
        /** @var Entry $entry */
        $entry = $item && $item->entry() ? $item->entry()->refresh() : null;

        if (! $entry) {
            return true;
        }

        return $entry->trashed() || $entry->paymentComplete();
    }

    public function refresh(Item $item, Cart $cart): void
    {
        $entry = $item->entry();

        $price = $this->prices->getPriceForEntryFee($cart, $entry->seasonId);
        $itemName = $price->getTitle();

        $memberNumber = $cart->get('memberNumber', '');
        if ($memberNumber !== '') {
            $itemName .= trans('payments.prices.member_number', ['number' => $memberNumber]);
        }

        $entryAmount = EntryCost::calculate($cart, $this->costingStrategy, new EntryAmount($entry, $cart->currency()));

        $item->setAmount($entryAmount->amount());
        $item->setDiscount($entryAmount->discount());
        $item->setName($itemName);
        $item->setPrice($price);
    }
}
