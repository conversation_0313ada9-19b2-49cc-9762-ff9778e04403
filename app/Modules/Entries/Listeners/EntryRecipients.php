<?php

namespace AwardForce\Modules\Entries\Listeners;

use AwardForce\Library\Mail\Values\Recipient;
use AwardForce\Library\Mail\Values\Recipients;
use AwardForce\Modules\Broadcasts\Contracts\BroadcastRecipients;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Traits\IncludesCollaborators;
use AwardForce\Modules\Funding\Services\FundAllocationsTable;
use AwardForce\Modules\Identity\Users\Models\User;

class EntryRecipients extends BroadcastRecipients
{
    use FundAllocationsTable;
    use IncludesCollaborators;

    /**
     * The search area that should be used to find recipients.
     */
    protected function area(): string
    {
        return 'manage_entries.search';
    }

    /**
     * The broadcast types supported by this listener.
     */
    protected function allowedTypes(): array
    {
        return ['entrants-with-entries'];
    }

    /**
     * Extracts one or more recipients from an object returned by the used search class and appends it to the given
     * recipients collection.
     *
     * @param  Entry  $entry
     */
    protected function appendRecipients(Recipients $recipients, $entry): Recipients
    {
        if (is_null($entry->entrant->currentMembership) || $entry->entrant->currentMembership->blocked) {
            return $recipients;
        }

        $this->appendCollaborators($entry, $recipients, fn($entry, User $user) => $this->fromEntry($entry, $user));

        return $recipients->push($this->fromEntry($entry, $entry->entrant));
    }

    protected function fromEntry(Entry $entry, $user, $data = []): ?Recipient
    {
        try {
            /** @var \AwardForce\Modules\Categories\Models\Category $category */
            $category = translate($entry->category);
            $parentCategory = $category?->hasParentCategory() ? translate($category->parent) : null;
            $chapter = translate($entry->chapter);

            return Recipient::fromUser(
                $user,
                array_merge([
                    'user_id' => $user->id,
                    'entry_name' => $entry->title ?: '',
                    'application_name' => $entry->title ?: '',
                    'entry_slug' => (string) $entry->slug,
                    'application_slug' => (string) $entry->slug,
                    'entry_local_id' => $entry->localId,
                    'application_local_id' => $entry->localId,
                    'parent_category' => $parentCategory?->name ?: '',
                    'chapter' => $chapter?->name ?: '',
                    'category' => $category?->name ?: '',
                    'fund_allocations' => $this->fundAllocationsMarkdownTable($entry->allocations),
                ], $data)
            );
        } catch (\Exception $ex) {
            \Log::warning("Unable to create recipient from entry: {$ex->getMessage()}", ['entry_slug' => (string) $entry->slug]);

            return null;
        }
    }
}
