<?php

namespace AwardForce\Modules\Entries\Search\Filters;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Arr;
use Platform\Search\ApiVisibility;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilter;
use Platform\Search\Filters\SearchFilterValidation;
use Platform\Search\Services\SearchFilterValidator;
use Platform\Search\Services\SlugToIdMapper;

class EntrantFilter implements ColumnatorFilter, Htmlable, SearchFilter, SearchFilterValidation
{
    use SearchFilterValidator;
    use SlugToIdMapper;

    /** @var array */
    private $input;

    /** @var UserRepository */
    private $users;

    public function __construct(array $input, ?UserRepository $users = null)
    {
        $this->input = $input;
        $this->users = $users ?? app(UserRepository::class);
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return Query
     */
    public function applyToEloquent($query)
    {
        $entrant = $this->entrantFromInput();

        $table = $query->getModel()->getTable();

        $query->where($table.'.account_id', current_account_id())
            ->where($table.'.user_id', $this->id($entrant));

        return $query;
    }

    /**
     * Determines if this dependency applies to the search in any way.
     */
    public function applies(): bool
    {
        return (bool) $this->entrantFromInput();
    }

    /**
     * Get content as a string of HTML.
     *
     * @return string
     */
    public function toHtml()
    {
        $entrantId = $this->id(Arr::get($this->input, 'entrant', 0));
        $entrant = $entrantId ? $this->users->getInAccount($entrantId, true) : null;

        return view('entry.manager.search.filters.entrant', [
            'slug' => $entrant ? (string) $entrant->slug : null,
            'fullName' => $entrant ? $entrant->fullName() : null,
        ])->render();
    }

    public function validateFilterValue(string $filterName, string $filterValue)
    {
        $this->validateValueIsSlug($filterName, $filterValue);
    }

    public function validateFilterName(string $filterName): bool
    {
        return in_array($filterName, [Vertical::replace('entrant')]);
    }

    public function repository()
    {
        return $this->users;
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }

    private function entrantFromInput(): ?string
    {
        return Arr::get(
            $this->input,
            'entrant',
            Arr::get($this->input, Vertical::replace('entrant'))
        );
    }
}
