<?php

namespace AwardForce\Modules\Entries\Search\Filters;

use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use Eloquence\Behaviours\Slug;
use Illuminate\Database\Eloquent\Builder;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class EntrantFilterTest extends BaseTestCase
{
    use Database;
    use Laravel;

    #[TestWith(['awards', 'entrant', 'applicant'])]
    #[TestWith(['grants', 'applicant', 'entrant'])]
    public function testValidatesFilterNameInApiForVertical(string $vertical, string $verticalTerm, string $otherVerticalTerm): void
    {
        $filter = new EntrantFilter([]);
        current_account()->vertical = $vertical;

        $this->assertTrue($filter->validateFilterName($verticalTerm));
        $this->assertFalse($filter->validateFilterName($otherVerticalTerm));
    }

    #[TestWith(['awards', 'entrant'])]
    #[TestWith(['grants', 'applicant'])]
    public function testAppliesForVerticalTerm(string $vertical, string $verticalTerm): void
    {
        current_account()->vertical = $vertical;

        $filter = new EntrantFilter([$verticalTerm => 'test']);
        $this->assertTrue($filter->applies());
    }

    #[TestWith(['awards'])]
    #[TestWith(['grants'])]
    #[TestWith(['irrelevant'])]
    public function testEntrantTermAppliesRegardlessOfVertical(string $vertical): void
    {
        current_account()->vertical = $vertical;
        $filter = new EntrantFilter(['entrant' => 'test']);
        $this->assertTrue($filter->applies());
    }

    #[TestWith(['awards', 'entrant'])]
    #[TestWith(['grants', 'applicant'])]
    #[TestWith(['grants', 'entrant'])]
    public function testApplyToEloquentResolvesValueFromInput(string $vertical, string $term): void
    {
        current_account()->vertical = $vertical;
        $filter = new EntrantFilter([$term => 1]);

        $query = m::mock(Builder::class);
        $query->shouldReceive('getModel->getTable')->andReturn('entries');
        $query->shouldReceive('where')->with('entries.account_id', current_account_id())->andReturnSelf();
        $query->shouldReceive('where')->with('entries.user_id', 1)->andReturnSelf();

        $filter->applyToEloquent($query);
    }

    public function testItDoesNotQueryUsersWhenNotFiltering(): void
    {
        $users = m::mock(UserRepository::class);
        $filter = new EntrantFilter([], $users);

        $users->shouldNotReceive('getBySlug');
        $users->shouldNotReceive('getInAccount');

        $filter->toHtml();
    }

    public function testItShouldQueryWhenFiltering(): void
    {
        $users = m::mock(UserRepository::class);

        $user = new User;
        $user->id = 1;
        $user->slug = Slug::fromId(1);
        $user->firstName = 'John';
        $user->lastName = 'Doe';
        $users->shouldReceive('getBySlug')->once()->with((string) $user->slug)->andReturn($user);
        $users->shouldReceive('getInAccount')->once()->with(1, true)->andReturn($user);

        $filter = new EntrantFilter(['entrant' => (string) $user->slug], $users);
        $filter->toHtml();
    }
}
