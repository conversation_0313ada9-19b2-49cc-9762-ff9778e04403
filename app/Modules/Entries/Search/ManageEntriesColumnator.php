<?php

namespace AwardForce\Modules\Entries\Search;

use AwardForce\Library\Search\Columns\EligibilityStatusChecker;
use AwardForce\Library\Search\Columns\Form;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Columns\Star;
use AwardForce\Library\Search\Filters\EntryFormFilter;
use AwardForce\Library\Search\Filters\FormJoinFilter;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Library\Search\Filters\TagSearchFilter;
use AwardForce\Library\Search\SeasonalColumnator;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\Columns\ActionOverflow;
use AwardForce\Modules\Entries\Search\Columns\Category;
use AwardForce\Modules\Entries\Search\Columns\CategoryShortcode;
use AwardForce\Modules\Entries\Search\Columns\CategorySlug;
use AwardForce\Modules\Entries\Search\Columns\Chapter;
use AwardForce\Modules\Entries\Search\Columns\ChapterSlug;
use AwardForce\Modules\Entries\Search\Columns\Comments;
use AwardForce\Modules\Entries\Search\Columns\ContributorCount;
use AwardForce\Modules\Entries\Search\Columns\Deadline;
use AwardForce\Modules\Entries\Search\Columns\Division;
use AwardForce\Modules\Entries\Search\Columns\EligibilityStatus;
use AwardForce\Modules\Entries\Search\Columns\Entrant;
use AwardForce\Modules\Entries\Search\Columns\EntrantEmail;
use AwardForce\Modules\Entries\Search\Columns\EntrantMobile;
use AwardForce\Modules\Entries\Search\Columns\EntrantSlug;
use AwardForce\Modules\Entries\Search\Columns\EntryFundAllocations;
use AwardForce\Modules\Entries\Search\Columns\FilesCount;
use AwardForce\Modules\Entries\Search\Columns\FirstName;
use AwardForce\Modules\Entries\Search\Columns\GrantStatus;
use AwardForce\Modules\Entries\Search\Columns\LastName;
use AwardForce\Modules\Entries\Search\Columns\Links;
use AwardForce\Modules\Entries\Search\Columns\LocalId;
use AwardForce\Modules\Entries\Search\Columns\ModerationStatus;
use AwardForce\Modules\Entries\Search\Columns\ParentCategory;
use AwardForce\Modules\Entries\Search\Columns\PaymentStatus;
use AwardForce\Modules\Entries\Search\Columns\PlagiarismScanStatus;
use AwardForce\Modules\Entries\Search\Columns\RawLocalId;
use AwardForce\Modules\Entries\Search\Columns\ReviewStatus;
use AwardForce\Modules\Entries\Search\Columns\Season;
use AwardForce\Modules\Entries\Search\Columns\Status;
use AwardForce\Modules\Entries\Search\Columns\SubmissionStatus;
use AwardForce\Modules\Entries\Search\Columns\SubmittedAt;
use AwardForce\Modules\Entries\Search\Columns\Tags;
use AwardForce\Modules\Entries\Search\Columns\Thumbnail;
use AwardForce\Modules\Entries\Search\Columns\Title;
use AwardForce\Modules\Entries\Search\Columns\UserComments;
use AwardForce\Modules\Entries\Search\Enhancers\Comments as CommentsEnhancer;
use AwardForce\Modules\Entries\Search\Enhancers\Feedback as FeedbackEnhancer;
use AwardForce\Modules\Entries\Search\Enhancers\FilesCount as FilesCountEnhancer;
use AwardForce\Modules\Entries\Search\Enhancers\Thumbnails;
use AwardForce\Modules\Entries\Search\Filters\AllocationsJoinFilter;
use AwardForce\Modules\Entries\Search\Filters\ApiArchivedFilter;
use AwardForce\Modules\Entries\Search\Filters\CategorySearchFilter;
use AwardForce\Modules\Entries\Search\Filters\ChapterManagerRoleFilter;
use AwardForce\Modules\Entries\Search\Filters\ChapterSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\EligibilityStatusSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\EntrantFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryAttachmentsFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryContributorsFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryKeywordFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryPayment;
use AwardForce\Modules\Entries\Search\Filters\EntryPrice;
use AwardForce\Modules\Entries\Search\Filters\EntrySlugFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryStatusSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryTitleFilter;
use AwardForce\Modules\Entries\Search\Filters\FieldColumnOrderFilter;
use AwardForce\Modules\Entries\Search\Filters\GrantStatusJoinFilter;
use AwardForce\Modules\Entries\Search\Filters\IncludeCollaboratorsFilter;
use AwardForce\Modules\Entries\Search\Filters\ManageEntriesFieldSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\ModerationSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\PlagiarismScanStatusFilter;
use AwardForce\Modules\Entries\Search\Filters\ReviewStatusFilter;
use AwardForce\Modules\Forms\Collaboration\Search\Columns\CollaboratorsCount;
use AwardForce\Modules\Forms\Collaboration\Search\Filters\CollaboratorsCountFilter;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Search\Columns\Attachments;
use AwardForce\Modules\Forms\Fields\Search\Columns\Contributors;
use AwardForce\Modules\Forms\Fields\Search\Columns\EntryFields;
use AwardForce\Modules\Forms\Fields\Search\Columns\Search\FieldAutoScore;
use AwardForce\Modules\Forms\Fields\Search\Columns\Search\TotalScoreColumn;
use AwardForce\Modules\Forms\Fields\Search\Columns\SearchField;
use AwardForce\Modules\Forms\Fields\Search\Enhancers\FieldValuesEnhancer;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Grants\Search\Columns\GrantEndDate;
use AwardForce\Modules\Grants\Search\Filters\GrantStatusFilter;
use AwardForce\Modules\Identity\Users\Search\Enhancers\EntryUserComments as EntryUserCommentsEnhancer;
use AwardForce\Modules\Integrations\Data\IntegrationRepository;
use AwardForce\Modules\Integrations\Plagiarism\PlagiarismDetection;
use AwardForce\Modules\Search\FormRelated;
use AwardForce\Modules\Seasons\Search\Columns\LinkedSeason;
use AwardForce\Modules\Stars\Search\Filters\StarredSearchFilter;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Archived;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\Dates\DateFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class ManageEntriesColumnator extends Columnator implements ApiColumnator, FormRelated
{
    use ApiColumns;
    use EligibilityStatusChecker;
    use SeasonalColumnator;

    /**
     * Base columns are those that are static and essentially not columns based off any custom
     * field configuration. For example, a user's first and last name are base columns.
     *
     * @return mixed
     */
    protected function baseColumns()
    {
        return new Columns([
            new ReactiveMarker,
            new ActionOverflow('manage_entries.search', $plagiarism = $this->plagiarismDetection()),
            new Thumbnail('entry.manager.view'),
            new Star,
            new RawLocalId,
            new CategoryShortcode,
            new LocalId,
            Slug::forResource('entries'),
            new Title,
            new LinkedSeason(trans('entries.table.columns.season'), 'manage_entries.season'),
            app(Form::class),
            new Status,
            new Entrant,
            new FirstName,
            new LastName,
            new EntrantEmail,
            new EntrantMobile,
            new EntrantSlug,
            new Chapter,
            new ChapterSlug,
            new ParentCategory,
            new Category,
            new Division,
            new CategorySlug,
            new GrantStatus(Arr::has($this->input, 'showGrants')),
            new GrantEndDate(Arr::has($this->input, 'showGrants')),
            Created::forResource('entries', consumer()->dateLocale()),
            Updated::forResource('entries', consumer()->dateLocale()),
            Archived::forResource('entries', consumer()->dateLocale()),
            new SubmittedAt,
            new SubmissionStatus,
            new ReviewStatus,
            new ModerationStatus,
            new PlagiarismScanStatus($plagiarism),
            new PaymentStatus,
            new Tags,
            new FilesCount,
            new ContributorCount,
            new CollaboratorsCount,
            new Comments,
            new Season,
            new Links,
            new EntryFundAllocations,
            new TotalScoreColumn,
            new EligibilityStatus,
            new Deadline,
            app(EntryFields::class)->setFieldColumns($this->fieldColumns())->setRepository($this->repository()),
            app(Attachments::class)->setFieldColumns($this->fieldColumns())->setRepository(app(FieldRepository::class)),
            app(Contributors::class)->setFieldColumns($this->fieldColumns())->setRepository(app(FieldRepository::class)),
            new UserComments,
        ]);
    }

    /** @var Collection */
    private $fieldColumns;

    /**
     * Columns that are defined and configured by custom fields within the Fields module.
     *
     * @return Collection
     */
    protected function fieldColumns()
    {
        return $this->fieldColumns ?: $this->fieldColumns = collect(
            array_merge(
                $this->fields(Field::RESOURCE_FORMS, 'entries.id'),
                $this->userFields('entries.user_id')
            )
        );
    }

    protected function fields(string $resource, string $foreignId): array
    {
        if ($formId = Arr::get($this->input, 'formId', FormSelector::getId())) {
            $fields = app(FieldRepository::class)->requestCache()->getByFormIdAndResourceWhereNot(
                $formId, $resource, ['content']
            );
        } else {
            $fields = app(FieldRepository::class)->requestCache()->getForEntriesByResourceWhereNot(
                $resource,
                ['content'],
                $this->seasonId()
            );
        }

        return [
            ...$this->mapFields($fields, $foreignId),
            ...$fields->filter(fn(Field $field) => $field->autoScoring)->map(fn(Field $field) => new FieldAutoScore($field))->all(),
        ];
    }

    protected function userFields(string $foreignId): array
    {
        return $this->mapFields(
            app(FieldRepository::class)->getUserFieldsByResourceWhereNot(['content'], $this->seasonId()),
            $foreignId
        );
    }

    protected function mapFields(Collection $fields, string $foreignId)
    {
        return $fields->map(function (Field $field) use ($foreignId) {
            return SearchField::fromField($field, $foreignId);
        })->all();
    }

    /**
     * Return the resource that this columnator represents (such as entries, or users).
     *
     * @return string|null
     */
    public function resource()
    {
        return 'EntriesAll';
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $columns = $this->columns($view);
        $dependencies = new Dependencies;

        //Filters
        $dependencies->add((new ColumnFilter(...$columns))->with(
            'entries.id',
            'entries.local_id',
            'entries.slug',
            'entries.title',
            'entries.form_id',
            'entries.user_id',
            'entries.season_id',
            'entries.chapter_id',
            'entries.category_id',
            'entries.resubmission_required_at',
            'entries.resubmitted_at',
            'entries.submitted_at',
            'entries.deleted_at',
            'entries.archived_at',
            'entries.health_status',
            'entries.moderation_status',
            'entries.review_status_action',
            'entries.grant_status_id',
            'entries.eligible_at',
            'entries.ineligible_at',
            'entries.invited_at',
            'entries.deadline_at',
            'entries.deadline_timezone',
            'entries.account_id',
        ));

        $dependencies->add(new IncludeFilter(['allocations', 'attachments', 'contributors', 'entrant.currentMembership', 'category.parent', 'chapter', 'contributors', 'season', 'form', 'tags', 'orders.orderItems.price', 'certificates', 'plagiarismScans', 'links', 'user']));
        $dependencies->add(new IncludeCollaboratorsFilter($this->input));
        $dependencies->add(app(ChapterManagerRoleFilter::class));
        $dependencies->add(new AllocationsJoinFilter);
        $dependencies->add(new ChapterSearchFilter($this->input));
        $dependencies->add(new CategorySearchFilter($this->input, divisions: true));
        $dependencies->when(
            $this->hasGrantAccess(),
            fn($dependency) => $dependency->add(new GrantStatusFilter($this->input))
        );
        $dependencies->add(new EntryStatusSearchFilter($this->input));
        $dependencies->add(new EntryPrice($this->input));
        $dependencies->add(new EntryPayment($this->input));
        $dependencies->add(new EntryFormFilter($this->input));
        $dependencies->add(new FormJoinFilter('entries'));
        $dependencies->add(new ReviewStatusFilter($this->input, $this->baseColumns()->translatedColumns(['manage_entries.status', 'manage_entries.review_status'])->first()));
        $dependencies->add(new ModerationSearchFilter($this->input));
        $dependencies->when($this->showEligibilityStatus(), fn($dependency) => $dependency->add(new EligibilityStatusSearchFilter($this->input)));
        $dependencies->add(new TagSearchFilter($this->input, $this->repository(), [Entry::class, Allocation::class], [Allocation::class => 'entry_id']));
        $dependencies->add(new PlagiarismScanStatusFilter($this->input, $this->plagiarismDetection()));
        $dependencies->add(new EntryContributorsFilter);
        $dependencies->add(new CollaboratorsCountFilter);
        $dependencies->add(new EntryAttachmentsFilter);
        $dependencies->add(new GrantStatusJoinFilter($this->input));
        $dependencies->add(new GroupingFilter('entries.local_id'));
        $dependencies->add(new ApiArchivedFilter($this->input));
        $dependencies->add(new EntryTitleFilter($this->input));
        $dependencies->add(new EntrantFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $this->applySeasonalFilter($dependencies);
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(FieldColumnOrderFilter::fromColumns($columns, $this->input, 'entries.updated')->uniqueColumn('entries.id'));
        $dependencies->add(new EntryKeywordFilter($this->input['keywords'] ?? '', ['entries.title', 'entries.slug', 'entries.local_id', 'users.email', 'users.first_name', 'users.last_name']));
        $dependencies->add(new EntrySlugFilter($this->input));

        $dependencies->add(new StarredSearchFilter($this->input, new Entry));

        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns(['manage_entries.category', 'manage_entries.parent_category', 'manage_entries.category_shortcode', 'manage_entries.local_id']), 'Category', current_account_id()))->setJoinTable('categories')->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('manage_entries.chapter'), 'Chapter', current_account_id()))->setJoinTable('chapters')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('manage_entries.season'), 'Season', current_account_id()))->setJoinTable('seasons')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($this->baseColumns()->translatedColumns(['manage_entries.status', 'manage_entries.review_status']), 'Entry', current_account_id()))->setJoinTable('entries')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('form'), 'Form', current_account_id()))->setJoinTable('entries')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('grant_status'), 'GrantStatus', current_account_id()))->setJoinTable('grant_statuses')->restrictLanguage($language));

        $dependencies->add(new DateFilter($this->input, 'created_at', 'created_at'));
        $dependencies->add(new DateFilter($this->input, 'updated_at', 'updated_at'));
        $dependencies->add(new DateFilter($this->input, 'submitted_at', 'submitted_at'));

        //Enhancers
        $dependencies->add(app(Thumbnails::class));
        $dependencies->add(app(FeedbackEnhancer::class));
        $dependencies->add(app(CommentsEnhancer::class));
        $dependencies->add(app(FilesCountEnhancer::class));
        $dependencies->add(app(EntryUserCommentsEnhancer::class));

        $dependencies->add(ManageEntriesFieldSearchFilter::fromInput($this->input));
        $dependencies->add(FieldValuesEnhancer::fromColumns($columns));

        return $dependencies;
    }

    protected function applySeasonalFilter($dependencies)
    {
        $dependencies->add(new SeasonalFilter(Arr::get($this->input, 'season'), 'entries.season_id', ! is_null(Arr::get($this->input, 'slug'))));
    }

    /**
     * Each columnator must have a unique key that can be used to identify it when loading from settings.etc.
     */
    public static function key(): string
    {
        return 'manage_entries.search';
    }

    /**
     * All columnators have an export view as well. The export key represents a value that can be used to search
     * for any saved exports for this particular columnator.
     */
    public static function exportKey(): string
    {
        return 'manage_entries.export';
    }

    /**
     * Return the repository to be used for future queries.
     */
    public function repository(): Repository
    {
        return app(EntryRepository::class);
    }

    /** @var PlagiarismDetection */
    protected static $plagiarismDetection = [];

    /**
     * @return PlagiarismDetection
     */
    protected function plagiarismDetection()
    {
        $id = current_account_id();
        if (array_key_exists($id, self::$plagiarismDetection)) {
            return self::$plagiarismDetection[$id];
        }

        $seasonId = $this->seasonId();

        return self::$plagiarismDetection[$id] = ! is_null($seasonId) ? app(IntegrationRepository::class)->activePlagiarismDetection($seasonId) : null;
    }

    /**
     * Check if the consumer has grant access
     */
    private function hasGrantAccess(): bool
    {
        if (
            (($this->input['showGrants'] ?? null) == 1 || isset($this->input['grant_status'])) &&
            feature_enabled('grants')) {
            if (is_api_consumer()) {
                return true;
            }

            if (! is_api_consumer() && \Consumer::can('view', 'Grants')) {
                return true;
            }
        }

        return false;
    }
}
