<?php

namespace AwardForce\Modules\Entries\Services\AIAgent;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\AIAgents\Boundary\PromptContext;
use AwardForce\Modules\AIAgents\Exceptions\MissingParameterException;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Tags\Models\Tag;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class EntryContextTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testHandleSuccessfullyFillsPromptContextWithEntryData(): void
    {
        $category = $this->muffin(Category::class, ['parent_id' => $this->muffin(Category::class)->id]);
        $tags = collect($this->muffins(2, Tag::class));
        $entry = $this->muffin(Entry::class, ['category_id' => $category->id, 'grant_status_id' => $this->muffin(GrantStatus::class)->id]);
        $entry->tags()->attach($tags->pluck('id'));
        $tabs = collect()
            ->push($this->muffin(Tab::class, ['form_id' => $entry->formId, 'order' => 10]))
            ->push($this->muffin(Tab::class, ['form_id' => $entry->formId, 'order' => 20]))
            ->push($this->muffin(Tab::class, ['form_id' => $entry->formId, 'order' => 30]))
            ->each(function (Tab $tab, $tabIndex) use ($entry) {
                $fields = collect(range(1, random_int(2, 4)))
                    ->map(fn($i) => $this->muffin(Field::class, [
                        'form_id' => $entry->formId,
                        'tab_id' => $tab->id,
                        'order' => $i * 10,
                    ]));

                $this->muffin(Field::class, ['form_id' => $entry->formId, 'tab_id' => $tab->id, 'protection' => Field::PROTECTION_ELEVATED, 'order' => 1000]);
                $this->muffin(Field::class, ['form_id' => $entry->formId, 'tab_id' => $tab->id, 'protection' => Field::PROTECTION_MAXIMUM, 'order' => 1010]);

                app(ValuesService::class)->syncValuesForObject(
                    $fields->mapWithKeys(fn(Field $field) => [(string) $field->slug => str_random()])->all(),
                    $entry
                );
            });

        $entryContext = app(EntryContext::class);
        $result = $entryContext->handle(new PromptContext(), ['entry_id' => $entry->id]);

        $entry = translate($entry->fresh([
            'form',
            'category.parent',
            'chapter',
            'grantStatus',
            'form.tabs' => fn(HasMany $query) => $query->with(['fields' => fn(HasMany $query) => $query->orderBy('order')])->orderBy('order'),
        ]));

        $entryData = $result->get(Vertical::replace('entry'));

        $this->assertIsObject($entryData);
        $this->assertSame($entry->form->name, $entryData->form);
        $this->assertSame($entry->title, $entryData->title);
        $this->assertSame($entry->category->name, $entryData->category);
        $this->assertSame($entry->category->parent->name, $entryData->parent_category);
        $this->assertSame($entry->chapter->name, $entryData->chapter);
        $this->assertSame($entry->submissionStatus(), $entryData->status);
        $this->assertSame($entry->grantStatus->name, $entryData->grant_status);
        $this->assertSame($entry->tags->just('tag'), $entryData->tags);
        $this->assertCount($tabs->count(), $entryData->tabs);

        collect($entry->form->tabs)
            ->zip($entryData->tabs)
            ->each(function ($pair, $index) use ($entry) {
                [$tab, $tabData] = $pair;

                $this->assertSame($tab->name, $tabData->name);
                $this->assertSame($tab->type, $tabData->type);
                $this->assertIsArray($tabData->fields);

                $mappedFields = app(ValuesService::class)->mapValuesToFields($entry, $tab->fields);
                $this->assertCount(count($mappedFields) - 2, $tabData->fields); // protected fields are not included

                collect($tabData->fields)
                    ->each(function ($fieldData, $fieldIndex) use ($mappedFields) {
                        $field = $mappedFields[$fieldIndex];

                        $this->assertSame($field->label, $fieldData->label);
                        $this->assertSame($field->type, $fieldData->type);
                        $this->assertNotNull($fieldData->value);
                        $this->assertSame(['label', 'value', 'type'], array_keys((array) $fieldData));
                    });
            });
    }

    public function testHandleThrowsExceptionWhenEntryIdIsMissing(): void
    {
        $entryContext = app(EntryContext::class);

        $this->expectException(MissingParameterException::class);

        $entryContext->handle(new PromptContext(), []);
    }
}
