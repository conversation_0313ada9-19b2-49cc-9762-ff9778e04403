<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Library\Database\EventSourcing\MysqlAggregateRepository;
use AwardForce\Modules\Forms\Formables\Boundary\FormableAggregate;
use AwardForce\Modules\Forms\Formables\Boundary\FormableAggregateRepository;
use AwardForce\Modules\Forms\Formables\Boundary\HandlesFormables;
use AwardForce\Modules\Forms\Forms\Enums\Resource;

class EntryFormableProvider implements HandlesFormables
{
    public function repository(): FormableAggregateRepository
    {
        return new class extends MysqlAggregateRepository implements FormableAggregateRepository
        {
            public function new(): FormableAggregate
            {
                return new EntryAggregate;
            }
        };
    }

    public function handles(Resource $resource): bool
    {
        return $resource === Resource::Entry;
    }
}
