<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Http\Controllers\EntryForm\SubmittableFormResources;
use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\String\StripSpecialCharacters;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Chapters\Services\UploadValidator as ChapterUploadValidator;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Models\Link;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Files\Services\Uploader;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\AutoScoringService;
use AwardForce\Modules\Forms\Fields\Services\UploadValidator;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Services\FormFileMapper;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Referees\Contracts\RefereeRepository;
use AwardForce\Modules\Referees\Models\Referee;
use AwardForce\Modules\Rounds\Services\Rounds;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Models\Season;
use Facades\Platform\Strings\Output;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Facades\Translator;

abstract class SubmittableFormService
{
    use SubmittableFormResources;
    use Uploadable;

    public function __construct(
        protected AttachmentsUploader $attachmentsUploader,
        protected FieldRepository $fields,
        protected TabRepository $tabs,
        protected SeasonRepository $seasons,
        protected FileRepository $files,
        protected FormRepository $forms,
        protected FormFileMapper $formFileMapper,
        protected UploadValidator $validator,
        protected ValuesService $values,
        protected EntryCategoriesListGenerator $generator,
        protected ChapterRepository $chapters,
        protected ChapterUploadValidator $chapterUploadValidator,
        protected Rounds $rounds,
        protected AutoScoringService $autoScoring,
        protected EntryRepository $entries,
        protected RefereeRepository $referees
    ) {
    }

    /**
     * Get Fields for this Entry Form
     *
     * @return array
     */
    public function getFields(Tab $tab, ?Submittable $submittable = null)
    {
        $fields = $this->fields->forTab($tab);
        $fields = $this->values->mapValuesToFields($submittable, $this->populateConditional($fields))->unique('id');

        if ($tab->type !== Tab::TYPE_ELIGIBILITY) {
            $resourceFields = $this->fields->getAllByFormAndResource($tab->form ?? $this->getForm($submittable), [
                Field::RESOURCE_CONTRIBUTORS,
                Field::RESOURCE_ATTACHMENTS,
                Field::RESOURCE_REFEREES,
            ]);
            $fields = $resourceFields->merge($fields);
        }

        return Translator::shallow(
            $this->filterFields($fields)->sortBy('order')
        )->map(function ($field) use ($submittable) {
            return $this->mapField($field, $submittable);
        })->values();
    }

    public function getFieldsForConditionalSelector(?Submittable $submittable = null)
    {
        $form = $this->getForm($submittable);

        return Translator::shallow($this->fields->getAllByFormAndResource($form, [Field::RESOURCE_FORMS, Field::RESOURCE_ATTACHMENTS, Field::RESOURCE_CONTRIBUTORS]))
            ->map(function ($field) {
                return [
                    'id' => $field->id,
                    'title' => $field->title,
                    'type' => $field->type,
                    'resource' => $field->resource,
                ];
            })
            ->sortBy('title', SORT_NATURAL | SORT_FLAG_CASE)
            ->values();
    }

    /**
     * Get Tabs for this Entry Form
     *
     * @param  ?Submittable  $entry
     * @return array
     */
    public function getTabs(?Submittable $submittable = null)
    {
        $tabs = Translator::shallow($this->tabs->getFromForm($this->getForm($submittable)));

        return $tabs->map(function ($tab) use ($submittable) {
            return $this->mapTab($tab, $submittable);
        })->sortBy('order')->values();
    }

    /**
     * Filter out unnecessary fields
     */
    abstract protected function filterFields(Collection $fields);

    abstract protected function getSeason(?Submittable $submittable = null): Season;

    abstract protected function tabEligibilityScore(Tab $tab, ?Submittable $submittable): float|int;

    protected function chapters(Form $form): Collection
    {
        return $this->chapters->getAllWithFormFlag($form);
    }

    /**
     * Sets up the field array with the necessary key / value pairs
     *
     * @param  Submittable  $entry
     * @return array
     */
    public function mapField(Field $field, ?Submittable $submittable = null)
    {
        // Set key / value pairs from relevant columns in fields table
        $fieldArray = [
            'categories' => $field->categories->pluck('id')->toArray(),
            'categoryOption' => $field->categoryOption,
            'conditional' => $field->conditional,
            'conditionalFieldId' => $field->conditionalFieldId,
            'conditionalFieldSlug' => $field->conditionalField ? (string) $field->conditionalField->slug : '',
            'conditionalPattern' => $field->conditionalPattern,
            'conditionalValue' => $field->conditionalValue,
            'conditionalVisibility' => $field->conditionalVisibility,
            'configuration' => $field->configuration,
            'createdAt' => $field->createdAt ? (string) $field->createdAt : null,
            'currency' => $field->currency,
            'entrantReadAccess' => $field->entrantReadAccess,
            'entrantWriteAccess' => $field->entrantWriteAccess,
            'fileTypes' => $field->fileTypes ?? [],
            'minVideoLength' => $field->minVideoLength,
            'maxVideoLength' => $field->maxVideoLength,
            'imageDimensionConstraints' => (object) $field->imageDimensionConstraints->toArray(),
            'formId' => $field->formId,
            'hasValues' => $field->hasValues,
            'helpText' => $field->helpText,
            'hintText' => $field->hasHintText() ? Output::html($field->hintText) : null,
            'id' => $field->id,
            'includeTimezone' => $field->includeTimezone,
            'isRecalculating' => $field->isRecalculating,
            'label' => ($label = markdown_inline($field->label)),
            'labelMarkdown' => Output::html($field->label),
            'labelStripped' => strip_tags($label),
            'maximumCharacters' => $field->maximumCharacters,
            'maximumWords' => $field->maximumWords,
            'minimumCharacters' => $field->minimumCharacters,
            'minimumWords' => $field->minimumWords,
            'order' => $field->order,
            'plagiarismDetection' => $field->plagiarismDetection,
            'preselectCurrentDate' => $field->preselectCurrentDate,
            'protection' => $field->protection,
            'required' => $field->required,
            'resource' => $field->resource,
            'searchable' => $field->searchable,
            'autoScoring' => $field->autoScoring,
            'autoTag' => $field->autoTag,
            'slug' => (string) $field->slug,
            'tabId' => $field->tabId,
            'title' => $field->title,
            'translated' => StripSpecialCharacters::strip($field->translated),
            'type' => $field->type,
            'value' => $field->value,
        ];

        $this->mapSpecificFields($field, $submittable, $fieldArray);

        return $fieldArray;
    }

    /**
     * Sets up the tab array with the necessary key / value pairs
     *
     * @return array
     */
    public function mapTab(Tab $tab, ?Submittable $submittable = null)
    {
        $minEligibilityScore = floatval($tab->getSetting('min-eligibility-score', 0.00));
        $tabEligibilityScore = $this->tabEligibilityScore($tab, $submittable);

        // Set key / value pairs from relevant columns in tabs table
        return [
            'acceptAttachmentLinks' => (bool) $tab->getSetting('accept-attachment-links'),
            'attachmentAllowedFileTypes' => $tab->getSetting('attachment-allowed-file-types', []),
            'categories' => $tab->categoryIds(),
            'categoryOption' => $tab->categoryOption,
            'contentblockId' => $tab->contentblockId,
            'createdAt' => $tab->createdAt ? (string) $tab->createdAt : null,
            'formId' => $tab->formId,
            'id' => $tab->id,
            'locked' => (bool) $tab->locked,
            'maxAttachments' => (int) $tab->getSetting('max-attachments') ?: null,
            'maxContributors' => $tab->maxContributors,
            'maxFilesize' => (int) $tab->getSetting('max-filesize') ?: null,
            'minVideoLength' => (int) $tab->getSetting('min-video-length') ?: null,
            'maxVideoLength' => (int) $tab->getSetting('max-video-length') ?: null,
            'imageDimensionConstraints' => (object) $tab->imageDimensionConstraints->toArray(),
            'minAttachments' => (int) $tab->getSetting('min-attachments') ?: null,
            'minContributors' => $tab->minContributors,
            'minFilesize' => (int) $tab->getSetting('min-filesize') ?: null,
            'name' => $tab->name,
            'order' => $tab->order,
            'resource' => $tab->resource,
            'slug' => (string) $tab->slug,
            'tabDividerOnPdfs' => $tab->tabDividerOnPdfs,
            'translated' => $tab->translated,
            'type' => (string) $tab->type,
            'uploaderOptions' => $submittable ? obfuscate($this->uploaderOptionsForTab($tab, $submittable))->toHtml() : null,
            'visibleToEntrants' => $tab->visibleToEntrants,
            'ineligibleHideTabs' => (int) $tab->getSetting('ineligible-hide-tabs') ?: null,
            'eligibleContentBlock' => $tab->getSetting('eligible-content-block'),
            'ineligibleContentBlock' => $tab->getSetting('ineligible-content-block'),
            'eligibleNotification' => $tab->getSetting('eligible-notification'),
            'ineligibleNotification' => $tab->getSetting('ineligible-notification'),
            'minEligibilityScore' => $minEligibilityScore,
            'eligibilityScore' => $tabEligibilityScore,
            'eligible' => (bool) $submittable?->tabIsEligible($tab),
            'calculatedScore' => $tabEligibilityScore,
            'reviewStage' => $tab->getSetting('review-stage'),
            'minReferees' => $tab->getSetting('min-referees'),
            'maxReferees' => $tab->getSetting('max-referees'),
        ];
    }

    public static function mapContentBlock(ContentBlock $contentBlock): array
    {
        $contentBlock = Translator::shallow($contentBlock);

        return [
            'id' => $contentBlock->id,
            'slug' => (string) $contentBlock->slug,
            'key' => $contentBlock->key,
            'visibility' => $contentBlock->visibility,
            'title' => $contentBlock->title(),
            'content' => Output::html($contentBlock->content()),
        ];
    }

    private function mapSpecificFields(Field $field, ?Submittable $submittable, array &$fieldArray): void
    {
        // Set Checkbox list and Dropdown list specific key / value pairs
        if ($field->optionable() || $field->type === 'checkbox') {
            if (in_array($field->type, ['checkboxlist', 'radio'])) {
                $fieldArray['value'] = empty_string($field->value) ? [] : $field->value;
            }
            $fieldArray['options'] = StripSpecialCharacters::strip($this->fieldOptions($field));
        }

        // Drop-down list
        if ($field->type === 'drop-down-list') {
            $fieldArray['autocomplete'] = $field->autocomplete;
        }

        // Set Table specific key / value pairs
        if ($field->type === 'table') {
            $this->mapTable($field, $fieldArray);
        }

        // Set File specific key / value pairs
        if ($field->type === 'file') {
            $uploader = $this->uploader($field, $submittable ?? null);
            $fieldArray['uploaderOptions'] = obfuscate($uploader->options())->toHtml();

            $file = $this->getFileForFileField($field, $submittable);
            $fieldArray['file'] = $file ? json_encode($file) : null;

            $fieldArray['maxFileSize'] = $field->maxFileSize;
        }

        // Set URL specific key / value pairs
        if ($field->value && $field->type === 'url') {
            $this->mapUrl($field, $fieldArray);
        }

        // Set Textarea specific key / value pairs
        if ($field->type === 'textarea') {
            $fieldArray['enableMarkdown'] = $field->form?->settings->enableEditorInMultilineTextFields ?? false;
        }
    }

    /**
     * Map key / value pairs for table field
     */
    private function mapTable(Field $field, array &$fieldArray)
    {
        $configuration = translate($field->getConfiguration());

        $fieldArray['configurationTranslated'] = $configuration->translated() ? json_encode($configuration->translated()) : null;
        $fieldArray['language'] = Consumer::languageCode();
        $fieldArray['defaultLanguage'] = CurrentAccount::defaultLanguage()->code;
        $fieldArray['labels'] = json_encode(['addRow' => trans('fields.table_field_configurator.add_row')]);
        $fieldArray['value'] = $fieldArray['value'] ? json_encode($fieldArray['value']) : null;
        $fieldArray['hasEntries'] = $this->entries->existsForField($field);
    }

    private function uploader(Field $field, ?Submittable $submittable): Uploader
    {
        return $this->validator
            ->setField($field)
            ->setupUploader($this->setupUploader()->setForeign($submittable?->id))
            ->setUseFileToken();
    }

    private function getFileForFileField(Field $field, ?Submittable $submittable = null)
    {
        if ($submittable && $file = $this->files->getFromFileField($field)) {
            return $this->formFileMapper->mapFile($file);
        }

        return null;
    }

    /**
     * Map key / value pairs for url field
     */
    private function mapUrl(Field $field, array &$fieldArray)
    {
        $urlTypes = [
            'vimeo' => 'get_vimeo_id',
            'youtube' => 'get_youtube_id',
            'wistia' => 'get_wistia_id',
            'tiktok' => 'get_tiktok_id',
            'twitch' => 'get_twitch_id',
            'instagram' => 'get_instagram_id',
        ];

        foreach ($urlTypes as $type => $function) {
            if ($id = $function($field->value)) {
                $fieldArray['urlReferenceId'] = $id;
                $fieldArray['urlType'] = $type;

                return;
            }
        }

        $fieldArray['urlType'] = 'url';
        $fieldArray['externalUrl'] = external_url($field->value);
        $fieldArray['url2png'] = url2png($field->value, ['thumbnail_max_width' => config('ui.images.judging.preview_width')]);
    }

    /**
     * Get contributors for the submittable form
     *
     * @param  Form  $form
     */
    public function getContributors(Submittable $submittable): Collection
    {
        $contributorFields = $this->fields->getAllByFormAndResource($this->getForm($submittable), Field::RESOURCE_CONTRIBUTORS);

        return $submittable->contributors->map(function (Contributor $contributor) use ($contributorFields) {
            $values = $this->getValuesForEntityFields($contributor, $contributorFields, true);

            return $values->isNotEmpty() ? ['id' => $contributor->id, 'values' => clone $values, 'tabId' => $contributor->tabId] : null;
        })->filter()->groupBy('tabId');
    }

    public function getAttachments(Submittable $submittable)
    {
        $attachmentFields = $this->fields->getAllByFormAndResource(
            $this->getForm($submittable),
            Field::RESOURCE_ATTACHMENTS
        );

        return json_encode([
            'attachments' => $this->mapAttachmentsExtraProperties($submittable->orderedAttachments(), $attachmentFields),
            'uploaderOptions' => $this->uploaderOptionsByTabs($submittable),
            'links' => $this->parseLinks($submittable),
        ]);
    }

    private function parseLinks(Submittable $submittable)
    {
        return $submittable->links->map(function (Link $link) {
            return [
                'id' => $link->id,
                'tabId' => $link->tabId,
                'url' => $link->url,
                'extra' => $link->extra,
            ];
        })->groupBy('tabId');
    }

    /**
     * Returns uploader options keyed by every attachments tab id.
     */
    private function uploaderOptionsByTabs(Submittable $submittable): Collection
    {
        $attachmentsUploader = $this->attachmentsUploader;

        return $this->tabs->forResourceTypeAndForm(Tab::RESOURCE_ENTRIES, Tab::TYPE_ATTACHMENTS, $submittable->getFormId())
            ->mapWithKeys(function (Tab $tab) use ($attachmentsUploader, $submittable) {
                return [$tab->id => obfuscate($attachmentsUploader->uploaderForEntryAndTab($submittable, $tab)->options())->toHtml()];
            });
    }

    /**
     * Return uploader options for the Attachments tab.
     *
     * @return Uploader
     */
    protected function uploaderOptionsForTab(Tab $tab, Submittable $submittable)
    {
        return $tab->type === Tab::TYPE_ATTACHMENTS ?
            $this->attachmentsUploader->uploaderForEntryAndTab($submittable, $tab)->options() :
            null;
    }

    /**
     * Key attachments by tab id preserving the order.
     *
     * @param  Entry  $entry
     */
    protected function mapAttachmentsExtraProperties(Collection $orderedAttachments, Collection $fields): Collection
    {
        return $orderedAttachments
            ->filter(fn(Attachment $attachment) => ! empty($attachment->file?->file))
            ->map(function (Attachment $attachment) use ($fields) {
                $values = $this->getValuesForEntityFields($attachment, $fields);
                $file = $attachment->file;

                return [
                    'id' => $attachment->id,
                    'tabId' => $attachment->tabId,
                    'fileId' => $file->id,
                    'order' => $attachment->order,
                    'file' => json_encode($this->formFileMapper->mapFile($file, $attachment)),
                    'values' => $values->count() ? clone $values : null,
                ];
            })
            ->groupBy('tabId');
    }

    private function getValuesForEntityFields(Model $entity, Collection $fields, bool $withEmpty = false)
    {
        return $this->values->mapValuesToFields($entity, $fields)
            ->filter(fn(Field $field) => $withEmpty || ! empty($field->value))
            ->mapWithKeys(fn($field) => [(string) $field->slug => $field->value]);
    }

    protected static function mapSubmittable(Submittable $submittable): array
    {
        return [
            'id' => $submittable->id,
            'slug' => (string) $submittable->slug,
            'chapterId' => $submittable->getChapterId(),
            'categoryId' => $submittable->getCategoryId(),
            'submittedAt' => $submittable->submittedAt,
            'updatedAt' => $submittable->updatedAt->toDateTimeString(),
            'seasonId' => $submittable->getSeasonId(),
        ];
    }

    protected function chapterFiles(array $chapterIds): Collection
    {
        return $this->files
            ->getByResourceId(File::RESOURCE_CHAPTERS, $chapterIds)
            ->groupBy('resource_id');
    }

    abstract protected function activeRoundsChapterIds(?Submittable $submittable = null): array;

    abstract public function getChapters(?Submittable $submittable = null, $locked = false): Collection;

    public function singleChapter(Chapter $chapter, ?Entry $entry = null, bool $appliesToForm = false): array
    {
        return $this->mapChapter(
            $chapter,
            $this->chapterFiles([$chapter->id]),
            $this->activeRoundsChapterIds($entry),
            $appliesToForm
        );
    }

    public function mapChapter(
        Chapter $chapter,
        ?Collection $files = null,
        array $activeRoundsChapterIds = [],
        bool $appliesToForm = false
    ): array {
        return [
            'active' => (bool) $chapter->active,
            'createdAt' => $chapter->createdAt ? (string) $chapter->createdAt : null,
            'deletedAt' => $chapter->deletedAt ? (string) $chapter->deletedAt : null,
            'files' => $files ? $this->mapChapterFiles($chapter, $files) : [],
            'heading' => $chapter->image_heading,
            'id' => $chapter->id,
            'inForm' => $appliesToForm,
            'inActiveRound' => Consumer::isManager() ? in_array($chapter->id, $activeRoundsChapterIds) : true,
            'inArchivedSeason' => $chapter->season ? $chapter->season->isArchived() : false,
            'locked' => (bool) $chapter->locked,
            'managerIds' => $chapter->managers->pluck('id')->toArray(),
            'maxImageWidth' => $chapter->maxImageWidth,
            'name' => $chapter->name,
            'description' => $chapter->description ? Output::html($chapter->description) : null,
            'slug' => (string) $chapter->slug,
            'translated' => $chapter->translated,
            'uploaderOptions' => obfuscate($this->chapterUploaderOptions($chapter))->toHtml(),
        ];
    }

    private function chapterUploaderOptions(Chapter $chapter): ?array
    {
        if (! $chapter->exists) {
            return null;
        }

        return $this->chapterUploadValidator
            ->setupUploader($this->setupUploader(), $chapter->id)
            ->setTempPrefix($chapter->slug)
            ->setMultiSelect(true)
            ->options();
    }

    private function mapChapterFiles(Chapter $chapter, Collection $files): array
    {
        if (! feature_enabled('sponsors')) {
            return [];
        }

        return $files->has($chapter->id) ?
            $files->get($chapter->id)->map(function ($file) {
                return $this->formFileMapper->mapFile($file);
            })->toArray() : [];
    }

    public function fieldTemplate(?Submittable $submittable = null): array
    {
        $template = new Field;

        $template->id = 0;
        $template->slug = new \Eloquence\Behaviours\Slug('template');

        $template->categoryOption = Field::CATEGORY_OPTION_ALL;
        $template->conditional = false;
        $template->entrantReadAccess = true;
        $template->entrantWriteAccess = true;
        $template->plagiarismDetection = true;
        $template->preselectCurrentDate = true;
        $template->required = false;

        $template->injectTranslations([
            'label' => [Consumer::languageCode() => trans('fields.new_field')],
            'title' => [Consumer::languageCode() => trans('fields.new_field')],
        ]);

        $fieldArray = $this->mapField($template, $submittable);

        foreach (config('awardforce.fields.types') as $type) {
            $template->type = $type;
            $this->mapSpecificFields($template, $submittable, $fieldArray);
        }

        return $fieldArray;
    }

    public function tabTemplate(?Submittable $submittable = null): array
    {
        $template = new Tab;

        $template->id = 0;
        $template->slug = new \Eloquence\Behaviours\Slug('template');

        $template->categoryOption = Field::CATEGORY_OPTION_ALL;
        $template->locked = false;
        $template->resource = Tab::RESOURCE_ENTRIES;
        $template->visibleToEntrants = true;

        $template->injectTranslations([
            'name' => [Consumer::languageCode() => trans('tabs.new_tab')],
        ]);

        return $this->mapTab($template, $submittable);
    }

    public function categoryTemplate(): array
    {
        $template = new Category;

        $template->id = 0;
        $template->slug = new \Eloquence\Behaviours\Slug('template');
        $template->active = true;

        $template->injectTranslations([
            'name' => [Consumer::languageCode() => trans('category.new_category')],
        ]);

        return $this->generator->mapCategory($template);
    }

    public function chapterTemplate(): array
    {
        $template = new Chapter;

        $template->id = 0;
        $template->slug = new \Eloquence\Behaviours\Slug('template');
        $template->active = true;

        $template->injectTranslations([
            'name' => [Consumer::languageCode() => trans('chapters.new_chapter')],
        ]);

        return $this->mapChapter($template, null, [], true);
    }

    private function populateConditional(Fields $fields): Fields
    {
        return $fields->merge($fields->map(function (Field $field) {
            if ($field->conditional) {
                if ($field->conditionalField->conditional) {
                    return $this->populateConditional(new Fields([$field->conditionalField]));
                }

                return $field->conditionalField;
            }
        })->sortBy('order')->flatten()->filter());
    }

    protected function fieldOptions(Field $field)
    {
        return Consumer::isManager() ? $field->options->forList() : $field->options->forListWithoutScore();
    }

    public function referees(Submittable $submittable): Collection
    {
        $refereeFields = $this->fields->getAllByFormAndResource(
            $this->getForm($submittable),
            Field::RESOURCE_REFEREES
        );

        return $this->referees
            ->fields([
                'id',
                'name',
                'email',
                'tab_id',
                'submittable_id',
                'review_task_id',
                'request_sent_at',
                'request_completed_at',
                'values',
                'hashes',
                'protected',
            ])
            ->submittable($submittable->id)
            ->with(['reviewTask:id,token'])
            ->withoutSoftDeletedReviewTasks()
            ->get()
            ->map(function (Referee $refere) use ($refereeFields) {
                $values = $this->getValuesForEntityFields($refere, $refereeFields);

                return [
                    'id' => $refere->id,
                    'name' => $refere->name,
                    'email' => $refere->email,
                    'tabId' => $refere->tabId,
                    'submittableId' => $refere->submittableId,
                    'reviewTask' => (string) $refere->reviewTask?->token,
                    'values' => $values->count() ? clone $values : [],
                    'requestSentAt' => $refere->requestSentAtFormatted(),
                    'requestSent' => $refere->requestSent(),
                    'requestCompletedAt' => $refere->requestCompletedAtFormatted(),
                    'requestCompleted' => $refere->requestCompleted(),
                ];
            })->groupBy('tabId');
    }
}
