<?php

namespace AwardForce\Modules\Entries\Services;

use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Services\Uploader;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\ImageDimensionConstraints;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\EloquentFieldRepository;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Referees\Models\Referee;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\Data\ReviewTaskRepository;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Mockery as m;
use Platform\Search\HasValues;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class SubmittableFormServiceTest extends BaseTestCase
{
    use Database;
    use Laravel;

    /**
     * @var m\MockInterface
     */
    protected $attachmentsUploaderMock;

    /**
     * @var m\MockInterface
     */
    protected $fieldRepositoryMock;

    /**
     * @var m\MockInterface
     */
    protected $tabRepositoryMock;

    /**
     * @var EntryFormService
     */
    protected $entryFormService;

    public function init(): void
    {
        $this->attachmentsUploaderMock = m::mock(AttachmentsUploader::class);
        $this->fieldRepositoryMock = m::mock(FieldRepository::class);
        $this->tabRepositoryMock = m::mock(TabRepository::class);

        app()->instance(AttachmentsUploader::class, $this->attachmentsUploaderMock);
        app()->instance(FieldRepository::class, $this->fieldRepositoryMock);
        app()->instance(TabRepository::class, $this->tabRepositoryMock);
    }

    public function testItGetFields(): void
    {
        $form = $this->muffin(Form::class);
        $tabMock = $this->muffin(Tab::class, [
            'type' => Tab::TYPE_FIELDS,
            'form_id' => $form->id,
        ]);

        $dropDownListFieldMock = $this->muffin(Field::class, [
            'type' => 'drop-down-list',
        ]);

        $tableFieldMock = $this->muffin(Field::class, [
            'type' => 'table',
        ]);

        $checkboxField1Mock = $this->muffin(Field::class, [
            'type' => 'checkbox',
        ]);

        $checkboxField2Mock = $this->muffin(Field::class, [
            'type' => 'checkboxlist',
        ]);

        $radioFieldMock = $this->muffin(Field::class, [
            'type' => 'radio',
        ]);

        $fieldsCollection = new Fields([
            $dropDownListFieldMock,
            $tableFieldMock,
            $checkboxField1Mock,
            $checkboxField2Mock,
            $radioFieldMock,
        ]);

        $submittable = m::mock(Entry::class, ['form_id' => $form->id]);

        $checkboxField1Value = '4x4';
        $values = [
            (string) $dropDownListFieldMock->slug => $dropDownListFieldValue = 'Mercedes',
            (string) $tableFieldMock->slug => $tableFieldValue = '{"dynamicRows":[],"values":{"row-JjbPW":{"column-rIfjk":"1234"}}}',
            (string) $checkboxField1Mock->slug => '["'.$checkboxField1Value.'"]',
            (string) $checkboxField2Mock->slug => '',
            (string) $radioFieldMock->slug => $radioFieldValue = '1',
        ];

        $submittable->shouldReceive('valuesData')->andReturn(new class($values, [], []) implements HasValues
        {
            public function __construct(public array $values, public array $protected, public array $hashes)
            {
            }
        });

        $this->fieldRepositoryMock->shouldReceive('forTab')->andReturn($fieldsCollection);
        $this->fieldRepositoryMock->shouldReceive('getAllByFormAndResource')->andReturn($fieldsCollection);
        $this->tabRepositoryMock->shouldReceive('getFromForm')->andReturn(new Collection([$tabMock]));

        $submittableFormService = app(EntrantEntryFormService::class);

        $result = $submittableFormService->getFields($tabMock, $submittable);

        $this->assertInstanceOf(Collection::class, $result);
        $this->assertCount(5, $result);
        $this->assertEquals($dropDownListFieldValue, Arr::get($result->firstWhere('slug', (string) $dropDownListFieldMock->slug), 'value'));
        $this->assertEquals($tableFieldValue, Arr::get($result->firstWhere('slug', (string) $tableFieldMock->slug), 'value'));
        $this->assertEquals([$checkboxField1Value], Arr::get($result->firstWhere('slug', (string) $checkboxField1Mock->slug), 'value'));
        $this->assertEquals([], Arr::get($result->firstWhere('slug', (string) $checkboxField2Mock->slug), 'value'));
        $this->assertEquals($radioFieldValue, Arr::get($result->firstWhere('slug', (string) $radioFieldMock->slug), 'value'));
    }

    public function testexistsForFieldShouldRunOnlyForTableFields()
    {
        $form = $this->muffin(Form::class);
        $tableField = $this->muffin(Field::class, [
            'type' => 'table',
        ]);
        $textField = $this->muffin(Field::class, [
            'type' => 'text',
        ]);
        $otherTableField = $this->muffin(Field::class, [
            'type' => 'table',
        ]);

        $submittable = m::mock(Entry::class, ['form_id' => $form->id]);

        $entryRepository = $this->mock(EntryRepository::class);
        $entryRepository->shouldReceive('existsForField')->with($tableField)->once()->andReturnFalse();
        $entryRepository->shouldReceive('existsForField')->with($otherTableField)->once()->andReturnFalse();
        $entryRepository->shouldNotReceive('existsForField')->with($textField);

        $submittableFormService = app(EntrantEntryFormService::class);

        $resultTableField = $submittableFormService->mapField($tableField, $submittable);
        $resultTextField = $submittableFormService->mapField($textField, $submittable);
        $resultTextField = $submittableFormService->mapField($otherTableField, $submittable);
    }

    public function testMapFieldContainsMinVideoLengthAttribute(): void
    {
        $fileField = $this->muffin(Field::class, ['min_video_length' => $maxVideoLength = random_int(1, 10000)]);
        $submittableFormService = app(EntrantEntryFormService::class);

        $resultFileField = $submittableFormService->mapField($fileField, $this->muffin(Entry::class));

        $this->assertSame($resultFileField['minVideoLength'], $maxVideoLength);
    }

    public function testMapFieldStripsSpecialWhitespaceCharacters(): void
    {
        $field = $this->muffin(Field::class, [
            'type' => 'drop-down-list',
            'options' => '{" val​":0}',
        ]);
        $field->addTranslation('en_GB', 'optionText', json_encode([' val​' => ' Hello, World! ']));

        $submittableFormService = app(EntrantEntryFormService::class);
        $mapped = $submittableFormService->mapField($field, $this->muffin(Entry::class));

        $this->assertEquals('Hello, World!', $mapped['translated']['en_GB']['optionText']['val']);
    }

    public function testMapFieldContainsMaxVideoLengthAttribute(): void
    {
        $fileField = $this->muffin(Field::class, ['max_video_length' => $maxVideoLength = random_int(1, 10000)]);
        $submittableFormService = app(EntrantEntryFormService::class);

        $resultFileField = $submittableFormService->mapField($fileField, $this->muffin(Entry::class));

        $this->assertSame($resultFileField['maxVideoLength'], $maxVideoLength);
    }

    public function testMapFieldContainsImageDimensionConstraintAttributes(): void
    {
        $fileField = $this->muffin(Field::class, ['image_dimension_constraints' => new ImageDimensionConstraints(
            $maxWidth = 40,
            $minWidth = 30,
            $maxHeight = 20,
            $minHeight = 10,
        )]);
        $submittableFormService = app(EntrantEntryFormService::class);

        $resultFileField = $submittableFormService->mapField($fileField, $this->muffin(Entry::class));

        $this->assertSame($resultFileField['imageDimensionConstraints']->maxWidth, $maxWidth);
        $this->assertSame($resultFileField['imageDimensionConstraints']->minWidth, $minWidth);
        $this->assertSame($resultFileField['imageDimensionConstraints']->maxHeight, $maxHeight);
        $this->assertSame($resultFileField['imageDimensionConstraints']->minHeight, $minHeight);
    }

    public function testMapFieldContainsUploaderOptions(): void
    {
        \Session::start();
        $fileField = $this->muffin(Field::class, ['type' => fn() => Field::TYPE_FILE]);
        $entry = $this->muffin(Entry::class);
        $submittableFormService = app(EntrantEntryFormService::class);

        $resultFileField = $submittableFormService->mapField($fileField, $entry);
        $this->assertArrayHasKey('uploaderOptions', $resultFileField);

        $uploaderOptions = $resultFileField['uploaderOptions'];
        $this->assertIsString($uploaderOptions);

        $decodedUploaderOption = json_decode(base64_decode($uploaderOptions), true);
        $this->assertIsArray($decodedUploaderOption);

        $this->assertArrayHasKey('s3', $decodedUploaderOption);
        $this->assertArrayHasKey('resource', $decodedUploaderOption);
    }

    public function testMapTabIncludesUploaderOptions(): void
    {
        $uploader = m::mock(Uploader::class);
        $uploader->shouldReceive('options')->once()->andReturn(['maxFiles' => 3]);

        $this->attachmentsUploaderMock->shouldReceive('uploaderForEntryAndTab')->once()->andReturn($uploader);

        $form = $this->muffin(Form::class);
        $tab = $this->muffin(Tab::class, ['resource' => Tab::RESOURCE_ENTRIES, 'type' => Tab::TYPE_ATTACHMENTS, 'form_id' => $form->id]);

        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);

        $result = app(EntrantEntryFormService::class)->mapTab($tab, $entry);

        $this->assertArrayHasKey('uploaderOptions', $result);
        $this->assertSame(base64_decode($result['uploaderOptions']), json_encode(['maxFiles' => 3]));
    }

    public function testReferees()
    {
        $tabRefereeOne = $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES]);
        $tabRefereeTwo = $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES]);
        $fieldsCollection = new Fields([
            $this->muffin(Field::class, [
                'tab_id' => $tabRefereeOne->id,
                'type' => 'text',
                'resource' => Field::RESOURCE_REFEREES,
            ]),
            $this->muffin(Field::class, [
                'tab_id' => $tabRefereeTwo->id,
                'type' => 'text',
                'resource' => Field::RESOURCE_REFEREES,
            ]),
        ]);

        $this->fieldRepositoryMock->shouldReceive('getAllByFormAndResource')->andReturn($fieldsCollection);
        $entry = $this->muffin(Entry::class);
        $this->muffins(2, Referee::class, ['submittable_id' => $entry->id, 'tab_id' => $tabRefereeOne->id]);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->id, 'tab_id' => $tabRefereeTwo->id]);

        $submittableFormService = app(EntrantEntryFormService::class);
        $referees = $submittableFormService->referees($entry);
        $keysToReturn = ['id', 'name', 'email', 'tabId', 'submittableId', 'reviewTask', 'values', 'requestSentAt', 'requestSent', 'requestCompletedAt', 'requestCompleted'];

        $this->assertCount(2, $referees[$tabRefereeOne->id]);
        $this->assertCount(1, $referees[$tabRefereeTwo->id]);
        $this->assertEquals($referee->name, $referees[$tabRefereeTwo->id][0]['name']);
        $this->assertEquals($keysToReturn, array_keys($referees[$tabRefereeTwo->id][0]));
    }

    public function testRefereesWithoutReviewTaskAreRetrieved()
    {
        // release mock
        app()->instance(FieldRepository::class, app(EloquentFieldRepository::class));

        $entry = $this->muffin(Entry::class);
        $reviewTask = $this->muffin(ReviewTask::class, ['entry_id' => $entry->id]);
        $controlReferee = $this->muffin(Referee::class, ['submittable_id' => $entry->id, 'review_task_id' => $reviewTask->id]);
        $refereeWithoutReviewTask = $this->muffin(Referee::class, ['submittable_id' => $entry->id, 'review_task_id' => null]);

        $submittableFormService = app(EntrantEntryFormService::class);
        $refereesGroupedByTab = $submittableFormService->referees($entry);

        $this->assertCount(2, $refereesGroupedByTab);  // 1 tab per referee
        $this->assertEquals($controlReferee->name, $refereesGroupedByTab[$controlReferee->tab_id][0]['name']);
        $this->assertEquals($refereeWithoutReviewTask->name, $refereesGroupedByTab[$refereeWithoutReviewTask->tab_id][0]['name']);
    }

    public function testRefereesWithTrashedReviewTaskAreNotRetrieved()
    {
        // release mock
        app()->instance(FieldRepository::class, app(EloquentFieldRepository::class));

        $entry = $this->muffin(Entry::class);
        $reviewTask = $this->muffin(ReviewTask::class, ['entry_id' => $entry->id]);
        $controlReferee = $this->muffin(Referee::class, ['submittable_id' => $entry->id, 'review_task_id' => $reviewTask->id]);

        $deletedReviewTask = $this->muffin(ReviewTask::class, ['entry_id' => $entry->id]);
        app(ReviewTaskRepository::class)->delete($deletedReviewTask);
        $this->muffins(2, Referee::class, ['submittable_id' => $entry->id, 'review_task_id' => $deletedReviewTask->id]);

        $submittableFormService = app(EntrantEntryFormService::class);
        $refereesGroupedByTab = $submittableFormService->referees($entry);

        $this->assertCount(1, $refereesGroupedByTab); // 1 group
        $this->assertCount(1, $refereesGroupedByTab[$controlReferee->tab_id]); // 1 referee
        $this->assertEquals($controlReferee->name, $refereesGroupedByTab[$controlReferee->tab_id][0]['name']);
    }

    public function testGetContributors(): void
    {
        $entry = $this->muffin(Entry::class);

        $tab1 = $this->muffin(Tab::class, [
            'type' => Tab::TYPE_CONTRIBUTORS,
            'resource' => Tab::RESOURCE_ENTRIES,
        ]);

        $tab2 = $this->muffin(Tab::class, [
            'type' => Tab::TYPE_CONTRIBUTORS,
            'resource' => Tab::RESOURCE_ENTRIES,
        ]);

        $valuesServiceMock = m::mock(ValuesService::class);
        app()->instance(ValuesService::class, $valuesServiceMock);

        $fieldsCollection = new Fields([
            $this->muffin(Field::class, [
                'tab_id' => $tab1->id,
                'type' => 'text',
                'resource' => Field::RESOURCE_CONTRIBUTORS,
            ]),
            $this->muffin(Field::class, [
                'tab_id' => $tab2->id,
                'type' => 'text',
                'resource' => Field::RESOURCE_CONTRIBUTORS,
            ]),
        ]);

        $valuesServiceMock->shouldReceive('mapValuesToFields')->andReturn($fieldsCollection);
        $this->tabRepositoryMock->shouldReceive('forResourceTypeAndForm')->andReturn(new Collection([$tab1, $tab2]));
        $this->fieldRepositoryMock->shouldReceive('getAllByFormAndResource')->andReturn($fieldsCollection);

        $this->muffin(Contributor::class, ['submittable_id' => $entry->id, 'tab_id' => $tab1->id]);
        $this->muffin(Contributor::class, ['submittable_id' => $entry->id, 'tab_id' => $tab1->id]);
        $this->muffin(Contributor::class, ['submittable_id' => $entry->id, 'tab_id' => $tab2->id]);

        $submittableFormService = app(EntrantEntryFormService::class);
        $contributors = $submittableFormService->getContributors($entry);

        $this->assertCount(2, $contributors);
        $this->assertCount(2, $contributors[$tab1->id]);
        $this->assertCount(1, $contributors[$tab2->id]);
    }
}
