<?php

namespace AwardForce\Modules\Entries\Projections;

use AwardForce\Library\EventSourcing\ConsumableProjector;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\FieldValues\Events\FieldValuesCreated;
use EventSauce\EventSourcing\EventConsumption\EventConsumer;
use EventSauce\EventSourcing\Message;

class Entries extends EventConsumer implements ConsumableProjector
{
    public function __construct(private ValuesService $values, private EntryRepository $entries)
    {
    }

    public function handleFieldValuesCreated(FieldValuesCreated $event, Message $message): void
    {
        $this->values->setValuesForObject(
            $event->fieldValues->toArrayWithKeys(),
            $this->entries->requireById($event->resourceId->value)
        );
    }
}
