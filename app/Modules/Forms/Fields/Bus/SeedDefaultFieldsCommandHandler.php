<?php

namespace AwardForce\Modules\Forms\Fields\Bus;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Translation\Translator;
use Platform\Events\EventDispatcher;

class SeedDefaultFieldsCommandHandler
{
    use EventDispatcher;

    /**
     * @var FieldRepository
     */
    private $fields;

    /**
     * @var CategoryRepository
     */
    private $categories;

    /**
     * @var TabRepository
     */
    private $tabs;

    /**
     * @var SeasonRepository
     */
    private $seasons;

    /**
     * @var Tab
     */
    private $tab;

    /**
     * @var int
     */
    private $seasonId;

    /**
     * @var \AwardForce\Modules\Accounts\Models\Account
     */
    private $account;

    /**
     * @var mixed
     */
    private $detailsTab;

    /**
     * @var Translator
     */
    private $translator;

    /**
     * SeedDefaultFieldsCommandHandler constructor.
     */
    public function __construct(
        FieldRepository $fields,
        CategoryRepository $categories,
        TabRepository $tabs,
        SeasonRepository $seasons,
        Translator $translator
    ) {
        $this->fields = $fields;
        $this->categories = $categories;
        $this->tabs = $tabs;
        $this->seasons = $seasons;
        $this->translator = $translator;

        $this->tab = $this->tabs->getOneBy('type', Tab::TYPE_FIELDS);
        $this->detailsTab = $this->tabs->getOneBy('type', Tab::TYPE_DETAILS);
    }

    public function handle(SeedDefaultFieldsCommand $command)
    {
        $this->seasonId = $this->seasons->getActiveId();

        $this->account = $command->account;

        foreach (config('seeds.'.Vertical::translationsKey().'.fields') as $fieldKey => $data) {
            $this->seedDefaultSampleField($fieldKey, $data);
        }
    }

    public function seedDefaultSampleField($fieldKey, $data)
    {
        $defaultParams = config('seeds.defaultParams');

        $field = Field::add(
            $this->account->id,
            $this->seasonId,
            FormSelector::defaultForSeason($this->seasonId)->id,
            Arr::has($data, 'tab') ? $this->getFieldTabId($data['tab']) : null,
            $data['resource'],
            $data['type'],
            $data['seasonal'] ?? true,
            $data['options'] ?? '',
            $data['order'],
            $data['categoryOption'] ?? 'all',
            $data['roleOption'] ?? 'all',
            0,
            false,
            array_merge($defaultParams, $data['params'] ?? [])
        );

        $this->addTranslations($field, $this->account->languages, $fieldKey);

        $this->dispatch($field->releaseEvents());
    }

    private function syncCategories(Field $field)
    {
        $ids = $this->categories->getAll()->just('id');

        foreach ($ids as $id) {
            DB::table('category_field')->insert([
                'category_id' => $id,
                'field_id' => $field->id,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);
        }
    }

    /**
     * @param  Field  $field
     * @param  Collection  $supportedLanguages
     * @param  string  $fieldKey
     */
    private function addTranslations($field, $supportedLanguages, $fieldKey)
    {
        $this->addTranslation($field, $supportedLanguages, $fieldKey, 'label');
        $this->addTranslation($field, $supportedLanguages, $fieldKey, 'title');
        $this->addTranslation($field, $supportedLanguages, $fieldKey, 'helpText');
        $this->addTranslation($field, $supportedLanguages, $fieldKey, 'hintText');
        $this->addTranslation($field, $supportedLanguages, $fieldKey, 'optionText');
    }

    /**
     * @param  Field  $field
     * @param  Collection  $supportedLanguages
     * @param  string  $fieldKey
     * @param  string  $key
     */
    private function addTranslation($field, $supportedLanguages, $fieldKey, $key)
    {
        $translationKey = 'fields.'.Vertical::translationsKey().'.'.$fieldKey.'.'.$key;
        foreach ($supportedLanguages as $language) {
            if (! $this->translator->has($translationKey, $language->code, false)) {
                continue;
            }

            // Hack. Translated values for optionText do not preserve the line break format used in English, which we rely on on multiple places
            $formattedTranslation = str_replace("\n", "\r\n", trans($translationKey, [], $language->code));

            $field->saveTranslation(
                $language->code,
                $key,
                $formattedTranslation,
                $field->accountId
            );
        }
    }

    private function getFieldTabId($tabKey)
    {
        if ($tabKey === 'details') {
            return $this->detailsTab->id;
        }

        return Cache::store('array')->get(ucfirst($tabKey), $this->tab->id);
    }
}
