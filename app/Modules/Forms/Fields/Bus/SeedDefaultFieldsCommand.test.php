<?php

namespace AwardForce\Modules\Forms\Fields\Bus;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Bus\SeedDefaultTabsCommand;
use AwardForce\Modules\Forms\Tabs\Bus\SeedDefaultTabsCommandHandler;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use Illuminate\Support\Facades\Cache;
use Tests\IntegratedTestCase;
use Tests\OrganisationFieldValidation;

final class SeedDefaultFieldsCommandTest extends IntegratedTestCase
{
    use OrganisationFieldValidation;

    /**
     * @var FieldRepository
     */
    private $fields;

    /**
     * @var |null
     */
    private $attachmentsTab;

    /**
     * @var TabRepository|\Illuminate\Contracts\Foundation\Application|mixed
     */
    private $tabs;

    public function init()
    {
        $this->fields = app(FieldRepository::class);
        $this->tabs = app(TabRepository::class);
        $this->account->vertical = 'awards';
        app(SeedDefaultTabsCommandHandler::class)->handle(new SeedDefaultTabsCommand($this->account));
        $this->attachmentsTab = $this->tabs->getById(Cache::store('array')->get('Attachments'));
    }

    public function testItAssignsAttachmentFieldsToAttachmentsTab(): void
    {
        app(SeedDefaultFieldsCommandHandler::class)->handle(new SeedDefaultFieldsCommand($this->account));

        $fields = $this->attachmentsTab->fields;

        $this->assertEquals(1, $fields->count());
        $this->assertEquals($this->attachmentsTab->id, ($field = $fields->first())->tabId);
        $this->assertSame($formId = FormSelector::getId(), $this->attachmentsTab->formId);
        $this->assertSame($formId, $field->formId);
    }

    //    public function testOrganisationsFields()
    //    {
    //        app(SeedDefaultFieldsCommandHandler::class)->handle(new SeedDefaultFieldsCommand($this->account));
    //
    //        $orgFields = $this->fields->getByResource(Field::RESOURCE_ORGANISATIONS);
    //        $this->assertCount(4, $orgFields);
    //        $orgFields->each(fn($orgField) => $this->validateOrgField($orgField));
    //    }
}
