<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Entries\Contracts\AttachmentRepository;
use AwardForce\Modules\Entries\Contracts\ContributorRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Events\EntryWasUpdated;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Database\Repositories\RepositoryWithValues;
use AwardForce\Modules\Forms\Fields\Events\FieldValueUpdated;
use AwardForce\Modules\Forms\Fields\Events\UpdatingFieldValuesAfterEncoding;
use AwardForce\Modules\Forms\Fields\Events\UpdatingFieldValuesBeforeEncoding;
use AwardForce\Modules\Forms\Fields\Exceptions\CannotLoadFieldValue;
use AwardForce\Modules\Forms\Fields\Exceptions\CannotModifyFieldValue;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\Identity\Users\Events\UserWasUpdated;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Referees\Contracts\RefereeRepository;
use AwardForce\Modules\Referees\Models\Referee;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Encryption\Encrypter;
use Platform\Events\EventDispatcher;
use Platform\Search\HasValues;

/**
 * The FieldValues service can be used to sync data for a given resource, or search for and bind
 * fields and field values to a givne resource, or a resource collection.
 */
class ValuesService
{
    use EventDispatcher;

    /** @var FieldRepository */
    private $fields;

    /** @var Encrypter */
    private $encrypter;

    /** @var object */
    private $repositories = [];

    /** @var object */
    private $modelRepositoryMapping = [];

    /** @var FileRepository */
    private $files;

    public function __construct(
        FieldRepository $fields,
        Encrypter $encrypter,
        EntryRepository $entryRepository,
        MembershipRepository $membershipRepository,
        AttachmentRepository $attachmentRepository,
        ContributorRepository $contributorRepository,
        FileRepository $files,
        GrantReportRepository $grantReportRepository,
        RefereeRepository $refereeRepository,
        OrganisationProjectionRepository $organisationRepository
    ) {
        $this->fields = $fields;
        $this->encrypter = $encrypter;
        $this->repositories = $this->makeObjectRepositoryMappings(
            $entryRepository,
            $membershipRepository,
            $attachmentRepository,
            $contributorRepository,
            $grantReportRepository,
            $refereeRepository,
            $organisationRepository
        );
        $this->modelRepositoryMapping = $this->makeClassResourceMapping();
        $this->files = $files;
    }

    /**
     * sets an array of repositories keyed with concerning objects
     * this is a helper structure for further, generalised usage
     * called once in service constructor
     */
    private function makeObjectRepositoryMappings(
        EntryRepository $entryRepository,
        MembershipRepository $membershipRepository,
        AttachmentRepository $attachmentRepository,
        ContributorRepository $contributorRepository,
        GrantReportRepository $grantReportRepository,
        RefereeRepository $refereeRepository,
        OrganisationProjectionRepository $organisationRepository
    ): array {
        return [
            Form::FORM_TYPE_ENTRY => [
                Field::RESOURCE_FORMS => $entryRepository,
                Field::RESOURCE_ATTACHMENTS => $attachmentRepository,
                Field::RESOURCE_CONTRIBUTORS => $contributorRepository,
                Field::RESOURCE_REFEREES => $refereeRepository,
            ],
            Form::FORM_TYPE_REPORT => [
                Field::RESOURCE_FORMS => $grantReportRepository,
                Field::RESOURCE_ATTACHMENTS => $attachmentRepository,
                Field::RESOURCE_CONTRIBUTORS => $contributorRepository,
                Field::RESOURCE_REFEREES => $refereeRepository,
            ],
            Form::FORM_TYPE_EMPTY => [
                Field::RESOURCE_USERS => $membershipRepository,
                Field::RESOURCE_FORMS => $entryRepository,
                Field::RESOURCE_ATTACHMENTS => $attachmentRepository,
                Field::RESOURCE_CONTRIBUTORS => $contributorRepository,
                Field::RESOURCE_REFEREES => $refereeRepository,
                Field::RESOURCE_ORGANISATIONS => $organisationRepository,
            ],
        ];
    }

    /**
     * sets an array of resource types keyed with concerning objects
     * this is a helper structure for further, generalised usage
     * called once in service constructor
     */
    private function makeClassResourceMapping(): array
    {
        return [
            Entry::class => Field::RESOURCE_FORMS,
            Membership::class => Field::RESOURCE_USERS,
            Attachment::class => Field::RESOURCE_ATTACHMENTS,
            Contributor::class => Field::RESOURCE_CONTRIBUTORS,
            GrantReport::class => Field::RESOURCE_FORMS,
            Referee::class => Field::RESOURCE_REFEREES,
            //            Organisation::class => Field::RESOURCE_ORGANISATIONS,
        ];
    }

    /**
     * generate a hash or value for searching purposes
     * ( stored in `hashes` json field next to `values`
     *
     * @param  mixed  $value
     * @return string|null
     */
    private function hash(Field $field, $value)
    {
        if ($this->searchable($field)) {
            switch ($field->protection) {
                case Field::PROTECTION_ELEVATED:
                    $sanitised = preg_replace('/[\x00-\x1F\x7F\s]/s', '', mb_strtolower($value));

                    return hash('sha256', $field->slug.':'.$sanitised);
                case Field::PROTECTION_MAXIMUM:
                    return null;
                default:
                    return mb_strtolower($this->normaliseValue($field, $value));
            }
        } else {
            return null;
        }
    }

    /**
     * encode a field value according to Field::protection setting
     *
     * @param  mixed  $value
     */
    private function value(Field $field, $value): ?string
    {
        switch ($field->protection) {
            case Field::PROTECTION_ELEVATED:
                return $this->encrypter->encrypt($value);
            case Field::PROTECTION_MAXIMUM:
                return $this->encrypter->maximum($value);
            default:
                return $value;
        }
    }

    private function normaliseValue(Field $field, $value): ?string
    {
        $fieldType = $field->type;
        if (is_string($value) && in_array($fieldType, [Field::TYPE_TEXT, Field::TYPE_TEXTAREA])) {
            $value = preg_replace_callback(
                "/\[(.*?)]\s*+\(http?:\/\/(\S+\.\S+)\)/i",
                static fn($matches) => '['.$matches[1].'](https://'.$matches[2].')',
                $value
            );
        }

        if (is_array($value) || is_object($value)) {
            // normalise date format
            if (in_array($fieldType, [Field::TYPE_DATE, Field::TYPE_DATETIME]) && ! empty($value[$fieldType])) {
                $value[$fieldType] = Carbon::parse($value[$fieldType])->format(
                    $fieldType === Field::TYPE_DATE ? 'Y-m-d' : 'Y-m-d H:i'
                );
            }

            return json_encode($value);
        }

        $json = json_decode($value);
        if ($json && $value != $json) {
            return $field->isTable() ? $value : json_encode($value);
        }

        if (is_null($value) || is_string($value) || is_numeric($value) || is_bool($value)) {
            return $value;
        }

        throw new CannotModifyFieldValue();
    }

    /**
     * encodes slug->value table according to concerning fields settings
     */
    private function encodeValuesWithHashes(array $values, Fields $fields): array
    {
        $valuesWithHashes = $fields
            ->map(
                function (Field $field) use ($values) {
                    $value = $this->normaliseValue($field, $values[(string) $field->slug]);

                    return [
                        'key' => (string) $field->slug,
                        'value' => $this->value($field, $value),
                        'hash' => $this->hash($field, $value),
                        'protected' => $field->standardProtection() ? 0 : 1,
                    ];
                }
            );

        return [
            $valuesWithHashes->pluck('value', 'key')->toArray(),
            $valuesWithHashes->pluck('hash', 'key')->filter(function ($item) {
                return strlen($item ?? '');
            })->toArray(),
            $valuesWithHashes->pluck('protected', 'key')->toArray(),
        ];
    }

    /**
     * filter values for entrant access within a specific form/category
     */
    public function filterEntrantsValuesOnlyForFormAndCategory(array $values, int $formId, int $categoryId): array
    {
        $entrantWritableFieldsSlugs = $this->fields->getWritableForEntrant($formId, $categoryId);

        return array_only($values, $entrantWritableFieldsSlugs);
    }

    /**
     * overwrites or updates values, search hashes and protection for applicabe object     *
     *
     * @param  array|null  $values <- values to be saved keyed with field slug
     * @param  HasValues  $object <- an object has to implement HasValues interface
     * @param  bool  $wipeOut <- if true it does overwrite, otherwise its a merge
     * @param  bool  $timestamped <- if true then entity should be timestamped on update
     */
    private function updateValuesForObject(?array $values, HasValues $object, ?int $timestamp = null, bool $wipeOut = true, bool $timestamped = true): HasValues
    {
        /**
         * persist values if they have been changed
         * ( if not only save a record if its new )
         */
        if (([$updatedFields, $addedFields] = $this->updateValues($object, $values, $timestamp, $wipeOut, $timestamped)) !== null) {
            $this->fields->setFieldsHaveValues($addedFields->just('id'));
            $this->updateFilesForValues($updatedFields, $object, $values);
        }

        return $object;
    }

    /**
     * get collection of fields where hasValues should
     * be updated based on existing and changed values
     */
    private function addedFields(array $oldValues, array $newValues, Fields $fields): Collection
    {
        return $fields->filter(function (Field $field) use ($oldValues, $newValues) {
            $slug = (string) $field->slug;

            return array_key_exists($slug, $newValues)
                && $this->doesValueCount($field, $oldValues[$slug] ?? null) !== $this->doesValueCount($field, $newValues[$slug] ?? null);
        });
    }

    /**
     * check if values should be considered in Field::hasValues
     */
    private function doesValueCount(Field $field, $value): bool
    {
        return $value !== null && $value !== '' && ($field->type != 'checkbox' || $value !== 0);
    }

    /**
     * this method is the only responsible for doing values database writes
     * it all happens inside transaction to avoid risk for eventual races
     * first it reads data from the db
     * then it checks if its going to change with eventual write
     * if its going to change it does write - otherwise it ignores save
     * if it saves data it also updates values count on concerning fields
     *
     * @return Fields|null
     *
     * @throws \Throwable
     */
    private function updateValues(HasValues $object, ?array $newValues, ?int $timestamp, bool $wipeOut, bool $timestamped): ?array
    {
        return DB::transaction(function () use ($object, $newValues, $timestamp, $wipeOut, $timestamped) {
            /**
             * update values related fields for object with most recent data
             * this is done to be sure out further comparision is accurate
             * and not affected by any other session
             * this is LOCKING operation as values may not change outside of this transaction
             */
            [$oldEncodedValues, $oldHashes, $oldProtection] = $this->readObjectFromRepository($object);
            $oldValues = $this->getValuesForObject($object)->toArray();

            /**
             * if trying to save all null values just recalculate object
             */
            if ($newValues === null) {
                $newValues = $oldValues;
            }

            /**
             * grab collection of object fields - pre and post update
             */
            $allFields = $this->fields->getAllBySlugs(array_keys($newValues + $oldEncodedValues));

            $this->dispatch(new UpdatingFieldValuesBeforeEncoding($newValues, $oldValues, $allFields));

            $updatedFields = $allFields->filter(fn(Field $field) => isset($newValues[(string) $field->slug]));

            /**
             * get datastructures ready for persistance
             * ( i.e. encoded etc. )
             */
            [$newEncodedValues, $newHashes, $newProtection] = $this->encodeValuesWithHashes($newValues, $updatedFields);

            /**
             * according to wipeOut condition
             * perform data merge or overwrite
             */
            $newEncodedValues += $wipeOut ? [] : $oldEncodedValues;
            $newHashes += $wipeOut ? [] : $oldHashes;
            $newProtection += $wipeOut ? [] : $oldProtection;
            /**
             * this compares the data to be saved with actual database values
             * if it remains the same it does only save for object
             * that was not yet persisted ( newly created )
             */
            if (
                $this->compareObjects($object->values, $newEncodedValues) &&
                $this->compareObjects($object->hashes, $newHashes) &&
                $this->compareObjects($object->protected, $newProtection)
            ) {
                if (! ($object->id ?? 0)) {
                    $this->saveObjectViaRepository($object, $timestamped);
                } elseif ($object->isDirty('category_id')) {
                    $this->dispatch(new UpdatingFieldValuesAfterEncoding($object));
                }

                return null;
            }

            /**
             * if values have been changed lets save them to the database
             */
            $object->values = $newEncodedValues;
            $object->hashes = $newHashes;
            $object->protected = $newProtection;

            if ($timestamp && $object->isDirty()) {
                $object->updatedAt = $timestamp;
            }

            // Listen to modify the object based on its new values before saving
            $this->dispatch(new UpdatingFieldValuesAfterEncoding($object, $allFields));

            $this->saveObjectViaRepository($object, $timestamped);

            if ($updatedEvent = $this->raiseModelEvent($object)) {
                $object->raiseUnique($updatedEvent);
            }

            return [$updatedFields, $this->addedFields($oldValues, $newValues, $allFields)];
        });
    }

    /**
     * this method is being used to determine if values for object were actually changed
     * this check is done to avoid redundant saves to database
     * its suppoused to be key order insensitive
     */
    private function compareObjects(?array $a, ?array $b): bool
    {
        if ($a !== $b) {
            if (! is_array($a) || ! is_array($b)) {
                return false;
            }
            if (count($a) != count($b)) {
                return false;
            }
            foreach ($a as $key => $value) {
                if (! array_key_exists($key, $b)) {
                    return false;
                }
                if ($b[$key] != $value) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Syncs values for object by adding new values and updating existing ones (w/o delete)
     *
     * @throws CannotModifyFieldValue
     */
    public function syncValuesForObject(?array $values, HasValues $object, ?int $timestamp = null): HasValues
    {
        return $this->updateValuesForObject($values, $object, $timestamp, false);
    }

    public function syncIndividualFieldValueForObject(HasValues $object, Field $field, $value): HasValues
    {
        [$values, $hashes, $protected] = $this->encodeValuesWithHashes(
            [($fieldSlug = (string) $field->slug) => $value],
            new Fields([$field])
        );

        $this->getRepositoryForObject($object)
            ->updateSingleField(
                $object,
                $fieldSlug,
                $values[$fieldSlug] ?? '',
                $hashes[$fieldSlug] ?? '',
                $protected[$fieldSlug] ?? 0
            );

        $object->refresh();

        $object->raise(new FieldValueUpdated($object, $field, $value));

        return $object;
    }

    /**
     * overwrites values and search hashes for applicabe object
     *
     * @param  HasValues  $entity
     *
     * @throws CannotModifyFieldValue
     */
    public function setValuesForObject(?array $values, HasValues $object, ?int $timestamp = null): HasValues
    {
        return $this->updateValuesForObject($values, $object, $timestamp, true);
    }

    /**
     * get all values for a specified object
     */
    public function getValuesForObject(?HasValues $object, ?Fields $fields = null): Collection
    {
        return $this->mapValuesToFields($object, $fields)->map(
            function (Field $field) {
                return [
                    'key' => (string) $field->slug,
                    'value' => $field->value,
                ];
            }
        )->pluck('value', 'key');
    }

    /**
     * maps values into fields attribute
     *
     * @param  Model  $object|null
     */
    public function mapValuesToFields(?Model $entity, ?Fields $fields = null): Fields
    {
        if ($entity instanceof ValuesProvider) {
            return $this->mapFlatValuesToFields($entity->valuesData(), $fields)->sort([Field::class, 'compareTabAndFieldOrder']);
        }

        return ($fields ?? new Fields([]))->sort([Field::class, 'compareTabAndFieldOrder']);
    }

    private function mapFlatValuesToFields(HasValues $object, ?Fields $fields = null): Fields
    {
        $fields = $fields ?? $this->fields->getAllBySlugs($object->values ? array_keys($object->values) : []);

        return $fields->map(
            function (Field $field) use ($object) {
                $slug = (string) $field->slug;
                if (array_key_exists($slug, $object->values ?? [])) {
                    $field->value = $this->orderOptionable($field, $this->getFieldsValueFromObject($slug, $object));
                } else {
                    $field->value = null;
                }

                return clone $field;
            }
        );
    }

    /**
     * @return null|string
     */
    private function getFieldsValueFromObject(string $slug, HasValues $object)
    {
        $value = ($object->protected[$slug] ?? 0) && ! empty($object->values[$slug] ?? null) ? $this->encrypter->decrypt($object->values[$slug]) : $object->values[$slug];
        switch (true) {
            case in_array($value, ['true', 'false', 'null']):
            case is_array($value):  return $value;
            case is_numeric($value):  return "$value";
            default: return json_decode($value ?? '', true) ?? $value;
        }
    }

    /**
     * add 'where' clause to eloquent query according to passed values search
     * this does not verify passed eloquent Builder nor even resource type
     * however it does verify / filter out fields for slugs passed in search query
     */
    public function applyValuesSearchToEloquent(Builder $query, array $searches, ?array $resources = null): Builder
    {
        return $this->fields
            ->getAllQueryString(array_keys($searches))
            ->filter(
                function (Field $field) use ($resources) {
                    return $resources === null || in_array($field->resource, $resources);
                }
            )
            ->reduce(
                function ($previous, Field $field) use ($query, $searches) {
                    $table = $this->getRepositoryForField($field)->getModel()->getTable();
                    $q = $previous ?? $query;
                    if ($this->searchable($field)) {
                        $slug = (string) $field->slug;
                        $value = escape_string_for_query($searches[$slug]);
                        $value = html_entity_decode($value);

                        if ($field->elevatedProtection()) {
                            $q->where("$table.hashes->$slug", ''.$this->hash($field, $value));
                        } elseif (in_array($field->type, ['email', 'text', 'textarea', 'url', 'numeric', 'phone'])) {
                            $q->whereRaw(DB::raw("LOWER($table.hashes->'$.$slug') LIKE '%".mb_strtolower($value)."%'"));
                        } else {
                            $q->whereRaw(DB::raw("LOWER($table.hashes->'$.$slug') = '\"".mb_strtolower($value)."\"'"));
                        }
                    }

                    return $q;
                }
            ) ?? $query;
    }

    private function searchable(Field $field): bool
    {
        return in_array($field->type, config('awardforce.fields.searchable')) && ! $field->maximumProtection();
    }

    /**
     * recalculate values for given fields across all non-deleted entities
     * ( used on Field change )
     */
    public function recreateValuesForField(Field $field)
    {
        foreach ($this->getRepositoryForField($field)->searchByField($field) as $object) {
            if ($field->optionable()) {
                $this->modifyValueWithStrategy($field, $object, 'options');
            }
            $this->recalculateValuesForObject($object, false);
        }
    }

    /**
     * recalculate values for single entity object
     * ( used on entity model restoration
     *
     * @return HasValues
     */
    public function recalculateValuesForObject(HasValues $object, bool $timestamped = true)
    {
        return $this->updateValuesForObject(null, $object, null, true, $timestamped);
    }

    /**
     * get collection of missing and required fields for User object
     */
    public function getMissingRequiredUserFieldsForSeason(User $user, $seasonId, array $roleIds = []): Fields
    {
        $values = $this->getValuesForObject($user->currentMembership);

        return $this->fields->getMissingRequiredUserFieldsForSeason($user->id, $seasonId, $roleIds)
            ->filter(
                function (Field $field) use ($values) {
                    return $this->isFieldMissing($field, $values);
                }
            );
    }

    /**
     * get collection of all of missing fields for User object
     */
    public function getMissingUserFieldsForSeason(User $user, $seasonId, array $roleIds = []): Fields
    {
        $values = $this->getValuesForObject($user->currentMembership);

        return $this->fields->getMissingUserFieldsForSeason($user->id, $seasonId, $roleIds)
            ->filter(
                function (Field $field) use ($values) {
                    return $this->isFieldMissing($field, $values);
                }
            );
    }

    /**
     * check if value for a given field is missing ( or 'typewise' empty )
     */
    private function isFieldMissing(Field $field, Collection $values): bool
    {
        $value = $values->get((string) $field->slug) ?? null;

        return
            $value === null ||
            $value === '' ||
            ($field->type == 'checkbox' && $value == 0);
    }

    /**
     * @throws \AwardForce\Modules\Forms\Fields\Exceptions\CannotModifyFieldValue
     */
    public function nullifyValuesForFields(array $fieldIds, ?array $foreignIds = null): int
    {
        return $this->objectByFieldsAndIdsIterator(
            function (Field $field, HasValues $object) {
                $this->modifyValueWithStrategy($field, $object, 'nullify');
            },
            $fieldIds,
            $foreignIds
        );
    }

    /**
     * @throws \AwardForce\Modules\Forms\Fields\Exceptions\CannotModifyFieldValue
     */
    public function deleteValuesForFields(array $fieldIds, ?array $foreignIds = null): int
    {
        return $this->objectByFieldsAndIdsIterator(
            function (Field $field, HasValues $object) {
                $this->modifyValueWithStrategy($field, $object, 'unset');
            },
            $fieldIds,
            $foreignIds
        );
    }

    private function modifyValueWithStrategy(Field $field, HasValues $object, string $strategy): void
    {
        $removedFieldId = DB::transaction(function () use ($field, $object, $strategy) {
            [$values, $hashes] = $this->readObjectFromRepository($object);
            switch ($strategy) {
                case 'unset':
                    unset($values[(string) $field->slug], $hashes[(string) $field->slug]);

                    $timestamped = true;
                    break;
                case 'nullify':
                    $values[(string) $field->slug] = null;
                    unset($hashes[(string) $field->slug]);
                    $timestamped = true;
                    break;
                case 'options':
                    $values[(string) $field->slug] = $this->purgeOptionValues((string) $field->slug, $field->options->keys(), $values);
                    $timestamped = false;
                    break;
                default:
                    return null;
            }

            $object->values = $values;
            $object->hashes = $hashes;

            $this->saveObjectViaRepository($object, $timestamped);

            return $field->id;
        });
    }

    private function objectByFieldsAndIdsIterator(\Closure $make, array $fieldIds, ?array $foreignIds = null): int
    {
        $n = 0;
        $fieldsByResources = $this->fields->getByIds($fieldIds)->groupBy('resource');
        foreach ($fieldsByResources as $resource => $fields) {
            foreach ($fields as $field) {
                $query = $this->getRepositoryForField($field)->getSeasonedQuery($field);
                $slug = (string) $field->slug;
                if (! is_null($foreignIds)) {
                    $query->whereIn('id', $foreignIds);
                }
                $query->whereRaw("JSON_CONTAINS_PATH(`values`,'one','$.{$slug}') = true");
                $query->chunk(
                    1000,
                    function ($objects) use ($make, $field, &$n) {
                        foreach ($objects as $object) {
                            $make($field, $object);
                            $n++;
                        }
                    }
                );
            }
        }

        return $n;
    }

    public function getRepositoryForObject(HasValues $object): ?RepositoryWithValues
    {
        if ($this->modelRepositoryMapping[get_class($object)] ?? null) {
            return $this->repositories[$this->formType($object)][$this->modelRepositoryMapping[get_class($object)]] ?? null;
        }

        return null;
    }

    private function formType(HasValues $object): string
    {
        if ($object->form instanceof Form) {
            return $object->form->type;
        }

        return Form::FORM_TYPE_EMPTY;
    }

    public function getRepositoryForField(Field $field): ?Repository
    {
        return $this->repositories[$field->form->type ?? Form::FORM_TYPE_EMPTY][$field->resource] ?? null;
    }

    /**
     * @throws CannotModifyFieldValue
     */
    public function saveObjectViaRepository(HasValues $object, bool $timestamped): void
    {
        $repository = $this->getRepositoryForObject($object);

        if (! ($repository instanceof Repository)) {
            throw new CannotModifyFieldValue;
        }

        $oldTimestamps = $object->timestamps;
        $object->timestamps = $oldTimestamps && $timestamped;
        $repository->save($object);
        $object->timestamps = $oldTimestamps;
    }

    /**
     * retrieve most recent value data from object
     *
     * @throws CannotLoadFieldValue
     */
    private function readObjectFromRepository(HasValues $object): array
    {
        $repository = $this->getRepositoryForObject($object);

        if (! ($repository instanceof Repository)) {
            throw new CannotLoadFieldValue();
        }

        $repository->loadValuesForUpdate($object);

        return [$object->values ?? [], $object->hashes ?? [], $object->protected ?? []];
    }

    /**
     * @param  Collection  $entries
     */
    public function getFieldValuesForObjects(int $fieldId, Collection $objects): Collection
    {
        $fields = new Fields([$this->fields->getById($fieldId)]);

        return $objects->mapWithKeys(
            function (HasValues $object) use ($fields) {
                $field = $this->mapValuesToFields($object, $fields)->first();

                return [$object->id => $field ? $field->value : null];
            }
        )->filter();
    }

    /**
     * Retrieves an object identified by its ID that field refers to
     */
    public function getObjectByFieldAndForeign(int $fieldId, int $field_foreign_id): ?HasValues
    {
        $field = $this->fields->getById($fieldId);
        if ($field === null) {
            return null;
        }

        $object = $this->getRepositoryForField($field)->getById($field_foreign_id);
        if ($object === null) {
            return null;
        }

        if (array_key_exists((string) $field->slug, $object->values)) {
            return $object;
        }

        return null;
    }

    /**
     * update relations for File associated with values and object
     */
    private function updateFilesForValues(Fields $fields, HasValues $entity, ?array $values = [])
    {
        $fields
            ->filter(
                function (Field $field) use ($values) {
                    return $field->isFile() && ($values[(string) $field->slug] ?? false);
                }
            )
            ->each(
                function (Field $field) use ($values, $entity) {
                    $this->files->updateFileRelationsForToken(
                        $this->normaliseValue($field, $values[(string) $field->slug]),
                        $field->id,
                        $entity->id
                    );
                }
            );
    }

    public function loadFieldValues(Collection $objects, Fields $fields): Collection
    {
        if (! $objects->isEmpty() && $repository = $this->getRepositoryForObject($objects->first())) {
            return $repository->loadFieldValuesForObjects($objects->just('id'), $fields->just('slug'));
        }

        return collect();
    }

    public function loadVisibleEntryFieldValues(Collection $entries, Fields $fields): Collection
    {
        $fieldVisibility = $this->fields->requestCache()->visibilityInCategories(array_unique($fields->just('id')), array_unique($entries->just('category_id')));

        $filter = $this->filterVisibleEntryFieldValues($fieldVisibility, $fields);

        if (! $entries->isEmpty() && $repository = $this->getRepositoryForObject($entries->first())) {
            return $repository->setFilter($filter)->loadFieldValuesForObjects($entries->just('id'), $fields->just('slug'), ['entries.category_id']);
        }

        return collect();
    }

    protected function filterVisibleEntryFieldValues($fieldVisibility, $fields)
    {
        return function ($object, $fieldSlug) use ($fieldVisibility) {
            return ! in_array($object->categoryId, $fieldVisibility[$fieldSlug] ?? []);
        };
    }

    protected function purgeOptionValues($slug, $options, $values)
    {
        if (is_array($oldValues = json_decode($rawValue = Arr::get($values, $slug)))) {
            $newValues = [];
            foreach ($oldValues as $value) {
                if (array_search($value, $options) !== false) {
                    $newValues[] = $value;
                }
            }

            return json_encode($newValues);
        }

        return $rawValue;
    }

    public function orderOptionable(Field $field, $values)
    {
        if (! $field->optionable() || ! is_array($values)) {
            return $values;
        }
        $result = [];

        foreach ($field->options->keys() as $option) {
            if (($index = array_search($option, $values)) !== false) {
                $result[] = $values[$index];
            }
        }

        return $result;
    }

    private function raiseModelEvent(HasValues $model)
    {
        return match ($model::class) {
            Membership::class => $model->user ? new UserWasUpdated($model->user) : null,
            Entry::class => new EntryWasUpdated($model),
            default => null,
        };
    }
}
