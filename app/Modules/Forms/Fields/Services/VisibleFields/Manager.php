<?php

namespace AwardForce\Modules\Forms\Fields\Services\VisibleFields;

use AwardForce\Modules\Forms\Fields\Database\DataAccess\ResourceFieldsCollection;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\FieldsResource;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use Illuminate\Support\Collection;

class Manager extends VisibleFieldsWithTabs
{
    /**
     * @return \Illuminate\Support\Collection
     */
    protected function getForUser(Submittable $submittable)
    {
        return $this->userFields($submittable, function (Field $field) {
            return ! $field->isEmpty();
        })->filterFileExists();
    }

    protected function getForSubmittable(Submittable $submittable): Collection
    {
        return $this->formFields($submittable, function (Field $field) {
            return ! $field->isEmpty();
        });
    }

    /**
     * @return ResourceFieldsCollection
     */
    protected function getForContributors(Submittable $submittable)
    {
        return $this->resourceFields($submittable, FieldsResource::Contributors, fn(Field $field) => ! $field->isEmpty(false));
    }

    /**
     * @return ResourceFieldsCollection
     */
    protected function getForAttachments(Submittable $submittable, Collection $attachments, array $attachmentTypes)
    {
        $filter = fn(Field $field) => ! $field->isEmpty();

        return $this->attachmentFields($submittable, $attachments, $filter, []);
    }

    protected function filterCallback()
    {
        return [Field::class, 'tabIsVisibleForManager'];
    }

    protected function getForReferees(Submittable $submittable): ResourceFieldsCollection
    {
        return $this->resourceFields($submittable, FieldsResource::Referees, fn(Field $field) => ! $field->isEmpty(false));
    }

    protected function formFields(Submittable $submittable, callable $filter)
    {
        $fields = $this->fields
            ->requestCache()
            ->with(['conditionalField', 'tab', 'categories'])
            ->form($submittable->getFormId())
            ->resource(FieldsResource::Forms->value)
            ->category($submittable->getCategoryId())
            ->fields(['fields.*'])
            ->tabSort(true, $submittable->getCategoryId())
            ->groupBy('fields.id')
            ->get();

        return $this->conditional->filter($this->values->mapValuesToFields($submittable, $fields))->filter($filter);
    }
}
