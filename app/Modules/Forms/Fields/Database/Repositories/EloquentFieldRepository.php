<?php

namespace AwardForce\Modules\Forms\Fields\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\HardDeletesRepository;
use AwardForce\Library\Database\Eloquent\NonRestrictedRepository;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\FieldsResource;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Behaviours\DeletedForms;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Traits\HasFormBuilder;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Seasons\Repositories\EloquentSeasonalRepository;
use AwardForce\Modules\Seasons\Traits\HasSeasonalBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentFieldRepository extends Repository implements FieldRepository
{
    use DeletedForms;
    use EloquentSeasonalRepository;
    use Exportable;
    use HardDeletesRepository;
    use HasFormBuilder;
    use HasQueryBuilder;
    use HasRequestCache;
    use HasSeasonalBuilder;

    /**
     * EloquentFieldRepository constructor.
     *
     * @param  ValuesService  $valuesService
     * @param  UserRepository  $userRepository
     */
    public function __construct(Field $field)
    {
        $this->model = $field;
    }

    protected function additionalFormConditions($query)
    {
        return $query->orWhere(function ($query) {
            return $query->whereIn('fields.resource', [Field::RESOURCE_USERS, Field::RESOURCE_ORGANISATIONS])->whereNull('fields.form_id');
        });
    }

    /**
     * Retrieves all fields for a given resource.
     *
     * @param  string  $resource
     * @return collection
     */
    public function getByResource($resource)
    {
        $query = $this->resourceQuery($resource)
            ->with('categories')
            ->orderBy('order');

        return $this->seasonQuery($query, $this->activeSeasonId())->get();
    }

    /**
     * Retrieves the collection of required fields for the given resource.
     *
     * @param  string  $resource
     * @return Collection
     */
    public function getResourceRequired($resource)
    {
        $query = $this->resourceQuery($resource)
            ->whereRequired(true);

        return $this->seasonQuery($query, $this->activeSeasonId())->get();
    }

    /**
     * Retrieve all fields for a given resource, that are searchable.
     *
     * @param  string  $resource
     * @param  int  $seasonId
     * @param  array  $excludeIds
     * @return mixed
     */
    public function getSearchable($resource, $seasonId, $excludeIds = [])
    {
        $query = $this->resourceQuery($resource)
            ->whereSearchable(true)
            ->whereNotIn('type', ['content', 'header'])
            ->when($excludeIds, function ($query, $excludeIds) {
                $query->whereNotIn('id', $excludeIds);
            });

        return $this->seasonQuery($query, $seasonId)->get();
    }

    /**
     * Retrieve all searchable fields for a season
     *
     * @param  int  $seasonId
     * @return mixed
     */
    public function getAllSearchable($seasonId)
    {
        $query = $this->getQuery()
            ->whereSearchable(true)
            ->whereNotIn('type', ['content', 'header']);

        return $this->seasonQuery($query, $seasonId)->get();
    }

    /**
     * Return a collection of fields by their slug values.
     *
     * @return mixed
     */
    public function getAllBySlugs(array $slugs)
    {
        return $this->getQuery()
            ->whereIn('slug', $slugs)
            ->with('tab')
            ->get();
    }

    /**
     * @return mixed
     */
    public function getAllQueryString(array $input)
    {
        return $this->getAllBySlugs(array_filter($input, function ($slug) {
            return preg_match('/[[:alpha:]]{8,}/', $slug);
        }));
    }

    /**
     * Constructs a query based on a resource requirement.
     *
     * @param  string  $resource
     * @return mixed
     */
    private function resourceQuery($resource)
    {
        return $this->getQuery()
            ->whereResource($resource);
    }

    /**
     * Returns a collection of fields for the given tab.
     *
     * @return Fields
     */
    public function forTab(Tab $tab)
    {
        return $tab->fields()->whereResource(Field::RESOURCE_FORMS)->with('categories')->orderBy('order')->get();
    }

    /**
     * Returns a collection of fields for the given tab and category.
     *
     * @return Collection
     */
    public function forTabInCategory(Tab $tab, Category $category, array $excludedTypes = [])
    {
        return $this->getQuery()
            ->select('fields.*')
            ->whereTabId($tab->id)
            ->leftJoin('category_field', 'fields.id', '=', 'category_field.field_id')
            ->where(function ($query) use ($category) {
                $query->orWhere('fields.category_option', 'all')
                    ->orWhere('category_field.category_id', $category->id);
            })
            ->whereNotIn('type', $excludedTypes)
            ->groupBy('fields.id')
            ->orderByRaw("FIELD(resource, 'Entries') desc")
            ->orderBy('order', 'asc')
            ->get();
    }

    public function forCategory(Category $category, $resources, $excludedTypes = [])
    {
        return $this->getQuery()
            ->select('fields.*')
            ->leftJoin('category_field', 'fields.id', '=', 'category_field.field_id')
            ->where(function ($query) use ($category) {
                $query->orWhere('fields.category_option', 'all')
                    ->orWhere('category_field.category_id', $category->id);
            })
            ->whereIn('resource', (array) $resources)
            ->whereNotIn('type', $excludedTypes)
            ->whereFormId($category->formId)
            ->with('tab')
            ->groupBy('fields.id')
            ->orderBy('order', 'asc')
            ->get();
    }

    /**
     * Synchronise the field categories with the database.
     *
     * @return mixed
     */
    public function syncCategories(Field $field, array $categoryIds)
    {
        $field->categories()->sync($categoryIds);
    }

    /**
     * Return all fields for the season and resource.
     *
     * @param  int  $seasonId
     * @param  string|array  $resource
     * @return mixed
     */
    public function getAllBySeasonAndResource($seasonId, $resource, ?Form $form = null)
    {
        return $this->seasonResourceQuery($seasonId, $resource)
            ->when($form, function ($query) use ($form) {
                $query->where(fn($query) => $query->where('fields.form_id', $form->id)->orWhere('fields.resource', Field::RESOURCE_USERS));
            })
            ->get();
    }

    /**
     * @return mixed
     */
    public function getAllByFormAndResource(Form $form, $resource)
    {
        $query = $this->getQuery()
            ->where(function ($query) use ($form) {
                $query->where('fields.form_id', $form->id)
                    ->orWhere(function ($query) use ($form) {
                        $query->where('fields.resource', Field::RESOURCE_USERS)->where(function ($query) use ($form) {
                            $query->where('fields.seasonal', false)->orWhere('fields.season_id', $form->seasonId);
                        });
                    });
            });

        $query->leftJoin('tabs', 'tabs.id', '=', 'fields.tab_id')
            ->orderBy('tabs.order')
            ->orderBy('fields.order')
            ->select('fields.*');

        if (is_array($resource)) {
            return $query->whereIn('fields.resource', $resource)->get();
        }

        return $query->where('fields.resource', $resource)->get();
    }

    /**
     * Return all fields for the season and resource, eager load categories including trashed categories.
     *
     * @param  int  $seasonId
     * @param  string|array  $resource
     * @return mixed
     */
    public function getAllBySeasonAndResourceWithTrashedCategories($seasonId, $resource)
    {
        return $this->seasonResourceQuery($seasonId, $resource)
            ->with(['categories' => function ($query) {
                $query->withTrashed();
            }])
            ->get();
    }

    /**
     * Return all fields for the season and resource that can be searchable.
     *
     * @param  int  $seasonId
     * @param  string  $resource
     * @return mixed
     */
    public function getSearchableTypes($seasonId, $resource)
    {
        return $this->seasonResourceQuery($seasonId, $resource)
            ->whereIn('fields.type', config('awardforce.fields.searchable'))
            ->get();
    }

    /**
     * Return all fields for the given form and resource that can be searchable.
     *
     * @param  string  $resource
     * @return Collection
     */
    public function getSearchableTypesForForm(Form $form, $resource)
    {
        return $this->seasonResourceQuery($form->seasonId, $resource)
            ->when($form, function ($query) use ($form) {
                $query->where(fn($query) => $query->where('fields.form_id', $form->id)->orWhere('fields.resource', Field::RESOURCE_USERS));
            })
            ->whereIn('fields.type', config('awardforce.fields.searchable'))
            ->get();
    }

    /**
     * Return fields filtered by season, resource, and visibility.
     *
     * @param  int  $seasonId
     * @param  string|array  $resource
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getVisible($seasonId, $resource)
    {
        $query = $this->getQuery()
            ->with('categories')
            ->leftJoin('tabs', 'tabs.id', '=', 'fields.tab_id')
            ->orderBy('tabs.order', 'asc')
            ->orderBy('fields.order', 'asc')
            ->select('fields.*');

        // Only limit by season when season provided. (if Seasonal filter is all, this will be null)
        if ($seasonId) {
            $query->where('fields.season_id', $seasonId);
        }

        if (is_array($resource)) {
            $query->whereIn('fields.resource', $resource);
        } else {
            $query->where('fields.resource', $resource);
        }

        return $query->get();
    }

    /**
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getVisibleForScoring(Form $form)
    {
        return $this->getVisible($form->seasonId, Field::RESOURCE_FORMS)
            ->where('formId', $form->id)
            ->load('reviewStages');
    }

    /**
     * Return all fields that are for a specific role (applies to users only).
     *
     * @param  int|array  $roleIds
     * @param  int  $seasonId
     * @return Collection
     */
    public function getAllForRole($roleIds, $seasonId = null)
    {
        $all = Field::ROLE_OPTION_ALL;

        $query = $this->getQuery()
            ->with('roles')
            ->select(
                'fields.id',
                'fields.order',
                'fields.slug',
                'fields.type',
                'fields.registration',
                'fields.options',
                'fields.currency',
                'fields.role_option',
                'fields.resource',
                'fields.file_types',
                'fields.max_file_size',
                'fields.min_video_length',
                'fields.max_video_length',
                'fields.image_dimension_constraints',
                'fields.maximum_words',
                'fields.minimum_words',
                'fields.maximum_characters',
                'fields.minimum_characters',
                'fields.autocomplete'
            )
            ->selectRaw("IF(fields.role_option = '{$all}', 1, MAX(field_role.read_access)) as read_access")
            ->selectRaw("IF(fields.role_option = '{$all}', 1, MAX(field_role.write_access)) as write_access")
            ->selectRaw("IF(fields.role_option = '{$all}', fields.required, MAX(field_role.required)) as required")
            ->leftJoin('field_role', 'field_role.field_id', '=', 'fields.id')
            ->where(function ($query) use ($roleIds) {
                $query->where('fields.role_option', Field::ROLE_OPTION_ALL);
                $query->orWhereIn('field_role.role_id', (array) $roleIds);
            })
            ->whereResource(Field::RESOURCE_USERS)
            ->groupBy('fields.id')
            ->orderBy('fields.order');

        if ($seasonId) {
            $query = $this->seasonQuery($query, $seasonId);
        }

        return $query->get();
    }

    /**
     * Require a field by its slug value.
     *
     * @param  string  $slug
     * @return Field
     */
    public function requireBySlug($slug)
    {
        $field = parent::requireBy('slug', $slug);
        $field->load('categories');
        $field->load('season');

        return $field;
    }

    /**
     * Return a collection of required fields for the resource that are missing for a given
     * related object. This provides a mechanism to check to see if required field values are missing.
     *
     * @param  string  $resource
     * @param  int  $foreignId
     * @param  int  $seasonId
     * @return mixed
     */
    public function getMissingRequiredUserFieldsForSeason($foreignId, $seasonId, array $roleIds = [])
    {
        $fields = $this->missingFieldsQuery($foreignId, $seasonId, $roleIds)
            ->get();

        return $fields->where('required', true);
    }

    /**
     * Return a collection of fields that the given related object is missing.
     *
     * @param  string  $resource
     * @param  int  $foreignId
     * @param  int  $seasonId
     * @return mixed
     */
    public function getMissingUserFieldsForSeason($foreignId, $seasonId, array $roleIds = [])
    {
        return $this->missingFieldsQuery($foreignId, $seasonId, $roleIds)->get();
    }

    /**
     * Base query to obtain missing fields from a given related object.
     *
     * @return mixed
     */
    private function missingFieldsQuery($foreignId, $seasonId, array $roleIds = [])
    {
        $all = Field::ROLE_OPTION_ALL;

        return $this->getQuery()
            ->select('fields.*')
            ->selectRaw("IF(fields.role_option = '{$all}', 1, MAX(field_role.write_access)) as write_access")
            ->selectRaw("IF(fields.role_option = '{$all}', fields.required, MAX(field_role.required)) as required")
            ->leftJoin('field_role', 'field_role.field_id', '=', 'fields.id')
            ->where('resource', '=', Field::RESOURCE_USERS)
            ->where(function ($query) use ($seasonId) {
                return $this->seasonQuery($query, $seasonId);
            })->where('fields.type', '!=', 'content')
            ->where(function ($query) use ($roleIds) {
                $query->where('fields.role_option', Field::ROLE_OPTION_ALL);
                $query->orWhereIn('field_role.role_id', $roleIds);
            })
            ->orderBy('fields.order')
            ->groupBy('fields.id');
    }

    /**
     * Returns the fields for the specified category.
     *
     * @param  int  $categoryId
     * @return Collection
     */
    public function getFromCategory($categoryId)
    {
        return $this->getQuery()->whereHas('categories', function ($query) use ($categoryId) {
            $query->whereCategoryId($categoryId);
        })->get();
    }

    /**
     * Retrieves all fields for a given resource,
     * that are not of a specified type.
     *
     * @param  string  $resource
     * @param  string|array  $type
     * @param  null  $seasonId
     * @return Collection
     */
    public function getByResourceWhereNot($resource, $type, $seasonId = null)
    {
        return $this->byResourceWhereNot($resource, $type, $seasonId)
            ->get();
    }

    /**
     * @param  string|string[]  $type
     */
    public function getByResourceWhereFieldTypeNotAndFormTypeIsNotReport(string $resource, string|array $type, ?int $seasonId = null): Collection
    {
        return $this->byResourceWhereNot($resource, $type, $seasonId)
            ->when(
                $resource === Field::RESOURCE_FORMS,
                fn(Builder $query) => $query
                    ->leftJoin('forms', 'forms.id', 'fields.form_id')
                    ->whereNot('forms.type', Form::FORM_TYPE_REPORT)
            )
            ->get();
    }

    public function getForEntriesByResourceWhereNot($resource, $type, $seasonId = null)
    {
        return $this->byResourceWhereNot($resource, $type, $seasonId)
            ->join('forms', 'forms.id', 'fields.form_id')
            ->where('forms.type', Form::FORM_TYPE_ENTRY)
            ->get();
    }

    public function getForGrantReportsByResourceWhereNot(string $resource, array|string $type, ?int $seasonId = null): Collection
    {
        return $this->byResourceWhereNot($resource, $type, $seasonId)
            ->join('forms', 'forms.id', 'fields.form_id')
            ->where('forms.type', Form::FORM_TYPE_REPORT)
            ->get();
    }

    public function getUserFieldsByResourceWhereNot($type, $seasonId = null)
    {
        return $this->byResourceWhereNot(Field::RESOURCE_USERS, $type, $seasonId)->get();
    }

    /**
     * Retrieves all fields for a given ids and resource,
     * that are not of a specified type.
     */
    public function getByIdsAndResourceWhereNot(int|array $ids, string $resource, array|string $type, ?int $seasonId = null): Collection
    {
        return $this->byResourceWhereNot($resource, $type, $seasonId)
            ->whereIn('fields.id', (array) $ids)
            ->get();
    }

    /**
     * Retrieves all fields for a given formId and resource,
     * that are not of a specified type.
     */
    public function getByFormIdAndResourceWhereNot(int $formId, string $resource, array|string $type): Collection
    {
        return $this->byResourceWhereNot($resource, $type)
            ->with('categories')
            ->where('fields.form_id', $formId)
            ->get();
    }

    private function byResourceWhereNot($resource, $type, $seasonId = null)
    {
        $query = $this->getQuery()
            ->leftJoin('tabs', 'tabs.id', '=', 'fields.tab_id')
            ->orderBy('tabs.order', 'asc')
            ->orderBy('fields.order', 'asc')
            ->where('fields.resource', '=', $resource);

        if (is_array($type)) {
            $query->whereNotIn('fields.type', $type);
        } else {
            $query->where('fields.type', '!=', $type);
        }

        if (! is_null($seasonId)) {
            $query = $this->seasonQuery($query, $seasonId);
        }

        return $query->select('fields.*');
    }

    /**
     * Returns all fields for the season, limited by category for an submittable
     *
     *
     * @return mixed
     */
    public function getForSubmittable(int $formId, ?int $categoryId)
    {
        $query = $this->forSubmittableQuery($formId, $categoryId);

        return $this->tabSorted($query, true, $categoryId)->get();
    }

    /**
     * Returns all fields (with entrant write access) for the form, limited by category for a submittable
     */
    public function getWritableForEntrant(int $formId, ?int $categoryId): array
    {
        $query = $this->forSubmittableQuery($formId, $categoryId);
        $query = $this->tabSorted($query);

        $query->where(function (Builder $query) {
            $query->where('fields.entrant_write_access', '=', 1)
                ->orWhere('fields.type', '=', 'formula');
        });

        return $query
            ->select('fields.slug')
            ->pluck('fields.slug')
            ->toArray();
    }

    /**
     * Returns the fields for the specified season.
     *
     * @param  int  $seasonId
     * @return Collection
     */
    public function getForSeasonCopy($seasonId)
    {
        return $this->getQuery()->whereSeasonId($seasonId)->whereSeasonal(true)->get();
    }

    /**
     * Returns array of field ids for the specified season.
     *
     * @param  int  $seasonId
     * @return array
     */
    public function getIdsForSeason($seasonId)
    {
        return $this->seasonQuery($this->getQuery(), $seasonId)->pluck('id')->all();
    }

    /**
     * Return any contactable fields for the resource (email or phone).
     *
     * @param  string  $resource
     * @param  int  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getContactable($resource, int $formId, $seasonId = null)
    {
        $query = $this->resourceQuery($resource)
            ->where('fields.form_id', $formId)
            ->whereIn('type', ['email', 'phone']);

        if ($seasonId) {
            $query = $this->seasonQuery($query, $seasonId);
        }

        return $query->get();
    }

    public function getContactableInForm(int $formId)
    {
        return $this->resourceQuery(Field::RESOURCE_FORMS)
            ->whereIn('type', ['email', 'phone'])
            ->where('form_id', $formId)
            ->get();
    }

    /**
     * Return any Text fields for the resource.
     *
     * @param  string  $resource
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getTextFields($resource, ?int $seasonId = null)
    {
        $query = $this->resourceQuery($resource)
            ->where('type', 'text');

        if ($seasonId) {
            $query = $this->seasonQuery($query, $seasonId);
        }

        return $query->get();
    }

    public function getTextFieldsByForm($resource, int $formId)
    {
        $query = $this->resourceQuery($resource)
            ->where('type', 'text')
            ->where(function ($query) use ($formId) {
                // User fields use form_id = null
                $query->orWhere('fields.form_id', $formId)
                    ->orWhereNull('form_id');
            });

        return $query->get();
    }

    /**
     * Return given type fields for the resource.
     *
     * @param  string  $resource
     * @param  null  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getFieldsOfType($resource, string $type, $seasonId = null)
    {
        $query = $this->resourceQuery($resource)
            ->where('type', $type);

        if ($seasonId) {
            $query = $this->seasonQuery($query, $seasonId);
        }

        return $query->get();
    }

    /**
     * Return a collection of file fields ordered by order
     *
     * @param  int  $formId
     * @return \Illuminate\Support\Collection
     */
    public function getFileFields($formId)
    {
        $query = $this->getQuery()
            ->select('fields.*')
            ->where('fields.resource', 'Entries')
            ->where('fields.type', 'file')
            ->where('fields.form_id', $formId);

        return $this->tabSorted($query)->with('tab.categories')->get();
    }

    /**
     * Return a collection of all active fields for a season, excluding the
     * the field specified in the $exclude param for use with conditional fields.
     *
     * @param  int  $seasonId
     * @param  null|int  $exclude
     * @return Collection
     */
    public function getFieldsForConditionalList($seasonId, $exclude = null)
    {
        $query = $this->getQuery()
            ->whereIn('type', config('awardforce.fields.conditional_fields'))
            ->whereNull('deleted_at');

        $query = $this->seasonQuery($query, $seasonId);

        if (! is_null($exclude)) {
            $query->where('id', '!=', $exclude);
        }

        return $query->get();
    }

    /**
     * Return a collection of all active fields for a form, excluding the
     * the field specified in the $exclude param for use with conditional fields.
     *
     * @param  int  $formId
     * @param  null|int  $exclude
     * @return Collection
     */
    public function getFormFieldsForConditionalList($formId, $exclude = null)
    {
        $query = $this->getQuery()
            ->whereFormId($formId)
            ->whereIn('type', config('awardforce.fields.conditional_fields'))
            ->whereNull('deleted_at');

        if (! is_null($exclude)) {
            $query->where('id', '!=', $exclude);
        }

        return $query->get();
    }

    /**
     * Return all fields for the season and resource with conditional field joined
     * for validation.
     *
     * @param  int  $seasonId
     * @param  string  $resource
     * @param  int  $formId
     * @return mixed
     */
    public function getForValidation($seasonId, $resource, $formId = null)
    {
        return $this->getQuery()
            ->with(['categories', 'conditionalField'])
            ->when($formId, function ($query) use ($formId) {
                $query->where('form_id', $formId);
            }, function ($query) use ($seasonId) {
                $query->where('season_id', $seasonId);
            })
            ->when(is_array($resource), function ($query) use ($resource) {
                $query->whereIn('resource', $resource);
            }, function ($query) use ($resource) {
                $query->where('resource', '=', $resource);
            })
            ->where(function ($query) {
                $resources = [
                    Field::RESOURCE_FORMS,
                    Field::RESOURCE_CONTRIBUTORS,
                    Field::RESOURCE_ATTACHMENTS,
                    Field::RESOURCE_REFEREES,
                ];
                $query->where(function ($query) use ($resources) {
                    $query->whereIn('resource', $resources)
                        ->has('tab');
                })
                    ->orWhereNotIn('resource', $resources);
            })
            ->orderBy('id')
            ->get();
    }

    /**
     * Return all conditional fields for the season and resource.
     *
     * @param  int  $seasonId
     * @param  string  $resource
     * @return mixed
     */
    public function getConditionalForSeasonAndResource($seasonId, $resource)
    {
        $query = $this->getQuery()
            ->with(['categories', 'conditionalField'])
            ->whereConditional(true);

        $query = $this->seasonQuery($query, $seasonId);

        if (is_array($resource)) {
            $query->whereIn('resource', $resource);
        } else {
            $query->where('resource', '=', $resource);
        }

        return $query->get();
    }

    /**
     * Get all writable fields for a specific review stage
     *
     * @param  int  $reviewStageId
     * @return mixed
     */
    public function getReviewStageWritable($reviewStageId)
    {
        return $this->getQuery()
            ->leftJoin('field_review_stage', 'field_review_stage.field_id', '=', 'fields.id')
            ->where('field_review_stage.review_stage_id', '=', $reviewStageId)
            ->where('field_review_stage.write', '=', 1)
            ->get();
    }

    /**
     * @return mixed
     */
    protected function forSubmittableQuery(int $formId, ?int $categoryId)
    {
        return $this->getQuery()
            ->with('conditionalField', 'categories', 'sets')
            ->where('fields.form_id', '=', $formId)
            ->leftJoin('category_field', 'category_field.field_id', '=', 'fields.id')
            ->where(function ($query) use ($categoryId) {
                $query->orWhere('fields.category_option', 'all')
                    ->when($categoryId, function ($query) use ($categoryId) {
                        $query->orWhere('category_field.category_id', $categoryId);
                    });
            })
            ->select('fields.*')
            ->distinct('fields.id')
            ->where('fields.resource', '=', Field::RESOURCE_FORMS);
    }

    /**
     * Join tabs and order fields. Optionally, exclude fields attached to deleted tabs.
     *
     * @param  Builder  $query
     * @return Builder
     */
    protected function tabSorted($query, bool $notNull = false, ?int $categoryId = 0)
    {
        return $query->when($notNull, function ($query) {
            $query->join('tabs', 'tabs.id', '=', 'fields.tab_id')
                ->whereNull('tabs.deleted_at');
        }, function ($query) {
            $query->leftJoin('tabs', 'tabs.id', '=', 'fields.tab_id');
        })
            ->when(
                $categoryId,
                function ($query) use ($categoryId) {
                    $query->leftJoin('category_tab', 'tabs.id', '=', 'category_tab.tab_id');
                    $query->where(
                        function ($query) use ($categoryId) {
                            $query->where('category_tab.category_id', $categoryId);
                            $query->orWhere('tabs.category_option', 'all');
                            $query->orWhereNull('tabs.id');
                            $query->orWhereNull('category_tab.category_id');
                        }
                    );
                }
            )
            ->with('tab')
            ->orderBy('tabs.order')
            ->orderBy('fields.order');
    }

    /**
     * Get attachment fields of type radio and select, which can be used as attachment type fields, filtered by season.
     *
     * @param  int  $seasonId
     * @return Collection
     */
    public function getForAttachmentTypeField($seasonId)
    {
        return $this->attachmentTypeFieldQuery($seasonId)->get();
    }

    /**
     * Get attachment fields of type radio and select, which can be used as attachment type fields, for a particular form.
     *
     * @return Collection
     */
    public function getForAttachmentTypeFieldAndForm(Form $form)
    {
        return $this->attachmentTypeFieldQuery($form->seasonId)
            ->where('fields.form_id', $form->id)
            ->get();
    }

    /**
     * @param  int  $seasonId
     * @return Builder
     */
    private function attachmentTypeFieldQuery($seasonId)
    {
        $query = $this->getQuery()
            ->whereResource(Field::RESOURCE_ATTACHMENTS)
            ->whereProtection(Field::PROTECTION_STANDARD)
            ->whereIn('type', ['radio', 'drop-down-list']);

        return $this->seasonQuery($query, $seasonId);
    }

    /**
     * @param  int|null  $seasonId
     * @param  string|array  $resource
     * @return \Illuminate\Database\Eloquent\Builder
     */
    private function seasonResourceQuery($seasonId, $resource)
    {
        $query = $this->getQuery()
            ->with('categories', 'sets', 'tab')
            ->leftJoin('tabs', 'tabs.id', '=', 'fields.tab_id')
            ->orderBy('tabs.order', 'asc')
            ->orderBy('fields.order', 'asc')
            ->select('fields.*');

        // Only limit by season when season provided. (if Seasonal filter is all, this will be null)
        if ($seasonId) {
            $query = $this->seasonQuery($query, $seasonId);
        }

        if (is_array($resource)) {
            return $query->whereIn('fields.resource', $resource);
        }

        return $query->where('fields.resource', $resource);
    }

    private function seasonQuery($query, $seasonId)
    {
        return $query->where(function ($query) use ($seasonId) {
            return $query->where(function ($query) use ($seasonId) {
                return $query->where('fields.resource', '=', Field::RESOURCE_USERS)->where(function ($query) use ($seasonId) {
                    return $query->where('fields.season_id', $seasonId)->orWhere('fields.seasonal', false);
                });
            })->orWhere(function ($query) use ($seasonId) {
                return $query->where('fields.resource', '<>', Field::RESOURCE_USERS)->where('fields.season_id', $seasonId);
            });
        });
    }

    public function getAllFieldIdsForResource($resource)
    {
        return $this->getQuery()
            ->withTrashed()
            ->whereResource($resource)
            ->pluck('id')
            ->toArray();
    }

    /**
     * Returns maximum value of given field (as in SQL), filtered by resource and season.
     *
     * @return mixed
     */
    public function getMaxValue(string $fieldName, ?string $resource = null, ?int $seasonId = null)
    {
        $query = $this->getQuery()
            ->when($resource, function ($query, $resource) {
                return $query->whereResource($resource);
            });

        if ($seasonId) {
            $query = $this->seasonQuery($query, $seasonId);
        }

        return $query->max($fieldName);
    }

    /**
     * increments usage count for fields
     */
    public function setFieldsHaveValues(?array $ids = []): void
    {
        if (empty($ids)) {
            return;
        }

        $withoutValues = $this->getQuery()
            ->whereIn('id', $ids)
            ->where('has_values', '=', false)
            ->pluck('id')
            ->all();

        if (empty($withoutValues)) {
            return;
        }

        $this->getQuery()
            ->whereIn('id', $withoutValues)
            ->update(['has_values' => true, 'updated_at' => \DB::raw('updated_at')]);
    }

    /**
     * Compute the categories where the given fields are visible.
     *
     * @return mixed
     */
    public function visibilityInCategories(array $fieldIds, array $categoryIds)
    {
        $fieldIds = array_unique($fieldIds);
        $ids = implode(',', array_unique($categoryIds));

        return $this->getQuery()
            ->select([
                'fields.slug',
                DB::raw("IF(fields.category_option = 'all', '{$ids}', GROUP_CONCAT(field_categories.id)) as visible_in_category"),
                DB::raw("IF(tabs.category_option = 'all', '{$ids}', GROUP_CONCAT(tab_categories.id)) as visible_in_tab"),
            ])
            ->leftJoin('category_field', 'category_field.field_id', '=', 'fields.id')
            ->leftJoin('category_tab', 'category_tab.tab_id', '=', 'fields.tab_id')
            ->leftJoin('categories as field_categories', 'field_categories.id', '=', 'category_field.category_id')
            ->leftJoin('categories as tab_categories', 'tab_categories.id', '=', 'category_tab.category_id')
            ->leftJoin('tabs', 'tabs.id', '=', 'fields.tab_id')
            ->where('fields.resource', '=', Field::RESOURCE_FORMS)
            ->whereIn('fields.id', $fieldIds)
            ->where(function ($query) use ($categoryIds) {
                $query->where('fields.category_option', 'all')
                    ->orWhereIn('field_categories.id', $categoryIds);
            })
            ->where(function ($query) use ($categoryIds) {
                $query->where('tabs.category_option', 'all')
                    ->orWhereIn('tab_categories.id', $categoryIds);
            })
            ->groupBy('fields.id')
            ->get()
            ->mapWithKeys(function (Field $field) {
                return [(string) $field->slug => array_values(array_filter(
                    array_intersect(
                        explode(',', $field->visibleInCategory),
                        explode(',', $field->visibleInTab)
                    )
                ))];
            })
            ->all();
    }

    /**
     * @param  int  $fieldId
     * @param  int  $tabId
     */
    public function attachFieldToTab($fieldId, $tabId): void
    {
        if ($field = Field::where('id', $fieldId)->first()) {
            $field->tabId = $tabId;
            $field->save();
        }
    }

    public function shiftStartingFrom(?int $order): void
    {
        if ($this->getQuery()->where('order', '=', (int) $order)->withTrashed()->first()) {
            $this->getQuery()->where('order', '>=', (int) $order)->withTrashed()->update(['order' => DB::raw('`order`+1')]);
        }
    }

    /**
     * {@inheritDoc}
     */
    public function getOptionable(int $formId, ?string $resource = null, ?int $seasonId = null)
    {
        return $this->getQuery()
            ->where('fields.form_id', $formId)
            ->when($resource, function ($query, $resource) {
                return $query->whereResource($resource);
            })
            ->when($seasonId, function ($query, $seasonId) {
                return $query->where('fields.season_id', '=', $seasonId);
            })
            ->whereIn('type', ['radio', 'drop-down-list', 'checkboxlist'])
            ->get();
    }

    /**
     * {@inheritDoc}
     */
    public function countFieldsOfType(string $resource, string $type, $seasonId = null): int
    {
        return $this->resourceQuery($resource)
            ->where('type', $type)
            ->when($seasonId, function (&$query) use ($seasonId) {
                $query = $this->seasonQuery($query, $seasonId);
            })
            ->count();
    }

    public function getAllBySlugsForTypes(array $slugs, $types = [])
    {
        return $this->getQuery()
            ->whereIn('slug', $slugs)
            ->when(! empty($types), fn($query) => $query->whereIn('type', $types))
            ->get();
    }

    public function getAllScoreableWithScores(int $formId, $seasonId = null): \Platform\Database\Eloquent\Collection
    {
        return $this->scoreableQuery($formId, $seasonId)
            ->where('auto_scoring', true)
            ->get();
    }

    public function getAllScoreableWithTitleTranslatedByForm($formId, $categoryId = null): \Platform\Database\Eloquent\Collection
    {
        $query = $this->scoreableQuery($formId)
            ->leftJoin('tabs', 'tabs.id', 'fields.tab_id')
            ->where('auto_scoring', true)
            ->where('fields.form_id', $formId)
            ->when($categoryId, function ($query) use ($categoryId) {
                $query->leftJoin('category_field', 'category_field.field_id', '=', 'fields.id')
                    ->where(function ($query) use ($categoryId) {
                        $query->orWhere('fields.category_option', 'all')
                            ->orWhere('category_field.category_id', $categoryId);
                    })
                    ->leftJoin('category_tab', 'category_tab.tab_id', '=', 'fields.tab_id')
                    ->where(function ($query) use ($categoryId) {
                        $query->orWhere('tabs.category_option', 'all')
                            ->orWhere('category_tab.category_id', $categoryId);
                    });
            })
            ->orderBy('tabs.order')
            ->orderBy('fields.order')
            ->groupBy('fields.id')
            ->selectRaw('fields.id as id, fields.slug as slug, fields.type as type, fields.options as options');

        $this->joinTranslations($query, 'title');

        return $query->get();
    }

    public function countAllScoreableWithScores(?int $formId = null, ?int $seasonId = null): int
    {
        return $this->scoreableQuery($formId, $seasonId)
            ->where('auto_scoring', true)
            ->count();
    }

    private function scoreableQuery(?int $formId = null, ?int $seasonId = null)
    {
        return $this->getQuery()
            ->whereIn('fields.type', Field::SCOREABLE_TYPES)
            ->when($seasonId, function (&$query) use ($seasonId) {
                $query = $this->seasonQuery($query, $seasonId);
            })
            ->when($formId, function ($query) use ($formId) {
                $query->where('fields.form_id', $formId);
            });
    }

    public function getAllBySlugsWithTitleTranslated($slugs)
    {
        $query = $this->getQuery()->whereIn('fields.slug', $slugs)
            ->selectRaw('fields.id as id, fields.slug as slug');

        $this->joinTranslations($query, 'title');

        return $query->get();
    }

    private function joinTranslations(&$query, $field)
    {
        $preferredAlias = 'preferred';
        $fallbackAlias = 'fallback';

        $this->performTranslationJoin($query, \Consumer::languageCode(), $preferredAlias, $field);

        if (\Consumer::languageCode() !== default_language_code()) {
            $query->selectRaw("coalesce(NULLIF($preferredAlias.value, ''), $fallbackAlias.value) as $field");
            $this->performTranslationJoin($query, default_language_code(), $fallbackAlias, $field);
        } else {
            $query->selectRaw("$preferredAlias.value as $field");
        }
    }

    private function performTranslationJoin(&$query, string $languageCode, string $alias, string $field)
    {
        $query->leftJoin(DB::raw("translations $alias"), function ($join) use ($languageCode, $alias, $field) {
            $join->on("$alias.foreign_id", '=', 'fields.id')
                ->on("$alias.account_id", '=', 'fields.account_id');
            $join
                ->where("$alias.field", $field)
                ->where("$alias.resource", 'Field')
                ->where("$alias.language", $languageCode);
        });
    }

    public function getBySlugWithTrashed(string $slug)
    {
        return $this->getQuery()
            ->where('fields.slug', $slug)
            ->withTrashed()
            ->first();
    }

    public function getSearchableFields(array $context, int $seasonId)
    {
        if ($fieldIds = array_get($context, 'fieldIds')) {
            return $this->getByIds($fieldIds);
        }

        return $this->getAllSearchable($seasonId);
    }

    public function getAllFormulaCompatible()
    {
        return $this->getQuery()
            ->whereIn('fields.type', config('awardforce.fields.formula_field_compatible'))
            ->get();
    }

    public function userConfigurationExport(?int $seasonId = null)
    {
        return $this->getQuery()
            ->forConfiguration()
            ->where('fields.resource', Field::RESOURCE_USERS)
            ->where(function ($query) use ($seasonId) {
                $query->when($seasonId, function ($query, $seasonId) {
                    $query->where('fields.seasonal', 1)
                        ->where('fields.season_id', $seasonId);
                }, fn($q) => $q->where('fields.seasonal', false));
            })
            ->get();
    }

    public function configurationExport(?int $seasonId = null, ?int $formId = null)
    {
        return $this->getQuery()
            ->forConfiguration()
            ->where('fields.resource', '!=', Field::RESOURCE_USERS)
            ->where('fields.season_id', $seasonId)
            ->where('fields.form_id', $formId)
            ->get();
    }

    public function permanentlyDeleteFormFields(int $seasonId): void
    {
        throw_if(! $this->isSafe(), new NonRestrictedRepository);

        parent::getQuery()
            ->where('resource', '!=', Field::RESOURCE_USERS)
            ->where('season_id', $seasonId)
            ->forceDelete();
    }

    public function permanentlyDeleteUserFields(?int $seasonId): void
    {
        throw_if(! $this->isSafe(), new NonRestrictedRepository);

        parent::getQuery()
            ->where('resource', Field::RESOURCE_USERS)
            ->when($seasonId,
                fn($query, $seasonId) => $query->where('season_id', $seasonId)->where('seasonal', true),
                fn($query) => $query->where('seasonal', false)
            )
            ->forceDelete();
    }

    public function getSeasonalUserFields(int $seasonId): Collection
    {
        return $this->getQuery()
            ->where('resource', Field::RESOURCE_USERS)
            ->where('season_id', $seasonId)
            ->where('seasonal', true)
            ->get();
    }

    public function getNonSeasonalUserFields(): Collection
    {
        return $this->getQuery()
            ->where('resource', Field::RESOURCE_USERS)
            ->where('seasonal', false)
            ->get();
    }

    public function slugs(array $slugs): self
    {
        $this->query()->whereIn('slug', $slugs);

        return $this;
    }

    public function type(string $type): self
    {
        $this->query()->where('fields.type', $type);

        return $this;
    }

    public function hasTrigger(string $trigger): self
    {
        $this->query()->whereJsonContains('fields.configuration->triggers', $trigger);

        return $this;
    }

    public function forEligibilityTab(Tab $tab): self
    {
        $this->query()
            ->whereExists(function ($query) use ($tab) {
                $query->select(DB::raw(1))
                    ->from('tabs')
                    ->whereColumn('tabs.id', 'fields.tab_id')
                    ->where('tabs.visible_to_entrants', true)
                    ->where('tabs.order', '<=', $tab->order);
            });

        return $this;
    }

    public function scoreAble(bool $enabledOnly = true): self
    {
        $this->query()
            ->whereIn('fields.type', Field::SCOREABLE_TYPES)
            ->when($enabledOnly, fn($query) => $query->where('auto_scoring', true));

        return $this;
    }

    public function resource(array|string $resource): self
    {
        $this->query()->whereIn('fields.resource', (array) $resource);

        return $this;
    }

    /**
     * Return fields for a given resource filtered by form and category
     */
    public function forResourceFormAndCategory(int $formId, ?int $categoryId, FieldsResource $resource): Collection
    {
        $this->query()
            ->with('conditionalField', 'categories', 'sets')
            ->leftJoin('category_field', 'category_field.field_id', 'fields.id')
            ->where(function ($query) use ($categoryId) {
                $query->where('fields.category_option', 'all')
                    ->when($categoryId, function ($query) use ($categoryId) {
                        $query->orWhere('category_field.category_id', $categoryId);
                    });
            })
            ->select('fields.*')
            ->distinct('fields.id');
        $this->form($formId);
        $this->resource($resource->value);

        $this->tabSorted($this->query(), true, $categoryId);

        return $this->get();
    }

    public function category(int $categoryId): self
    {
        $this->query()
            ->leftJoin('category_field', 'category_field.field_id', 'fields.id')
            ->where(function ($query) use ($categoryId) {
                $query->where('fields.category_option', 'all')
                    ->when($categoryId, function ($query) use ($categoryId) {
                        $query->orWhere('category_field.category_id', $categoryId);
                    });
            });

        return $this;
    }

    public function tabSort(bool $notNull = false, ?int $categoryId = 0): self
    {
        $this->query()
            ->when($notNull, function ($query) {
                $query->join('tabs', 'tabs.id', '=', 'fields.tab_id')
                    ->whereNull('tabs.deleted_at');
            }, function ($query) {
                $query->leftJoin('tabs', 'tabs.id', '=', 'fields.tab_id');
            })
            ->when(
                $categoryId,
                function ($query) use ($categoryId) {
                    $query->leftJoin('category_tab', 'tabs.id', '=', 'category_tab.tab_id');
                    $query->where(
                        function ($query) use ($categoryId) {
                            $query->where('category_tab.category_id', $categoryId);
                            $query->orWhere('tabs.category_option', 'all');
                            $query->orWhereNull('tabs.id');
                            $query->orWhereNull('category_tab.category_id');
                        }
                    );
                }
            )
            ->orderBy('tabs.order')
            ->orderBy('fields.order');

        return $this;
    }

    public function scoreSets(array $scoreSetIds): self
    {
        $this->query()
            ->join('field_score_set', 'field_score_set.field_id', '=', 'fields.id')
            ->whereIn('field_score_set.score_set_id', $scoreSetIds)
            ->groupBy('fields.id');

        $this->fields(['group_concat(field_score_set.score_set_id) as score_set_ids']);

        return $this;
    }
}
