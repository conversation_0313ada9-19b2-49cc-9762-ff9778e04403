<?php

namespace AwardForce\Modules\Forms\Fields\Database\Entities;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Identity\Roles\Models\Role;
use Tests\IntegratedTestCase;

final class FieldIntegratedTest extends IntegratedTestCase
{
    /** @var ValuesService */
    private $valuesService;

    public function init()
    {
        Field::unguard();
        $this->valuesService = app(ValuesService::class);
    }

    public function testIsNotRequired(): void
    {
        $field = new Field;

        $this->assertFalse($field->isRequired());
    }

    public function testHasValuesReturnsFalseWithNoValues(): void
    {
        $field = $this->muffin(Field::class);

        $this->assertFalse($field->hasValues);
    }

    public function testHasValuesReturnsFalseWithEmptyValues(): void
    {
        $entries = $this->muffins(2, Entry::class);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);

        $this->valuesService->setValuesForObject([
            (string) $field->slug => '',
        ], $entries[0]);

        $this->valuesService->setValuesForObject([
            (string) $field->slug => null,
        ], $entries[1]);

        $field->refresh();

        $this->assertFalse($field->hasValues);
    }

    public function testHasValuesReturnsTrueWithValues(): void
    {
        $entry = $this->muffin(Entry::class);
        $field = $this->muffin(Field::class);

        $this->valuesService->setValuesForObject([
            (string) $field->slug => '*',
        ], $entry);

        $field->refresh();

        $this->assertTrue($field->hasValues);
    }

    public function testSyncRoleAccessSettings(): void
    {
        $role = $this->muffin(Role::class);
        $field = $this->muffin(Field::class);

        $settings = [$role->id => ['read_access' => 1]];

        $field->syncRoleAccessSettings($settings);

        $this->assertEquals(1, $field->roles->count());

        $this->assertEquals(1, $field->roles->first()->pivot->read_access);
        $this->assertEquals(0, $field->roles->first()->pivot->write_access);
        $this->assertEquals(0, $field->roles->first()->pivot->required);
    }

    public function testRemovalOfSpecialCharactersInOptions(): void
    {
        $field = $this->muffin(Field::class, [
            'type' => 'drop-down-list',
            'options' => '{"val 1":0,"val​2​":0,"\\val \\3":0,"val 4":0}',
        ]);

        $field->saveTranslation('en_GB', 'optionText', json_encode(
            ['val 1' => 'Text 1', 'val​2​' => 'Text 2', '\\val 3' => 'Text 3', 'val 4' => 'Text 4']
        ), $field->accountId);
        $options = $field->options->toArray();

        $this->assertEquals('Text 1', $options['val 1']->text());
        $this->assertEquals('Text 2', $options['val 2']->text());
        $this->assertEquals('Text 3', $options['val 3']->text());
        $this->assertEquals('Text 4', $options['val 4']->text());
    }
}
