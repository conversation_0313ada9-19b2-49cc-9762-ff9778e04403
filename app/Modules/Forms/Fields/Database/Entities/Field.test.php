<?php

namespace AwardForce\Modules\Forms\Fields\Database\Entities;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Forms\Fields\Configurations\Simple;
use AwardForce\Modules\Forms\Fields\Configurations\Table;
use AwardForce\Modules\Forms\Fields\Events\FieldProtectionWasChanged;
use AwardForce\Modules\Forms\Fields\Events\FieldScoreableWasChanged;
use AwardForce\Modules\Forms\Fields\Events\FieldSearchableWasChanged;
use AwardForce\Modules\Forms\Fields\Events\FieldUpdateAffectingValues;
use AwardForce\Modules\Forms\Fields\Events\FieldWasAdded;
use AwardForce\Modules\Forms\Fields\Events\FieldWasUpdated;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\Seasons\Models\Season;
use Platform\Database\Eloquent\Collection;
use Platform\Language\Language;
use Tests\IntegratedTestCase;
use Tests\MockConsumer;

final class FieldTest extends IntegratedTestCase
{
    public function init()
    {
        Field::unguard('type');
    }

    public function testIsEmptyContentField(): void
    {
        $field = new Field;
        $field->type = 'content';
        $field->value = null;

        $this->assertFalse($field->isEmpty());
    }

    public function testIsEmptyNull(): void
    {
        $field = new Field;
        $field->type = 'text';
        $field->value = null;

        $this->assertTrue($field->isEmpty());
    }

    public function testIsEmptyString(): void
    {
        $field = new Field;
        $field->type = 'text';
        $field->value = 'abc';

        $this->assertFalse($field->isEmpty());

        $field->value = null;

        $this->assertTrue($field->isEmpty());
    }

    public function testIsEmptyArray(): void
    {
        $field = new Field;
        $field->type = 'text';
        $field->value = ['abc'];

        $this->assertFalse($field->isEmpty());

        $field->value = [];

        $this->assertTrue($field->isEmpty());
    }

    public function testIsEmptyTable(): void
    {
        $field = new Field;
        $field->type = 'table';

        $this->assertTrue($field->isEmpty());

        $field->value = [];
        $this->assertTrue($field->isEmpty());

        $field->value = ['values' => []];
        $this->assertTrue($field->isEmpty());

        $field->value = ['values' => ['abc']];
        $this->assertFalse($field->isEmpty());
    }

    public function testHasHintText(): void
    {
        $field = new Field;
        $field->type = 'text';
        $field->addTranslation('en_GB', 'hintText', 'abc');

        $this->assertTrue($field->hasHintText());
    }

    public function testHasHintTextContentField(): void
    {
        $field = new Field;
        $field->type = 'content';
        $field->addTranslation('en_GB', 'hintText', 'abc');

        $this->assertFalse($field->hasHintText());
    }

    public function testHasHintCanDetectFallbackLang(): void
    {
        $field = new Field;
        $field->type = 'text';
        $field->addTranslation('en_GB', 'hintText', 'English hint');

        Consumer::user()->setPreferredLanguage(current_account_id(), 'pl_PL');

        $this->assertTrue($field->hasHintText());
        $this->assertEquals('English hint', $field->hintText);
    }

    public function testHasHintTextNull(): void
    {
        $field = new Field;
        $field->type = 'text';

        $this->assertFalse($field->hasHintText());
    }

    public function testRemovalOfWhitespaceOnOptions(): void
    {
        $field = new Field;
        $field->options = "lkjsdjlf     \r\n";

        $this->assertSame('lkjsdjlf', $field->options->keys()[0]);
    }

    public function testHasValuesReturnsFalseWhenNotExists(): void
    {
        $this->assertFalse((new Field)->hasValues);
    }

    public function testFieldConditionalSetup(): void
    {
        $field = new Field;
        $field->setConditionalField(true, 'visible', 123, 'lksjdf', 'some value');

        $this->assertSame(true, $field->conditional);
        $this->assertSame('visible', $field->conditionalVisibility);
        $this->assertSame(123, $field->conditionalFieldId);
        $this->assertSame('lksjdf', $field->conditionalPattern);
        $this->assertSame('some value', $field->conditionalValue);
    }

    public function testUserFieldsIgnoreConditionalSettings(): void
    {
        $field = new Field;
        $field->resource = Field::RESOURCE_USERS;
        $field->setConditionalField(true, 'visible', 123, 'lksjdf', 'some value');

        $this->assertFalse($field->conditional);
        $this->assertNull($field->conditionalVisibility);
        $this->assertNull($field->conditionalFieldId);
        $this->assertNull($field->conditionalPattern);
        $this->assertNull($field->conditionalValue);
    }

    public function testSettingOfMaxMinLimits(): void
    {
        $field = new Field;
        $field->setMaxMinLimits(5, 25, 2, 1);

        $this->assertSame(5, $field->maximumWords);
        $this->assertSame(25, $field->maximumCharacters);
        $this->assertSame(2, $field->minimumWords);
        $this->assertSame(1, $field->minimumCharacters);
    }

    public function testAppliesToAllCategories(): void
    {
        $field = new Field;

        $this->assertFalse($field->appliesToAllCategories());

        $field->categoryOption = Field::CATEGORY_OPTION_ALL;

        $this->assertTrue($field->appliesToAllCategories());
    }

    public function testAppliesToReviewStage(): void
    {
        $reviewStage = $this->muffin(ReviewStage::class);
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_TEXT]);
        $field->reviewStages()->sync([$reviewStage->id => ['required' => true]]);
        $this->assertTrue($field->appliesToReviewStage($reviewStage));

        $field->reviewStages()->sync([$reviewStage->id => ['required' => false]]);
        $this->assertFalse($field->refresh()->appliesToReviewStage($reviewStage));

        $field->type = Field::TYPE_CONTENT;
        $field->save();
        $field->reviewStages()->sync([$reviewStage->id => ['required' => true]]);
        $this->assertFalse($field->refresh()->appliesToReviewStage($reviewStage));
    }

    public function testUserField(): void
    {
        $field = new Field;

        $this->assertFalse($field->userField());

        $field->resource = Field::RESOURCE_USERS;

        $this->assertTrue($field->userField());
    }

    public function testRequiredForEntrant(): void
    {
        $field = new Field;
        $field->required = true;

        $this->assertTrue($field->isRequired());
    }

    public function testRequiredForReviewStage(): void
    {
        $field = new Field;
        $stage = new ReviewStage;

        $stage->setRelation('pivot', ['required' => true]);
        $field->setRelation('reviewStages', new Collection([$stage]));

        $this->assertTrue($field->isRequired());
    }

    public function testIsOptionable(): void
    {
        $field1 = new Field;
        $field1->type = 'radio';

        $field2 = new Field;
        $field2->type = 'drop-down-list';

        $field3 = new Field;
        $field3->type = 'checkboxlist';

        $field4 = new Field;
        $field4->type = 'text';

        $this->assertTrue($field1->optionable());
        $this->assertTrue($field2->optionable());
        $this->assertTrue($field3->optionable());
        $this->assertFalse($field4->optionable());
    }

    public function testIsOnAnyCategoryWhenAllSelected(): void
    {
        $field = new Field;
        $field->categoryOption = Field::CATEGORY_OPTION_ALL;

        $this->assertTrue($field->isOnCategory(random_int(1, 100)));
    }

    public function testIsOnSelectedCategoryOnly(): void
    {
        $cat1 = new Category;
        $cat1->id = 3;
        $cat2 = new Category;
        $cat2->id = 5;
        $field = $this->mock(Field::class)->makePartial();
        $field->categoryOption = Field::CATEGORY_OPTION_SELECT;
        $field->setRelation('categories', collect([$cat1, $cat2]));

        $this->assertTrue($field->isOnCategory(3));
        $this->assertFalse($field->isOnCategory(7));
    }

    public function testContentCannotBeRequired(): void
    {
        $field = new Field;
        $field->type = $this->randomFieldTypeExcept('content');
        $field->required = true;

        $this->assertTrue($field->required);

        $field->type = 'content';
        $field->required = true;

        $this->assertFalse($field->required);
    }

    public function testIsContent(): void
    {
        $field = new Field;
        $field->type = $this->randomFieldTypeExcept('content');

        $this->assertFalse($field->isContent());

        $field->type = 'content';

        $this->assertTrue($field->isContent());
    }

    public function testReturnConfigurationWhenAvailable(): void
    {
        // 'text' has simple/default configuration
        $configuration = (new Field(['type' => 'text']))->getConfiguration();
        $this->assertInstanceOf(Simple::class, $configuration);

        // 'table' has configuration
        $configuration = (new Field(['type' => 'table']))->getConfiguration();
        $this->assertInstanceOf(Table::class, $configuration);
    }

    public function testFilesCannotBeProtected(): void
    {
        // ensure default none
        $field = new Field;
        $this->assertEquals('standard', $field->protection);

        // ensure correctly set
        $field->protection = 'maximum';
        $this->assertEquals('maximum', $field->protection);

        // ensure file overrides
        $field->type = 'file';
        $this->assertEquals('standard', $field->protection);
    }

    public function testMaximumProtectionFieldsCannotBeSearched(): void
    {
        $field = new Field;
        $this->assertFalse($field->searchable);

        $field->searchable = true;
        $field->protection = Field::PROTECTION_ELEVATED;
        $this->assertTrue($field->searchable);

        $field->protection = Field::PROTECTION_MAXIMUM;
        $this->assertFalse($field->searchable);
    }

    private function randomFieldTypeExcept(string $ignore): string
    {
        return collect(config('awardforce.fields.types'))
            ->shuffle()
            ->first(function ($value) use ($ignore) {
                return $value != $ignore;
            });
    }

    public function testHasConfiguration(): void
    {
        $configuredField = new Field(['type' => 'table', 'configuration' => '{"columns":["column-YnRjr"]}']);
        $unconfiguredField = new Field(['type' => 'table', 'configuration' => '']);

        $this->assertTrue($configuredField->hasConfiguration());
        $this->assertFalse($unconfiguredField->hasConfiguration());
    }

    public function testSetConfigurationAttribute(): void
    {
        $tableField = new Field(['type' => 'table']);
        $textField = new Field(['type' => 'text']);

        $tableField->configuration = '{}';
        $textField->configuration = '{}';

        $this->assertEquals('{}', $tableField->configuration);
        $this->assertNull($textField->configuration);
    }

    public function testOrderComparision(): void
    {
        $tab1 = new Tab(['id' => 1, 'order' => 2]);
        $tab2 = new Tab(['id' => 2, 'order' => 6]);

        $tab1Order5Field = new Field(['id' => 1, 'order' => 5]);
        $tab1Order5Field->setRelation('tab', $tab1);

        $tab1Order8Field = new Field(['id' => 2, 'order' => 8]);
        $tab1Order8Field->setRelation('tab', $tab1);

        $tab2Order1Field = new Field(['id' => 3, 'order' => 1]);
        $tab2Order1Field->setRelation('tab', $tab2);

        $tab2Order6Field = new Field(['id' => 4, 'order' => 6]);
        $tab2Order6Field->setRelation('tab', $tab2);

        $noTabOrder10Field = new Field(['id' => 5, 'order' => 10]);
        $noTabOrder50Field = new Field(['id' => 6, 'order' => 50]);

        $this->assertSame(
            collect([
                $noTabOrder50Field,
                $tab1Order5Field,
                $tab2Order1Field,
                $tab2Order6Field,
                $noTabOrder10Field,
                $tab1Order8Field,
            ])->sort([Field::class, 'compareTabAndFieldOrder'])->pluck('id')->toArray(),
            [1, 2, 3, 4, 5, 6]
        );

        $this->assertSame(
            collect([
                $noTabOrder50Field,
                $tab1Order5Field,
                $tab2Order1Field,
                $tab2Order6Field,
                $noTabOrder10Field,
                $tab1Order8Field,
            ])->sort([Field::class, 'compareFieldOrder'])->pluck('id')->toArray(),
            [3, 1, 4, 2, 5, 6]
        );
    }

    public function testValuesAffectingEventsThrown(): void
    {
        $field = new Field();
        $field->save();
        $events = $field->releaseEvents();
        // TODO: change expected count to 2 and uncomment line 406 when eloquence event issue is resolved
        $this->assertCount(1, $events);
        $this->assertInstanceOf(FieldWasAdded::class, $events[0]);
        //        $this->assertInstanceOf(FieldWasUpdated::class, $events[1]);

        $field->searchable = 1;
        $events = $field->releaseEvents();
        $this->assertInstanceOf(FieldSearchableWasChanged::class, $events[0]);

        $field->protection = Field::PROTECTION_MAXIMUM;
        $events = $field->releaseEvents();
        $this->assertCount(2, $events);
        $this->assertInstanceOf(FieldProtectionWasChanged::class, $events[0]);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[1]);

        $field->save(['searchable' => 0]);
        $events = $field->releaseEvents();
        $this->assertCount(3, $events);
        $this->assertInstanceOf(FieldWasUpdated::class, $events[0]);
        $this->assertInstanceOf(FieldSearchableWasChanged::class, $events[1]);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[2]);

        $field->save(['protection' => Field::PROTECTION_STANDARD]);
        $events = $field->releaseEvents();
        $this->assertCount(3, $events);
        $this->assertInstanceOf(FieldWasUpdated::class, $events[0]);
        $this->assertInstanceOf(FieldProtectionWasChanged::class, $events[1]);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[2]);

        $field->searchable = 0;
        $field->protection = Field::PROTECTION_STANDARD;
        $events = $field->releaseEvents();
        $this->assertCount(0, $events);

        $field->options = ['opt'];
        $events = $field->releaseEvents();
        $field->save();
        $field->releaseEvents();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(FieldScoreableWasChanged::class, $events[0]);

        $field->autoScoring = true;
        // We don't care for FieldAutoScoringToggled event here
        $field->releaseEvents();
        $field->options = ['opt1'];
        $events = $field->releaseEvents();

        $this->assertCount(2, $events);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[0]);
        $this->assertInstanceOf(FieldScoreableWasChanged::class, $events[1]);
        $this->assertTrue($events[0]->shouldRecalculate());
    }

    public function testOptional(): void
    {
        $field1 = new Field([
            'type' => 'text', 'resource' => 'Entries', 'required' => 0, 'entrant_write_access' => 0,
        ]);
        $field2 = new Field([
            'type' => 'text', 'resource' => 'Entries', 'required' => 0, 'entrant_write_access' => 1,
        ]);
        $field3 = new Field([
            'type' => 'text', 'resource' => 'Entries', 'required' => 1, 'entrant_write_access' => 1,
        ]);
        $field4 = new Field([
            'type' => 'text', 'resource' => 'Users', 'required' => 0, 'entrant_write_access' => 0,
        ]);
        $field5 = new Field([
            'type' => 'text', 'resource' => 'Users', 'required' => 1, 'entrant_write_access' => 0,
        ]);

        $this->assertFalse($field1->optional());
        $this->assertTrue($field2->optional());
        $this->assertFalse($field3->optional());
        $this->assertTrue($field4->optional());
        $this->assertFalse($field5->optional());
    }

    public function testUserFieldReadOnly(): void
    {
        $activeSeason = new Season(['status' => Season::STATUS_ACTIVE]);
        $activeSeason->save();
        $draftSeason = new Season(['status' => Season::STATUS_DRAFT]);
        $draftSeason->save();
        $archivedSeason = new Season(['status' => Season::STATUS_ARCHIVED]);
        $archivedSeason->save();

        $field1 = new Field(['resource' => Field::RESOURCE_USERS, 'seasonal' => 0, 'season_id' => $activeSeason->id]);
        $field2 = new Field(['resource' => Field::RESOURCE_USERS, 'seasonal' => 1, 'season_id' => $activeSeason->id]);
        $field3 = new Field(['resource' => Field::RESOURCE_USERS, 'seasonal' => 0, 'season_id' => $archivedSeason->id]);
        $field4 = new Field(['resource' => Field::RESOURCE_USERS, 'seasonal' => 1, 'season_id' => $archivedSeason->id]);
        $field5 = new Field(['resource' => Field::RESOURCE_USERS, 'seasonal' => 0, 'season_id' => $draftSeason->id]);
        $field6 = new Field(['resource' => Field::RESOURCE_USERS, 'seasonal' => 1, 'season_id' => $draftSeason->id]);

        $this->assertFalse($field1->readOnly());
        $this->assertFalse($field2->readOnly());
        $this->assertFalse($field3->readOnly());
        $this->assertTrue($field4->readOnly());
        $this->assertFalse($field5->readOnly());
        $this->assertFalse($field6->readOnly());
    }

    public function testTestTransformsOldAndNewOptionTextFormat(): void
    {
        $field = new Field([
            'type' => 'drop-down-list',
            'options' => "op1\r\nop2\r\nop3",
        ]);

        $field->addTranslation('en_GB', 'optionText', json_encode(['op1' => 'Option 1', 'op2' => 'Option 2', 'op3' => 'Option 3']));
        $field->addTranslation('el_GR', 'optionText', "Greek 1\r\nGreek 2\r\nGreek 3");

        $this->assertEquals(['op1' => 'Option 1', 'op2' => 'Option 2', 'op3' => 'Option 3'], $field->optionText);
        $this->assertEquals(['op1' => 'Greek 1', 'op2' => 'Greek 2', 'op3' => 'Greek 3'], $field->translated['el_GR']['optionText']);
    }

    public function testTranslatedOptionTextIsTrimmed(): void
    {
        $field = new Field([
            'type' => 'drop-down-list',
            'options' => "I ❤️ \r\n💯👌🏻 か\r\nnői dolog",
        ]);

        $field->addTranslation('en_GB', 'optionText', json_encode(
            [' I ❤️ ' => ' පීසා     ', ' 💯👌🏻 か ' => '    比 ', ' női dolog ' => ' 𩸽 exotic test ホ 𩸽 ']
        ));

        $this->assertEquals(['I ❤️' => 'පීසා', '💯👌🏻 か' => '比', 'női dolog' => '𩸽 exotic test ホ 𩸽'], $field->optionText);
    }

    public function testTranslatedOptionWithInt(): void
    {
        $field = new Field([
            'type' => 'drop-down-list',
            'options' => ' 1 ',
        ]);

        $field->addTranslation('en_GB', 'optionText', ' 1 ');

        $this->assertEquals(' 1 ', $field->optionText['1']);
    }

    public function testOptionsGetterArray(): void
    {
        $field = new Field([
            'type' => 'drop-down-list',
            'options' => json_encode(['One' => 1.1, 'Two' => 2.2]),
        ]);

        $this->assertEquals('One', $field->options->get('One')->value);
        $this->assertEquals(1.1, $field->options->score('One'));
    }

    public function testOptionsGetterString(): void
    {
        $field = new Field([
            'type' => 'drop-down-list',
            'options' => "One\r\nTwo",
        ]);

        $this->assertEquals('One', $field->options->get('One')->value);
        $this->assertEquals(0.0, $field->options->score('One'));
    }

    public function testOptionsGetterNumeric(): void
    {
        $field = new Field([
            'type' => 'drop-down-list',
            'options' => "1\r\n2",
        ]);

        $this->assertEquals('1', $field->options->get('1')->value);
        $this->assertEquals(0.0, $field->options->score('1'));

        $field->setOptionsAttribute('1');

        $this->assertEquals('1', $field->options->get(1)->value);
        $this->assertEquals(0.0, $field->options->score('1'));
    }

    public function testHasScores(): void
    {
        $field = new Field([
            'type' => 'drop-down-list',
            'options' => '{"1": 0, "2" : 0}',
        ]);

        $this->assertFalse($field->hasScores());

        $field->options = '{"1": 1, "2" : 0}';

        $this->assertTrue($field->hasScores());
    }

    public function testNeedsScoreRecalculation(): void
    {
        $makeField = function ($autoScoring = 0) {
            return (new Field([
                'resource' => Field::RESOURCE_FORMS,
                'type' => 'drop-down-list',
                'options' => '{"1": 0, "2" : 0}',
                'auto_scoring' => $autoScoring,
            ]))->syncOriginal();
        };
        $field = $makeField();

        $this->assertFalse($field->needsScoreRecalculation());

        $field->autoScoring = 1;
        $this->assertTrue($field->needsScoreRecalculation());

        $field = $makeField();
        $field->options = '{"1": 1, "2" : 0}';
        // Auto scoring was OFF so there is no need for recalculation yet
        $this->assertFalse($field->needsScoreRecalculation());
        $field->autoScoring = 1;
        $this->assertTrue($field->needsScoreRecalculation());

        $field = $makeField(1);
        $field->options = '{"1": 1, "2" : 0}';
        // Auto scoring was ON, so we have to recalculate when options change
        $this->assertTrue($field->needsScoreRecalculation());
    }

    public function testFieldOptionUpdateTriggeringRecalculation()
    {
        $field = new Field();
        $field->options = ['First' => 0, 'Third' => 3];
        $field->save();
        $field->releaseEvents();

        //Adding new values should not trigger recalculation
        $field->options = ['First' => 0, 'Second' => 2, 'Third' => 3, 'Fourth' => 4];
        $events = $field->releaseEvents();
        $field->save();
        $field->releaseEvents();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(FieldScoreableWasChanged::class, $events[0]);

        //Changing a value should trigger recalculation
        $field->options = ['First' => 1, 'Second' => 2, 'Third' => 3, 'Fourth' => 4];
        $events = $field->releaseEvents();
        $field->save();
        $field->releaseEvents();

        $this->assertCount(2, $events);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[0]);

        //Removing a value should trigger recalculation
        $field->options = ['First' => 1, 'Second' => 2, 'Third' => 3];
        $events = $field->releaseEvents();
        $field->save();
        $field->releaseEvents();

        $this->assertCount(2, $events);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[0]);

        $field = new Field();
        $field->save();
        $field->releaseEvents();

        //Adding field when was empty should not trigger recalculation
        $field->options = ['Option'];
        $events = $field->releaseEvents();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(FieldScoreableWasChanged::class, $events[0]);

        //Support to old options format. When removing an option should recalculate
        $field = new Field();
        $field->options = "Option A\r\nOption B\r\nOption C\r\nOption D";
        $field->save();
        $field->releaseEvents();

        $field->options = "Option A\r\nOption B\r\nOption D";
        $events = $field->releaseEvents();

        $this->assertCount(2, $events);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[0]);

        $field = new Field;
        $field->options = ['First' => 1];
        $field->save();
        $field->releaseEvents(); // Clean FieldAdded event

        $field->options = ['First' => 2, 'Second' => 1];
        $events = $field->releaseEvents();

        $this->assertCount(2, $events);
        $this->assertInstanceOf(FieldUpdateAffectingValues::class, $events[0]);

        $field = new Field;
        $field->options = ['First' => 1];
        $field->save();
        $field->releaseEvents(); // Clean FieldAdded event

        $field->options = ['First' => 1, 'Second' => 2];
        $events = $field->releaseEvents();

        $this->assertCount(1, $events);
        $this->assertInstanceOf(FieldScoreableWasChanged::class, $events[0]);
    }

    public function testAutoTagIsFalseByDefault(): void
    {
        $field = new Field;
        $field->save();

        $this->assertFalse($field->autoTag);
    }

    public function testEmptyMinVideoLengthIsSavedAsNullInsteadZero(): void
    {
        $field = new Field;
        $field->minVideoLength = '';
        $field->save();

        $this->assertNull($field->minVideoLength);
    }

    public function testEmptyMaxVideoLengthIsSavedAsNullInsteadZero(): void
    {
        $field = new Field;
        $field->maxVideoLength = '';
        $field->save();

        $this->assertNull($field->maxVideoLength);
    }

    public function testWordsAndCharactersCountRetrievePluralForUkrainian(): void
    {
        $consumer = new MockConsumer;
        $consumer->language = new Language('uk_UA');
        Consumer::set($consumer);

        $field = new Field;
        $field->maximumWords = 250;
        $field->maximumCharacters = 250;

        $this->assertEquals(' слів: 250|250 символа/ів', $field->wordsAndCharactersCount());
    }

    public function testWordsAndCharactersCountRetrievePluralForRussian(): void
    {
        $consumer = new MockConsumer;
        $consumer->language = new Language('ru_RU');
        Consumer::set($consumer);

        $field = new Field;
        $field->maximumWords = 250;
        $field->maximumCharacters = 250;

        $this->assertEquals('250 слов(а)|250 симв.', $field->wordsAndCharactersCount());
    }

    public function testItCanParseMalformedJsonWithSlashes(): void
    {
        $field = new Field;
        $field->setRawAttributes(['options' => '{"Option \1": 1, "Option \s 2/": 2, "Option \\ 3\": 3}']);

        $this->assertEquals([
            'Option 1',
            'Option s 2/',
            'Option  3',
        ], $field->options->keys());
    }

    public function testFieldResources()
    {
        $field = new Field;
        $field->resource = Field::RESOURCE_FORMS;

        $this->assertTrue($field->entryField());
        $this->assertFalse($field->attachmentField());
        $this->assertFalse($field->contributorField());

        $field->resource = Field::RESOURCE_ATTACHMENTS;

        $this->assertFalse($field->entryField());
        $this->assertTrue($field->attachmentField());
        $this->assertFalse($field->contributorField());

        $field->resource = Field::RESOURCE_CONTRIBUTORS;

        $this->assertFalse($field->entryField());
        $this->assertFalse($field->attachmentField());
        $this->assertTrue($field->contributorField());
    }

    public function testWritableIfUserField(): void
    {
        $field = new Field;
        $field->resource = Field::RESOURCE_USERS;

        $this->assertTrue($field->writable());
    }

    public function testWritableIfFormulaField(): void
    {
        $field = new Field;
        $field->resource = Field::RESOURCE_FORMS;
        $field->type = Field::TYPE_FORMULA;

        $this->assertTrue($field->writable());
    }

    public function testWritableIfEntrantWriteAccessIsTrue(): void
    {
        $field = new Field;
        $field->resource = Field::RESOURCE_FORMS;
        $field->type = Field::TYPE_TEXT;
        $field->entrantWriteAccess = true;

        $this->assertTrue($field->writable());
    }

    public function testShouldNotWritableIfEntrantWriteAccessIsFalse(): void
    {
        $field = new Field;
        $field->resource = Field::RESOURCE_FORMS;
        $field->type = Field::TYPE_TEXT;
        $field->entrantWriteAccess = false;

        $this->assertFalse($field->writable());
    }
}
