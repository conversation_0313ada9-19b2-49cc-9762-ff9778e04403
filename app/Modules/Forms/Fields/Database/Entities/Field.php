<?php

namespace AwardForce\Modules\Forms\Fields\Database\Entities;

use AwardForce\Library\Database\Eloquent\ConfigurationExporter;
use AwardForce\Library\Database\Eloquent\ExportsConfiguration;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Localisation\InjectTranslations;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Forms\Fields\Configurations\Configuration;
use AwardForce\Modules\Forms\Fields\Configurations\Simple;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\ImageDimensionConstraints;
use AwardForce\Modules\Forms\Fields\Events\FieldAutoScoringToggled;
use AwardForce\Modules\Forms\Fields\Events\FieldProtectionWasChanged;
use AwardForce\Modules\Forms\Fields\Events\FieldScoreableWasChanged;
use AwardForce\Modules\Forms\Fields\Events\FieldSearchableWasChanged;
use AwardForce\Modules\Forms\Fields\Events\FieldUpdateAffectingValues;
use AwardForce\Modules\Forms\Fields\Events\FieldWasAdded;
use AwardForce\Modules\Forms\Fields\Events\FieldWasCopied;
use AwardForce\Modules\Forms\Fields\Events\FieldWasDeleted;
use AwardForce\Modules\Forms\Fields\Events\FieldWasUpdated;
use AwardForce\Modules\Forms\Fields\Exceptions\FieldHasDependents;
use AwardForce\Modules\Forms\Fields\Exceptions\FieldIsConditionalOnADeletedField;
use AwardForce\Modules\Forms\Fields\Values\Options\Options;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Relations\BelongsToForm;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;
use AwardForce\Modules\Seasons\Traits\Seasonal;
use Eloquence\Behaviours\CountCache\CountedBy;
use Eloquence\Behaviours\CountCache\HasCounts;
use Eloquence\Behaviours\HasSlugs;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Platform\Database\Eloquent\TranslatableModel;
use Tectonic\Localisation\Contracts\Translatable;
use Tectonic\Localisation\Translator\Translations;

/**
 * AwardForce\Modules\Forms\Fields\Database\DataAccess\Field
 *
 * @property int $id
 * @property int $accountId
 * @property int|null $formId
 * @property int|null $tabId
 * @property int $seasonId
 * @property string|null $slug
 * @property string $resource
 * @property string $type
 * @property int $seasonal
 * @property Options $options
 * @property string|null $currency
 * @property string $protection
 * @property bool $searchable
 * @property int|null $order
 * @property string $categoryOption
 * @property string $roleOption
 * @property string $roundOption
 * @property int|null $maximumWords
 * @property int|null $minimumWords
 * @property int|null $maximumCharacters
 * @property int|null $minimumCharacters
 * @property bool $required
 * @property int $autocomplete
 * @property int $includeTimezone
 * @property bool $preselectCurrentDate
 * @property string $registration
 * @property int $fieldvalueCount
 * @property int $hasValues
 * @property string $visibility
 * @property array $fileTypes
 * @property int|null $maxFileSize
 * @property int $conditional
 * @property string|null $conditionalVisibility
 * @property int|null $conditionalFieldId
 * @property string|null $conditionalPattern
 * @property string|null $conditionalValue
 * @property bool $entrantReadAccess
 * @property bool $entrantWriteAccess
 * @property string|null $configuration
 * @property int $plagiarismDetection
 * @property int $autoScoring
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property \Illuminate\Support\Carbon|null $deletedAt
 * @property bool $autoTag
 * @property int|null $minVideoLength
 * @property int|null $maxVideoLength
 * @property ImageDimensionConstraints $imageDimensionConstraints
 * @property-read Account|null $account
 * @property-read \Baum\Extensions\Eloquent\Collection<int, Category> $categories
 * @property-read int|null $categoriesCount
 * @property-read Field|null $conditionalField
 * @property-read Fields<int, Field> $dependents
 * @property-read int|null $dependentsCount
 * @property-read Form|null $form
 * @property bool $isRecalculating
 * @property-read \Platform\Database\Eloquent\Collection<int, ReviewStage> $reviewStages
 * @property-read int|null $reviewStagesCount
 * @property-read \AwardForce\Modules\Identity\Roles\Models\Roles<int, Role> $roles
 * @property-read int|null $rolesCount
 * @property-read \AwardForce\Modules\Seasons\Models\Season|null $season
 * @property-read ScoreSetCollection<int, ScoreSet> $sets
 * @property-read int|null $setsCount
 * @property-read Tab|null $tab
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Tectonic\LaravelLocalisation\Database\Translation> $translations
 * @property-read int|null $translationsCount
 *
 * @method static Fields<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Field forConfiguration()
 * @method static \Platform\Database\Eloquent\Builder|Field forSeason($seasonId)
 * @method static Fields<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Field newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Field newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Field onlyTrashed()
 * @method static \Platform\Database\Eloquent\Builder|Field preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Field query()
 * @method static \Platform\Database\Eloquent\Builder|Field whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereAutoScoring($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereAutoTag($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereAutocomplete($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereCategoryOption($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereConditional($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereConditionalFieldId($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereConditionalPattern($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereConditionalValue($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereConditionalVisibility($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereConfiguration($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereCurrency($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereEntrantReadAccess($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereEntrantWriteAccess($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereFieldvalueCount($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereFileTypes($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereFormId($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereHasValues($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereIncludeTimezone($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereMaxFileSize($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereMaxVideoLength($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereMaximumCharacters($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereMaximumWords($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereMinVideoLength($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereMinimumCharacters($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereMinimumWords($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereOptions($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereOrder($value)
 * @method static \Platform\Database\Eloquent\Builder|Field wherePlagiarismDetection($value)
 * @method static \Platform\Database\Eloquent\Builder|Field wherePreselectCurrentDate($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereProtection($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereRegistration($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereRequired($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereResource($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereRoleOption($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereRoundOption($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereSearchable($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereSeasonal($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereTabId($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereType($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Field whereVisibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Field withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Field withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Field extends Model implements ConfigurationExporter, Translatable
{
    use BelongsToForm;
    use BelongsToForm;
    use ExportsConfiguration;
    use HasCounts;
    use HasSlugs;
    use InjectTranslations;
    use Seasonal;
    use SoftDeletes;
    use TranslatableModel;
    use Translations;

    const VISIBILITY_DIVIDER = '|';

    const RESOURCE_ATTACHMENTS = 'Attachments';

    const RESOURCE_FORMS = 'Entries';

    const RESOURCE_USERS = 'Users';

    const RESOURCE_CONTRIBUTORS = 'Contributors';

    const RESOURCE_REFEREES = 'Referees';

    const RESOURCE_ORGANISATIONS = 'Organisations';

    const TYPE_CONTENT = 'content';

    const TYPE_FILE = 'file';

    // Table types
    const TYPE_TABLE = 'table';

    const TYPE_TEXTAREA = 'textarea';

    const TYPE_TEXT = 'text';

    const TYPE_URL = 'url';

    const TYPE_DATE = 'date';

    const TYPE_CHECKBOXLIST = 'checkboxlist';

    const TYPE_CURRENCY = 'currency';

    const TYPE_DATETIME = 'datetime';

    const TYPE_TIME = 'time';

    const TYPE_FORMULA = 'formula';

    const TYPE_AI = 'ai';

    const CATEGORY_OPTION_ALL = 'all';

    const CATEGORY_OPTION_SELECT = 'select';

    const ROLE_OPTION_ALL = 'all';

    const ROLE_OPTION_SELECT = 'select';

    const PROTECTION_STANDARD = 'standard';

    const PROTECTION_ELEVATED = 'elevated';

    const PROTECTION_MAXIMUM = 'maximum';

    const PROTECTION_TYPES = ['standard', 'elevated', 'maximum'];

    const SCOREABLE_TYPES = ['radio', 'drop-down-list', 'checkboxlist', 'checkbox'];

    const OPTIONABLE_TYPES = ['radio', 'drop-down-list', 'checkboxlist'];

    /**
     * The attributes that should be casted to native types.
     *
     * @var array
     */
    protected $casts = [
        'entrant_read_access' => 'boolean',
        'entrant_write_access' => 'boolean',
        'required' => 'boolean',
        'file_types' => 'array',
        'preselect_current_date' => 'boolean',
        'max_file_size' => 'integer',
        'auto_tag' => 'boolean',
        'min_video_length' => 'integer',
        'image_dimension_constraints' => ImageDimensionConstraints::class,
    ];

    protected $attributes = [
        'auto_tag' => false,
    ];
    protected $table = 'fields';
    protected $copiedEvent = FieldWasCopied::class;
    protected $deletedEvent = FieldWasDeleted::class;

    /**
     * @param  int  $accountId
     * @param  int  $seasonId
     * @param  int  $tabId
     * @param  string  $resource
     * @param  string  $type
     * @param  bool  $seasonal
     * @param  string  $options
     * @param  int  $order
     * @param  string  $categoryOption
     * @param  string  $roleOption
     * @param  bool  $autoScoring
     * @return Field
     */
    public static function add(
        $accountId,
        $seasonId,
        $formId,
        $tabId,
        $resource,
        $type,
        $seasonal,
        $options,
        $order,
        $categoryOption,
        $roleOption,
        $autoScoring = 0,
        bool $autoTag = false,
        array $params = [],
    ) {
        $field = self::make(
            $accountId,
            $seasonId,
            $formId,
            $tabId,
            $resource,
            $type,
            $seasonal,
            $options,
            $order,
            $categoryOption,
            $roleOption,
            $autoScoring,
            $autoTag,
            $params
        );

        $field->save();

        return $field;
    }

    /**
     * @param  int  $accountId
     * @param  int  $seasonId
     * @param  int  $formId
     * @param  int  $tabId
     * @param  string  $resource
     * @param  string  $type
     * @param  bool  $seasonal
     * @param  string  $options
     * @param  int  $order
     * @param  string  $categoryOption
     * @param  string  $roleOption
     * @param  bool  $autoScoring
     * @return Field
     */
    public static function make(
        $accountId,
        $seasonId,
        $formId,
        $tabId,
        $resource,
        $type,
        $seasonal,
        $options,
        $order,
        $categoryOption,
        $roleOption,
        $autoScoring = 0,
        bool $autoTag = false,
        array $params = []
    ) {
        $field = new self;
        $field->accountId = $accountId;
        $field->seasonId = $seasonId;
        $field->formId = $formId;
        $field->tabId = $tabId;
        $field->resource = $resource;
        $field->type = $type;
        $field->seasonal = $seasonal;
        $field->options = is_string($options) ? implode("\r\n", explode_options($options)) : $options;
        $field->order = $order;
        $field->categoryOption = $categoryOption;
        $field->roleOption = $roleOption;
        $field->autoScoring = $autoScoring;
        $field->autoTag = $autoTag;

        $field->required = array_get($params, 'required', false);
        $field->includeTimezone = array_get($params, 'includeTimezone', false);
        $field->preselectCurrentDate = array_get($params, 'preselectCurrentDate', false);
        $field->protection = array_get($params, 'protection', self::PROTECTION_STANDARD);
        $field->searchable = array_get($params, 'searchable', false);
        $field->registration = array_get($params, 'registration', false);
        $field->autocomplete = array_get($params, 'autocomplete', false);
        $field->fileTypes = array_get($params, 'fileTypes', []);
        $field->maxFileSize = array_get($params, 'maxFileSize');
        $field->minVideoLength = array_get($params, 'minVideoLength');
        $field->maxVideoLength = array_get($params, 'maxVideoLength');
        $field->imageDimensionConstraints = ImageDimensionConstraints::fromArray(array_get($params, 'imageDimensionConstraints', []));
        $field->entrantReadAccess = array_get($params, 'entrantReadAccess', false);
        $field->entrantWriteAccess = array_get($params, 'entrantWriteAccess', false);
        $field->configuration = array_get($params, 'configuration');
        $field->plagiarismDetection = array_get($params, 'plagiarismDetection', false);
        $field->currency = array_get($params, 'currency');

        $field->setConditionalField(
            array_get($params, 'conditional', false),
            array_get($params, 'conditionalVisibility'),
            array_get($params, 'conditionalField'),
            array_get($params, 'conditionalPattern'),
            array_get($params, 'conditionalValue')
        );
        $field->setMaxMinLimits(
            array_get($params, 'maximumWords'),
            array_get($params, 'maximumCharacters'),
            array_get($params, 'minimumWords'),
            array_get($params, 'minimumCharacters')
        );

        return $field;
    }

    /**
     * @return bool
     */
    public function save(array $options = [])
    {
        if ($this->exists) {
            $this->raiseUnique(new FieldWasUpdated($this));
            if (array_key_exists('protection', $options)) {
                $this->protection = $options['protection'];
            }
            if (array_key_exists('searchable', $options)) {
                $this->searchable = $options['searchable'];
            }
        } else {
            $this->raiseUnique(new FieldWasAdded($this));
        }

        return parent::save($options);
    }

    /**
     * @param  bool  $conditional
     * @param  string  $conditionalVisibility
     * @param  string  $conditionalFieldId
     * @param  string  $conditionalPattern
     * @param  string  $conditionalValue
     * @return $this
     */
    public function setConditionalField(
        $conditional,
        $conditionalVisibility,
        $conditionalFieldId,
        $conditionalPattern,
        $conditionalValue
    ) {
        if ($this->userField()) {
            $conditional = false;
        }

        $this->conditional = $conditional;
        $this->conditionalVisibility = $conditional ? $conditionalVisibility : null;
        $this->conditionalFieldId = $conditional ? $conditionalFieldId : null;
        $this->conditionalPattern = $conditional ? $conditionalPattern : null;
        $this->conditionalValue = $conditional ? $conditionalValue : null;

        return $this;
    }

    /**
     * @param  int  $maximumWords
     * @param  int  $maximumCharacters
     * @param  int  $minimumWords
     * @param  int  $minimumCharacters
     * @return $this
     */
    public function setMaxMinLimits($maximumWords, $maximumCharacters, $minimumWords, $minimumCharacters)
    {
        $this->maximumWords = first_or_max($maximumWords, $minimumWords);
        $this->maximumCharacters = first_or_max($maximumCharacters, $minimumCharacters);
        $this->minimumWords = first_or_min($minimumWords, $maximumWords);
        $this->minimumCharacters = first_or_min($minimumCharacters, $maximumCharacters);

        return $this;
    }

    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    #[CountedBy(as: 'field_count')]
    public function tab(): BelongsTo
    {
        return $this->belongsTo(Tab::class);
    }

    public function categories()
    {
        return $this->belongsToMany(Category::class);
    }

    public function conditionalField()
    {
        return $this->belongsTo(Field::class, 'conditional_field_id');
    }

    /**
     * Returns all fields that are dependent on this one for one reason or another.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function dependents()
    {
        return $this->hasMany(Field::class, 'conditional_field_id');
    }

    /**
     * User fields may be associated with one or more roles, for registration.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function roles()
    {
        return $this->belongsTomany(Role::class)->withPivot('read_access', 'write_access', 'required');
    }

    /**
     * Create a new field collection.
     *
     * @return Fields
     */
    public function newCollection(array $models = [])
    {
        return new Fields($models);
    }

    /**
     * Only user fields can be made not seasonal.
     */
    public function setSeasonalAttribute($value)
    {
        if (! $this->userField()) {
            $this->attributes['seasonal'] = true;

            return;
        }

        $this->attributes['seasonal'] = (bool) $value;
    }

    public function getFileTypesAttribute($value)
    {
        $fileTypes = $this->castAttribute('file_types', $this->getRawOriginal('file_types'));

        if ($this->isFile() && is_array($fileTypes)) {
            return array_filter($fileTypes, fn($fileType) => allowed_file_extension($fileType));
        }

        return $fileTypes;
    }

    /**
     * Synchronise the roles provided in the array.
     *
     * @return $this
     */
    public function syncRoles(array $roleIds)
    {
        $this->roles()->sync($roleIds);

        return $this;
    }

    /**
     * Synchronise the roles including access settings provided in the array.
     *
     * @return $this
     */
    public function syncRoleAccessSettings(array $settings)
    {
        $settings = array_map(function ($setting) {
            $setting['read_access'] = $setting['read_access'] ?? 0;
            $setting['write_access'] = $setting['write_access'] ?? 0;
            $setting['required'] = $setting['required'] ?? 0;

            return $setting;
        }, $settings);

        $this->roles()->sync($settings);

        return $this;
    }

    /**
     * Simple helper method to return all category ids that the field is associated with.
     */
    public function categoryIds(): array
    {
        if (! $this->relationLoaded('categories')) {
            $this->load('categories');
        }

        return $this->categories->pluck('id')->toArray();
    }

    public function getProtectionAttribute($value): string
    {
        return $this->isFile() || ! $value ? self::PROTECTION_STANDARD : $value;
    }

    public function setProtectionAttribute($value)
    {
        $original = $this->attributes['protection'] ?? self::PROTECTION_STANDARD;

        $this->attributes['protection'] = $value;

        if ($this->id && $value != $original) {
            $this->raiseUnique(new FieldProtectionWasChanged($this));
            $this->raiseUnique(new FieldUpdateAffectingValues($this));
        }
    }

    public function standardProtection(): bool
    {
        return $this->protection == self::PROTECTION_STANDARD;
    }

    public function elevatedProtection(): bool
    {
        return $this->protection == self::PROTECTION_ELEVATED;
    }

    public function maximumProtection(): bool
    {
        return $this->protection == self::PROTECTION_MAXIMUM;
    }

    public function getSearchableAttribute($value): bool
    {
        return $this->protection != self::PROTECTION_MAXIMUM && $value;
    }

    public function setSearchableAttribute($value)
    {
        $value = (bool) $value;
        $original = $this->attributes['searchable'] ?? false;

        $this->attributes['searchable'] = $value;

        if ($this->id && $value != $original) {
            $this->raiseUnique(new FieldSearchableWasChanged($this));
            $this->raiseUnique(new FieldUpdateAffectingValues($this));
        }
    }

    /**
     * Returns true if the judging mode is visible.
     *
     * @param  ScoreSet|ScoreSetCollection  $data
     * @return bool
     */
    public function hasVisibility($data)
    {
        if ($data instanceof ScoreSet) {
            return in_array($data->id, $this->sets()->pluck('id')->unique()->toArray());
        } elseif ($data instanceof ScoreSetCollection) {
            $ids = $data->pluck('id')->unique();

            return $ids->count() === count(array_intersect($ids->toArray(), $this->sets()->pluck('id')->unique()->toArray()));
        }

        return false;
    }

    /**
     * Simple mutator for file types.
     */
    public function setFileTypesAttribute(array $value)
    {
        $this->attributes['file_types'] = json_encode(array_values($value));
    }

    /**
     * Strips any trailing whitespace from options.
     *
     * @param  string  $options
     */
    public function setOptionsAttribute($options)
    {
        if (is_array($options)) {
            $options = json_encode($options);
        }
        $options = trim($options ?? '');

        $original = $this->attributes['options'] ?? '';

        $this->attributes['options'] = $options;

        if ($this->id && ($affectsValues = $this->affectsValues())) {
            $this->raiseUnique(new FieldUpdateAffectingValues($this, (bool) $this->autoScoring));
        }
        if ($this->id && $options != $original) {
            $this->raiseUnique(new FieldScoreableWasChanged($this, $affectsValues ?? false));
        }
    }

    public function getOptionsAttribute($options): Options
    {
        $data = [];
        // Stripslashes to parse problematic json strings
        if (is_json($json = $options) || is_json($json = stripslashes($options))) {
            $data = json_decode($json, true);
        } elseif (is_string($options)) {
            $data = array_fill_keys(explode_options($options), 0.0);
        }

        return Options::fromField($this, $data);
    }

    public function setAutoScoringAttribute($value)
    {
        $original = $this->attributes['auto_scoring'] ?? null;

        $this->attributes['auto_scoring'] = $value;

        if ($this->id && $value != $original) {
            $this->raiseUnique(new FieldAutoScoringToggled($this));
        }
    }

    /**
     * Returns true if the field is one of the following types: radio, drop-down-list or checkboxlist.
     */
    public function optionable(): bool
    {
        return in_array($this->type, self::OPTIONABLE_TYPES);
    }

    /**
     * Returns the category option of the field.
     *
     * Attachment fields always apply to all categories.
     *
     * @return string
     */
    public function getCategoryOptionAttribute($value)
    {
        if ($this->resource == self::RESOURCE_ATTACHMENTS) {
            return self::CATEGORY_OPTION_ALL;
        }

        return $value;
    }

    /**
     * Returns an array of the field names that can be used for translations.
     *
     * @return array
     */
    public function getTranslatableFields()
    {
        return ['title', 'label', 'helpText', 'hintText', 'optionText'];
    }

    /**
     * The fields that need something appended to them when copied.
     *
     * @return array
     */
    public function getTranslatableCopyAppendFields()
    {
        return [
            'title' => 'fields.actions.copy.append',
        ];
    }

    /**
     * @param  bool  $ignoreContent
     * @return bool
     */
    public function isEmpty($ignoreContent = true)
    {
        if ($ignoreContent && $this->isContent()) {
            return false;
        }

        return is_null($this->value)
            || (is_string($this->value) && ! strlen(trim($this->value)))
            || (is_array($this->value) && ! count($this->value))
            || ($this->isTable() && empty($this->value['values']));
    }

    /**
     * @return bool
     */
    public function hasHintText()
    {
        return $this->type !== self::TYPE_CONTENT && $this->hasTranslation('hintText', null, current_account()->defaultLanguage()->code);
    }

    /**
     * @return bool
     */
    public function hasHelpText()
    {
        return $this->type !== self::TYPE_CONTENT && $this->hasTranslation('helpText', null, current_account()->defaultLanguage()->code);
    }

    /**
     * Overload the delete method so that we can throw more specific event objects.
     *
     * @return bool|null
     *
     * @throws \Exception
     */
    public function delete()
    {
        // You cannot delete a field if it has field dependents
        if ($this->dependents()->count()) {
            throw new FieldHasDependents;
        }

        return parent::delete();
    }

    /**
     * Overload the restore method so that we can throw more specific event objects.
     *
     * @throws \Exception
     */
    public function restore()
    {
        $parent = $this->conditionalField()->withTrashed()->first();

        if ($parent && $parent->trashed()) {
            throw new FieldIsConditionalOnADeletedField();
        }

        return parent::restore();
    }

    /**
     * @return bool
     */
    public function useMarkdown()
    {
        return $this->type === self::TYPE_TEXTAREA;
    }

    public function getMaxFileSizeAttribute($value): ?int
    {
        return (int) $value ?: null;
    }

    /**
     * @param  int  $value
     */
    public function setMaximumWordsAttribute($value)
    {
        $this->attributes['maximum_words'] = $value ?: null; // Ensure 0 is saved as null
    }

    /**
     * @param  int  $value
     */
    public function setMaximumCharactersAttribute($value)
    {
        $this->attributes['maximum_characters'] = $value ?: null; // Ensure 0 is saved as null
    }

    /**
     * @param  int  $value
     */
    public function setMinimumWordsAttribute($value)
    {
        $this->attributes['minimum_words'] = $value ?: null; // Ensure 0 is saved as null
    }

    /**
     * @param  int  $value
     */
    public function setMinimumCharactersAttribute($value)
    {
        $this->attributes['minimum_characters'] = $value ?: null; // Ensure 0 is saved as null
    }

    public function setMinVideoLengthAttribute($value): void
    {
        $this->attributes['min_video_length'] = $value ?: null; // Ensure 0 is saved as null
    }

    public function setMaxVideoLengthAttribute($value): void
    {
        $this->attributes['max_video_length'] = $value ?: null; // Ensure 0 is saved as null
    }

    /**
     * Checks if a field applies to all categories
     *
     * @return bool
     */
    public function appliesToAllCategories()
    {
        return $this->categoryOption == self::CATEGORY_OPTION_ALL;
    }

    /**
     * Checks if a field applies to review stage
     */
    public function appliesToReviewStage(ReviewStage $reviewStage): bool
    {
        return $this->type !== 'content' && $this->reviewStages()
            ->whereReviewStageId($reviewStage->id)
            ->whereRequired(true)
            ->exists();
    }

    /**
     * Checks if the field is an Entry field
     *
     * @return bool
     */
    public function entryField()
    {
        return $this->resource == self::RESOURCE_FORMS;
    }

    public function attachmentField()
    {
        return $this->resource == self::RESOURCE_ATTACHMENTS;
    }

    public function contributorField()
    {
        return $this->resource == self::RESOURCE_CONTRIBUTORS;
    }

    public function refereeField(): bool
    {
        return $this->resource === self::RESOURCE_REFEREES;
    }

    /**
     * Checks if the field is a User field
     *
     * @return bool
     */
    public function userField()
    {
        return $this->resource == self::RESOURCE_USERS;
    }

    public function getRequiredAttribute($value): bool
    {
        return $this->isContent() ? false : (bool) $value;
    }

    /**
     * Checks if the field is required anywhere, such as the entry form or a review stage.
     * Note: we're type-casting internally as Eloquent is ignoring the cast when not set.
     */
    public function isRequired(): bool
    {
        if ($this->required) {
            return true;
        }

        return $this->reviewStages->contains('pivot.required', true);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function reviewStages()
    {
        return $this->belongsToMany(ReviewStage::class)->withPivot('read', 'write', 'required');
    }

    public function isOnCategory(int $category): bool
    {
        return $this->appliesToAllCategories() ? true : in_array($category, $this->categoryIds());
    }

    public function isContent(): bool
    {
        return $this->type === self::TYPE_CONTENT;
    }

    public function isFileField(): bool
    {
        return $this->type === self::TYPE_FILE;
    }

    public function isTextarea(): bool
    {
        return $this->type === self::TYPE_TEXTAREA;
    }

    public function isTable(): bool
    {
        return $this->type === self::TYPE_TABLE;
    }

    /**
     * Generate configuration class for specific field type.
     *
     * Uses dynamic class detection, to avoid an ugly map that can be easily missed.
     * Tested in `FieldTest::testReturnConfigurationWhenAvailable` to prevent breakages.
     */
    public function getConfiguration(): Configuration
    {
        $class = '\AwardForce\Modules\Forms\Fields\Configurations\\'.studly_case($this->type ?? '');

        return class_exists($class) ? new $class($this) : new Simple($this);
    }

    public function hasConfiguration(): bool
    {
        return $this->configurable() && ! empty($this->configuration);
    }

    public function hasTranslatableConfiguration(): bool
    {
        return $this->getConfiguration() instanceof Translatable;
    }

    /**
     * Set configuration attribute on fields that are configurable.
     */
    public function setConfigurationAttribute($value)
    {
        $this->attributes['configuration'] = $this->configurable() ? $value : null;
    }

    public function configurable(): bool
    {
        return ! $this->getConfiguration() instanceof Simple;
    }

    public function isFile(): bool
    {
        return $this->type === self::TYPE_FILE;
    }

    public function optional(): bool
    {
        return ! $this->required && ($this->entrantWriteAccess || $this->userField());
    }

    public function entrantRead(): bool
    {
        return (bool) $this->entrantReadAccess;
    }

    public function visibleToEntrant(): bool
    {
        return $this->entrantReadAccess && self::tabIsVisible($this);
    }

    public function sets()
    {
        return $this->belongsToMany(ScoreSet::class);
    }

    public static function compareTabAndFieldOrder(Field $f1, Field $f2): int
    {
        return static::compareTabOrder($f1, $f2) ? static::compareTabOrder($f1, $f2) : static::compareFieldOrder($f1, $f2);
    }

    public static function compareTabOrder(Field $f1, Field $f2): int
    {
        if ($f1->relationLoaded('tab') && $f2->relationLoaded('tab')) {
            $tab1Order = $f1->tab->order ?? 0;
            $tab2Order = $f2->tab->order ?? 0;

            return $tab1Order <=> $tab2Order;
        }

        return 0;
    }

    public static function compareFieldOrder(Field $f1, Field $f2): int
    {
        $field1Order = $f1->order ?? 0;
        $field2Order = $f2->order ?? 0;

        return $field1Order <=> $field2Order;
    }

    public static function tabIsVisible(Field $field): bool
    {
        return is_null($field->tab) || $field->tab->visibleToEntrants;
    }

    public static function tabIsVisibleForManager(Field $field): bool
    {
        return true;
    }

    public function updatesValues(bool $searchable, string $protection): bool
    {
        return $this->searchable != $searchable || $this->protection != $protection || $this->optionable();
    }

    public function getIsRecalculatingAttribute($value): bool
    {
        return $this->hasSlug() ? Cache::has($this->key()) : false;
    }

    public function setIsRecalculatingAttribute(bool $value)
    {
        if (! $this->hasSlug()) {
            return;
        }
        if ($value) {
            Cache::put($this->key(), true, now()->addMinutes(15));
        } else {
            Cache::forget($this->key());
        }
    }

    private function key()
    {
        return 'field-'.(string) $this->slug.'-is-recalculating';
    }

    protected function hasSlug(): bool
    {
        return array_key_exists($this->slugField(), $this->attributes);
    }

    public function getHasValuesAttribute()
    {
        return (bool) ($this->attributes['has_values'] ?? null);
    }

    public function orderByClause(string $table, string $field): string
    {
        if ($this->type === 'formula') {
            // This approach is essential for correctly sorting formulas. We treat numeric values as numbers for proper numeric sorting,
            // and all other values are treated as strings for alphabetical sorting.
            // The first part of the CASE statement determines if the value is numeric, and if so, pads it with zeros for consistent length.
            // The second part handles non-numeric values, leaving them as they are for standard string comparison.
            return "CASE
                WHEN JSON_UNQUOTE( $table.VALUES -> '$.$this->slug' ) REGEXP '^-[0-9]+(\\.[0-9]+)*$|^[0-9]+(\\.[0-9]+)*$'
                    THEN LPAD( JSON_UNQUOTE( $table.VALUES -> '$.$this->slug' ), 10, '0' )
                ELSE
                    JSON_UNQUOTE( $table.VALUES -> '$.$this->slug' ) COLLATE utf8mb4_unicode_ci
            END";
        }

        $modifier = $this->isNumber() ? '+0' : '';

        return "$table.values->'$.$field'$modifier COLLATE utf8mb4_unicode_ci";
    }

    public function isNumber(): bool
    {
        return in_array($this->type, ['numeric', 'currency']);
    }

    public function readOnly(): bool
    {
        if ($this->entryField() || $this->seasonal) {
            return $this->season->isArchived();
        }

        return false;
    }

    public function setTranslatedOptionTextProperty($language, $key, $value)
    {
        if (! is_json($value)) {
            return $this->translated[$language][$key] = $this->mapOldOptionText($value);
        }

        if (is_array(json_decode($value, true))) {
            return $this->translated[$language][$key] = $this->trimWhitespaces(json_decode($value, true));
        }

        $this->translated[$language][$key] = json_decode($value, true);
    }

    protected function trimWhitespaces(array $array): array
    {
        $keys = array_map('trim', array_keys($array));
        $values = array_map(fn($value) => trim($value ?? ''), $array);

        return array_combine($keys, $values);
    }

    protected function mapOldOptionText($optionTexts)
    {
        $optionTexts = explode("\r\n", $optionTexts);
        $result = [];
        foreach ($this->options->keys() ?? [] as $i => $option) {
            $result[$option] = $optionTexts[$i] ?? '';
        }

        return $result;
    }

    public function addTranslation($language, $key, $value)
    {
        if (! isset($this->translated[$language])) {
            $this->translated[$language] = [];
        }

        if (method_exists($this, $method = 'setTranslated'.ucfirst($key).'Property')) {
            $this->$method($language, $key, $value);
        } else {
            $this->translated[$language][$key] = $value;
        }
    }

    public function formType(): string
    {
        return $this->form ? $this->form->type : Form::FORM_TYPE_EMPTY;
    }

    public function scoreable(): bool
    {
        return in_array($this->type, self::SCOREABLE_TYPES) && $this->entryField();
    }

    public function hasScores(): bool
    {
        return $this->options->maxScore() > 0;
    }

    public function needsScoreRecalculation()
    {
        return $this->scoreable() &&
            (
                ($this->isDirty('options') && $this->autoScoring) ||
                $this->isDirty('auto_scoring')
            );
    }

    public function updateTab(int $tabId): void
    {
        $this->tabId = $tabId;
        $this->save();
    }

    public function wordsAndCharactersCount(): string
    {
        $text = $this->maximumWords ? trans_choice('fields.words_count', $this->maximumWords) : '';

        if ($this->maximumWords && $this->maximumCharacters) {
            $text .= '|';
        }

        $text .= $this->maximumCharacters ? trans_choice('fields.characters_count', $this->maximumCharacters) : '';

        return $text;
    }

    public function configurationRelations(): array
    {
        return ['categories', 'roles'];
    }

    public function writable(): bool
    {
        return $this->type === self::TYPE_FORMULA || $this->entrantWriteAccess || $this->userField();
    }

    private function affectsValues(): bool
    {
        $options = $this->attributes['options'] ?? '';
        $original = $this->getOriginal('options');

        $options = collect(is_json($options) ? json_decode($options, true) ?? [] : explode_options($options));
        $original = collect(is_json($original) ? json_decode($original, true) ?? [] : explode_options($original));

        return $options->count() < $original->count() || $options->first(fn($value, $key) => $original->has($key) && $original->get($key) != $value);
    }

    public function wasUpdated()
    {
        return $this->createdAt != $this->updatedAt;
    }
}
