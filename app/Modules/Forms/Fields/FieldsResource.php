<?php

namespace AwardForce\Modules\Forms\Fields;

use Illuminate\Database\Eloquent\Model;

enum FieldsResource: string
{
    case Attachments = 'Attachments';
    case Contributors = 'Contributors';
    case Forms = 'Entries';
    case Organisations = 'Organisations';
    case Referees = 'Referees';
    case Users = 'Users';

    public static function fromResource(Model $model): self
    {
        return match (class_basename($model)) {
            'Attachment' => self::Attachments,
            'Contributor' => self::Contributors,
            'Entry' => self::Forms,
            'Referee' => self::Referees,
            'User' => self::Users,
        };
    }

    public function formable(): bool
    {
        return in_array($this, [self::Forms]);
    }
}
