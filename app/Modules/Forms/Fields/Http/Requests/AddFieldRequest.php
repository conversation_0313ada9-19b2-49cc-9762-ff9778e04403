<?php

namespace AwardForce\Modules\Forms\Fields\Http\Requests;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\FieldsResource;
use AwardForce\Modules\Forms\Fields\Http\Validation\Configurations\ConfigurationValidatorFactory;
use AwardForce\Modules\Forms\Fields\Http\Validation\MaxFileSizeForAccount;
use AwardForce\Modules\Forms\Fields\Http\Validation\MaxSearchable;
use AwardForce\Modules\Forms\Fields\Services\SelectableFileTypes;
use AwardForce\Modules\Forms\Forms\Http\Validation\ValidForm;
use AwardForce\Modules\Payments\Repositories\CurrencyRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Illuminate\Validation\Rule;

class AddFieldRequest extends FieldRequest
{
    public function rules(SeasonRepository $seasons, CurrencyRepository $currencies)
    {
        $seasons = $seasons->getNonArchivedIds();

        $resourceRules = ['required'];

        if ($this->type == 'file') {
            $resourceRules[] = 'not_in:'.Field::RESOURCE_ATTACHMENTS.','.Field::RESOURCE_CONTRIBUTORS;
        }

        $fileTypes = (new SelectableFileTypes)->getAvailableTypes();

        return [
            'seasonId' => 'required|in:'.implode(',', $seasons),
            'formId' => ! FieldsResource::from($this->resource)->formable() ? [] : ['required', new ValidForm($this->get('seasonId'))],
            'translated' => $this->translationValidation(),
            'translated.title.*' => $this->titleRules(),
            'resource' => $resourceRules,
            'searchable' => $this->get('searchable') ? new MaxSearchable(3, $this->resource) : [],
            'protection' => ['required', 'in:'.Field::PROTECTION_STANDARD.','.Field::PROTECTION_ELEVATED.','.Field::PROTECTION_MAXIMUM],
            'type' => ['required'],
            'options' => 'required_if:type,'.implode(',', config('awardforce.fields.options_required')),
            'maximumWords' => 'numeric',
            'maximumCharacters' => 'numeric',
            'minimumWords' => 'numeric',
            'minimumCharacters' => 'numeric',
            'tabId' => 'required_if:resource,Entries,Attachments,Contributors',
            'visibility' => 'array',
            'fileTypes' => ['array', 'in:'.implode(',', $fileTypes)],
            'maxFileSize' => ['numeric', new MaxFileSizeForAccount],
            'minVideoLength' => VideoLengthRules::defaults(),
            'maxVideoLength' => VideoLengthRules::defaults('gte:minVideoLength'),
            'imageDimensionConstraints.*' => ImageDimensionRules::defaults(),
            'imageDimensionConstraints.minWidth' => [Rule::when($this->filled('imageDimensionConstraints.maxWidth'), 'lte:imageDimensionConstraints.maxWidth')],
            'imageDimensionConstraints.minHeight' => [Rule::when($this->filled('imageDimensionConstraints.maxHeight'), 'lte:imageDimensionConstraints.maxHeight')],
            'conditionalVisibility' => 'required_if:conditional,1',
            'conditionalField' => 'int|required_if:conditional,1',
            'conditionalPattern' => 'required_if:conditional,1',
            'conditionalValue' => $this->conditional && array_has(['empty', 'not empty'], $this->conditionalPattern) ? 'required' : '',
            'configuration' => ['json', ConfigurationValidatorFactory::forFieldType($this->type)],
            'currency' => ['required_if:type,currency', 'in:'.$currencies->getAll()->implode('code', ',')],
            'autoTag' => ['sometimes', 'boolean', Rule::requiredIf(fn() => in_array($this->input('type'), Field::OPTIONABLE_TYPES))],
        ];
    }
}
