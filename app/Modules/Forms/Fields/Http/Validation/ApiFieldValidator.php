<?php

namespace AwardForce\Modules\Forms\Fields\Http\Validation;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use Closure;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ApiFieldValidator extends FieldValidator
{
    public function getValidation(Field $field, $prefix = 'values')
    {
        $this->validateEntryField();

        $validation = parent::getValidation($field, $prefix);

        $fieldName = $this->fieldName($prefix, $field->slug);
        $rules = $validation['rules'];
        $messages = $validation['messages'];

        $this->addRules($fieldName, $field, $rules);

        $this->addMessages($fieldName, $field, $messages);

        return array_merge($validation, [
            'rules' => $rules,
            'messages' => $messages,
        ]);
    }

    private function addMessages(string $fieldName, Field $field, array &$messages)
    {
        match ($field->type) {
            'checkbox' => $messages[$fieldName.'.boolean'] = $this->translator->get(
                'validation.boolean',
                ['attribute' => Str::lower($field->title)]
            ),
            'drop-down-list', 'radio', 'checkboxlist' => $messages[$fieldName.'.in'] = $this->translator->get(
                'validation.in',
                ['attribute' => Str::lower($field->title)]
            ),
            'formula' => $messages[$fieldName.'.prohibited'] = $this->translator->get(
                'validation.prohibited',
                ['attribute' => $field->type]
            ),
            default => ''
        };
    }

    private function addRules(string $fieldName, Field $field, array &$rules)
    {
        $rules[$fieldName][] = $this->validateFieldCategory($field);

        match ($field->type) {
            'date' => $rules[$fieldName][] = $this->validateDate($field->title),
            'datetime' => $rules[$fieldName][] = $this->validateDateTime($field->title),
            'time' => $rules[$fieldName][] = $this->validateTime($field->title),
            'country' => $rules[$fieldName][] = $this->validateCountry($field->title),
            'checkbox' => $rules[$fieldName][] = 'boolean',
            'drop-down-list', 'radio' => $rules[$fieldName][] = $this->validateInListCaseInsensitive($field->options->keys(), $field->title),
            'checkboxlist' => $rules[$fieldName][] = $this->validateCheckboxList($field->options->keys(), $field->title),
            'formula' => $rules[$fieldName][] = 'prohibited',
            default => ''
        };
    }

    protected function validateDate(string $fieldName)
    {
        return new DateValidator($fieldName);
    }

    protected function validateDateTime(string $fieldName)
    {
        return new DateTimeValidator($fieldName);
    }

    protected function validateTime(string $fieldName)
    {
        return new TimeValidator($fieldName);
    }

    /**
     * @return int|mixed|null
     */
    public function categoryId(): mixed
    {
        if ($this->request->has('category')) {
            return id_from_slug($this->request->input('category'), app(CategoryRepository::class));
        }

        if ($slug = $this->request->route('slug')) {
            return app(EntryRepository::class)->getBySlug($slug)->categoryId;
        }

        if ($slug = $this->request->input('entry')) {
            return app(EntryRepository::class)->getBySlug($slug)->categoryId;
        }

        return null;
    }

    protected function validateInList($list)
    {
    }

    protected function validateInListCaseInsensitive($list, string $title = '')
    {
        try {
            return new InListValidator($list, $title);
        } catch (\Throwable $exception) {
            dd($exception->getMessage(), $list, $title);
        }
    }

    protected function validateCheckboxList($list, string $title)
    {
        return new CheckboxListValidator($list, $title);
    }

    protected function setDateFieldRequired(Field $field, $fieldName, array &$rules, array &$messages)
    {
        $rules[$fieldName][] = 'required';
        $messages[$fieldName.'.required'] = $this->translator->get(
            'validation.required',
            ['attribute' => Str::lower($field->title)]
        );
    }

    protected function validateCountry(string $title)
    {
        return function ($attribute, $value, $fail) use ($title) {
            if (! in_array(strtoupper($value), array_keys(config('countries.list')))) {
                $fail("Field {$title} is not a valid country code.");
            }
        };
    }

    protected function tabId($prefix = 'values')
    {
        if ($tabSlug = $this->request->route('tabSlug', $this->request->input('tab'))) {
            return id_from_slug($tabSlug, app(TabRepository::class));
        }
    }

    /**
     * Override the default value method on the parent class
     */
    protected function appliesToCategory(Field $field): bool
    {
        return true;
    }

    /**
     * If the request has an entry_fields key, then we need to validate that it is an array before we can
     * run all the validation rules.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    private function validateEntryField(): void
    {
        if ($this->request->has('entry_fields')) {
            $validator = Validator::make(
                [Vertical::replace('entry_fields') => $this->request->entry_fields],
                [Vertical::replace('entry_fields') => 'array'],
                ['array' => trans('validation.array', ['attribute' => Vertical::replace('entry_fields')])]
            );

            $validator->validate();
        }
    }

    /**
     * Creates validation to check if Field belongs to the entry's category or the request's category
     */
    private function validateFieldCategory(Field $field): Closure
    {
        return function ($attribute, $value, $fail) use ($field) {
            if ($field->appliesToAllCategories()) {
                return;
            }

            if ($field->isOnCategory((int) $this->categoryId())) {
                return;
            }

            $fail(trans('validation.field_not_available_for_category'));
        };
    }
}
