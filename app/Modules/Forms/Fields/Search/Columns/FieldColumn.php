<?php

namespace AwardForce\Modules\Forms\Fields\Search\Columns;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Exceptions\InvalidFieldValueFormat;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Facades\Platform\Strings\Output;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Search\Column;
use Platform\Search\Columns\HasSelectStrategy;
use Platform\Search\Defaults;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Services\ColumnSelectStrategies\JsonSelectStrategy;
use Platform\Search\Services\ColumnSelectStrategies\SelectStrategy;
use Tectonic\LaravelLocalisation\Facades\Translator;

abstract class FieldColumn implements \Platform\Search\FieldColumn, ApiField, Column, HasSelectStrategy
{
    private $tables = [
        Form::FORM_TYPE_ENTRY => [
            Field::RESOURCE_FORMS => 'entries',
            Field::RESOURCE_ATTACHMENTS => 'attachments',
            Field::RESOURCE_USERS => 'memberships',
            Field::RESOURCE_CONTRIBUTORS => 'contributors',
        ],
        Form::FORM_TYPE_REPORT => [
            Field::RESOURCE_FORMS => 'grant_reports',
            Field::RESOURCE_ATTACHMENTS => 'attachments',
            Field::RESOURCE_USERS => 'memberships',
            Field::RESOURCE_CONTRIBUTORS => 'contributors',
        ],
        Form::FORM_TYPE_EMPTY => [
            Field::RESOURCE_USERS => 'memberships',
        ],
    ];

    const NOT_ON_CATEGORY = '—';

    private array $formTypes = [
        '' => Form::FORM_TYPE_EMPTY,
    ];
    private static array $idTypeMap = [];

    /*** @var Field The field that the column represents. */
    protected $field;

    /** @var string */
    protected $foreignId;

    /** @var Defaults */
    protected $defaults;

    public function __construct(Field $field, string $foreignId, ?Defaults $defaults = null)
    {
        $this->field = $field;
        $this->foreignId = $foreignId;
        $this->defaults = $defaults ?: new Defaults('export');
    }

    /**
     * Return the field that should be used to match field values for a specific resource.
     *
     * @return mixed
     */
    public function foreignId(): string
    {
        return $this->foreignId;
    }

    /**
     * Title for the column - used for headers.
     *
     * @return string|HtmlString
     */
    public function title()
    {
        $this->field = translate($this->field);

        return lang($this->field, 'title');
    }

    /**
     * Name of the column. This should be unique.
     */
    public function name(): string
    {
        return $this->field->slug;
    }

    public function tooltip(): HtmlString|string
    {
        if (! $this->field->conditional) {
            return '';
        }

        return new HtmlString('<help-icon content="'.trans('fields.table.columns.conditional.tooltip', ['other_field' => Output::html(lang(Translator::shallow($this->field->conditionalField), 'title'))]).'"></help-icon>');
    }

    /**
     * When it comes to field columns, the slug is a unique string value that can be used for fetching the field from the query.
     *
     * @return string|null
     */
    public function field()
    {
        return $this->name();
    }

    abstract protected function processRawValue($rawValue);

    /**
     * accessor method used for retrieving data
     * in case of any error in processValue callback it falls back to an empty string
     *
     * @return mixed|string
     */
    public function value($record)
    {
        $categoryId = $record->categoryId ?? $record->entry?->categoryId ?? $record->submittable?->getCategoryId();

        if ($categoryId && ! $this->field->isOnCategory($categoryId)) {
            return static::NOT_ON_CATEGORY;
        }

        $rawValue = $this->rawValue($record);
        try {
            return $this->processRawValue($rawValue);
        } catch (InvalidFieldValueFormat $exception) {
            $this->logValueException($exception, $record, $rawValue);

            return $exception->getFallBackValue();
        }
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|HtmlString
     */
    public function html($record)
    {
        return $this->value($record);
    }

    /**
     * Returns the field that this field column is based on.
     */
    public function baseField(): Field
    {
        return $this->field->load('form:id,type', 'sets:id');
    }

    /**
     * Returns a Defaults value object that consists of a value representing the views that the column is available on.
     */
    public function default(): Defaults
    {
        return $this->defaults;
    }

    /**
     * Return true if the column is visible, a good place to check for permissions.
     */
    public function visible(): bool
    {
        return true;
    }

    /**
     * Return true if the column is fixed and cannot be rearranged.
     */
    public function fixed(): bool
    {
        return false;
    }

    /**
     * Return the column visibility priority. Give columns with particularly important information a higher
     * visibility priority. A higher visibility priority makes the column stay visible while columns with
     * lower visibility priority get hidden when they do not fit into the available horizontal space.
     */
    public function priority(): int
    {
        return 10000;
    }

    /**
     * Receives all the search filters available for search, and will state which ones it requires in order to be used.
     */
    public function dependencies(): Collection
    {
        return collect([
            ColumnFilter::class,
        ]);
    }

    /**
     * Custom fields are generally not sortable, due to the performance implications.
     */
    public function sortable(): bool
    {
        return $this->baseField()->protection === Field::PROTECTION_STANDARD;
    }

    /**
     * Return the value required in $record.
     *
     * @return mixed
     */
    protected function rawValue($record)
    {
        $name = strtolower($this->name());

        return $record->{$name};
    }

    /**
     * Return true if the field has encryption enabled
     */
    public function encrypted(): bool
    {
        return ! $this->field->standardProtection();
    }

    /**
     * Returns the field's type for this column.
     */
    public function type(): string
    {
        return $this->field->type;
    }

    private function logValueException($exception, $record, $rawValue)
    {
        \Log::notice(
            'FieldColumn::value conversion because of: '.trim($exception->getMessage()),
            [
                'recordId' => $record->id ?? '---',
                'recordClass' => is_object($record) ? get_class($record) : gettype($record),
                'fieldId' => $this->baseField()->id,
                'fieldType' => $this->baseField()->type,
                'fieldName' => $this->baseField()->name,
                'rawValue' => $rawValue,
            ]
        );
    }

    /**
     * gets a select strategy for column
     */
    public function selectStrategy(): SelectStrategy
    {
        $slug = $this->field();
        $table = $this->getTable();

        return $table ? new class($slug, $table) extends JsonSelectStrategy
        {
            private $data;

            public function __construct(string $slug, string $table)
            {
                $this->data = [
                    'values' => ["'$slug'" => "{$table}.values->'$.$slug'"],
                    'protected' => ["'$slug'" => "{$table}.protected->'$.$slug'"],
                ];
            }

            protected function getData($name)
            {
                return $this->data[$name];
            }

            protected function getNames(): array
            {
                return array_keys($this->data);
            }
        }
        : JsonSelectStrategy::noop();
    }

    public function apiFormat($value)
    {
        return ['value' => $value];
    }

    public function getTable()
    {
        return $this->tables[$this->formType() ?? Form::FORM_TYPE_ENTRY][$this->baseField()->resource] ?? false;
    }

    protected function orderOptions($values)
    {
        return app(ValuesService::class)->orderOptionable($this->field, $values);
    }

    protected function formType(): string
    {
        return $this->resolveFormTypeFromId((string) $this->baseField()->formId);
    }

    private function resolveFormTypeFromId(string $formId): string
    {
        return $this->formTypes[$formId] ??= $this->resolveFormTypeFromMap($formId);
    }

    private function resolveFormTypeFromMap(string $formId): string
    {
        return self::$idTypeMap[$formId] ??= $this->baseField()->formType();
    }
}
