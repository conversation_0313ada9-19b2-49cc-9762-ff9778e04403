<?php

namespace AwardForce\Modules\Forms;

use AwardForce\Modules\Forms\Fields\FieldsServiceProvider;
use AwardForce\Modules\Forms\Formables\FormableServiceProvider;
use AwardForce\Modules\Forms\Forms\FormsServiceProvider;
use AwardForce\Modules\Forms\Tabs\TabsServiceProvider;
use Illuminate\Support\AggregateServiceProvider;

class FormsAggregateServiceProvider extends AggregateServiceProvider
{
    protected $providers = [
        FieldsServiceProvider::class,
        FormableServiceProvider::class,
        FormsServiceProvider::class,
        TabsServiceProvider::class,
    ];
}
