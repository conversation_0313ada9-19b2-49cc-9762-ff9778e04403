<?php

namespace AwardForce\Modules\Forms\Formables\Bus;

use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Forms\Forms\FormId;

// Todo: form id and type do not apply to all formables (organisations, memberships)
final readonly class CreateFormable
{
    public function __construct(private int $formId, private string $formType, private array $requestData)
    {
    }

    public function formId(): FormId
    {
        return new FormId($this->formId);
    }

    public function resource(): Resource
    {
        return Resource::from($this->formType);
    }

    public function requestData(): array
    {
        return $this->requestData;
    }

    public function values(): array
    {
        return $this->requestData['values'];
    }
}
