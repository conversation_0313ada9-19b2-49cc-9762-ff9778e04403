<?php

namespace AwardForce\Modules\Forms\Formables\Bus;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Forms\FieldValues\RequestToFieldValues;
use AwardForce\Modules\Forms\FieldValues\Values\Text;
use AwardForce\Modules\Forms\Formables\Boundary\FormableManager;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use Eloquence\Behaviours\Slug;
use Mockery as m;
use Tests\LightUnitTestCase;

final class CreateFormableHandlerTest extends LightUnitTestCase
{
    public function testFormablesCanBeCreatedAlongWithAssociatedFieldValues()
    {
        $field = new Field;
        $field->slug = Slug::fromId(1);
        $field->type = 'text';

        $command = new CreateFormable(1, 'entry', ['values' => [
            (string) $field->slug => 'text',
        ]]);

        $fields = m::mock(FieldRepository::class);
        $fields->shouldReceive('fields')
            ->with(['id', 'protection', 'slug', 'type'])
            ->andReturnSelf();
        $fields->shouldReceive('slugs')->with([(string) $field->slug])->andReturnSelf();
        $fields->shouldReceive('get')->andReturn(collect([$field]));
        $requestToFieldValues = new RequestToFieldValues($fields);

        $manager = m::mock(FormableManager::class);
        $manager->shouldReceive('create')
            ->with(Resource::Entry, m::on(function (FieldValuesCollection $fieldValues) use ($field) {
                $this->assertArrayHasKey((string) $field->slug, $fieldValues);
                $this->assertInstanceOf(Text::class, $fieldValues[(string) $field->slug]);

                $fieldData = $fieldValues[(string) $field->slug]->toArray();
                $this->assertEquals((string) $field->slug, $fieldData['key']);
                $this->assertEquals('text', $fieldData['value']);

                return true;
            }), $command->requestData());

        $handler = new CreateFormableHandler($manager, $requestToFieldValues);
        $handler->handle($command);
    }
}
