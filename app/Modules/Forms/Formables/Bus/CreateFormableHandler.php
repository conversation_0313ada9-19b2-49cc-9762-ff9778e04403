<?php

namespace AwardForce\Modules\Forms\Formables\Bus;

use AwardForce\Modules\Forms\FieldValues\RequestToFieldValues;
use AwardForce\Modules\Forms\Formables\Boundary\FormableManager;

final class CreateFormableHandler
{
    public function __construct(private FormableManager $formableManager, private RequestToFieldValues $requestToFieldValues)
    {
    }

    public function handle(CreateFormable $command): void
    {
        $fieldValues = $this->requestToFieldValues->fromRequest($command->values());
        // @todo calculate formula field values
        // $fieldValues = $this->requestToFieldValues->fromRequest($command->values());
        // pipe($values)->through([
        //    new NormaliseValues
        //    new FormulaDependencies,
        //    new CalculateFormulas,
        //    new ScoreFields,
        //    new DiffValues - works through payload, discovers which values have actually changed.
        //    new EncryptValues,
        // ])

        $this->formableManager->create(
            $command->resource(),
            $fieldValues,
            $command->requestData()
        );
    }
}
