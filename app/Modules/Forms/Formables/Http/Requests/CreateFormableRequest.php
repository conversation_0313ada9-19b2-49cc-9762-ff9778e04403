<?php

namespace AwardForce\Modules\Forms\Formables\Http\Requests;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;

class CreateFormableRequest extends FormRequest
{
    public function resource(): Resource
    {
        return Resource::from($this->route('resource'));
    }

    public function form(): Form
    {
        // @todo: change ->first() to ->require()
        return app(FormRepository::class)
            ->fields(['id', 'type'])
            ->selectedSeason(SeasonFilter::getId())
            ->slug($this->route('formSlug'))
            ->first();
    }

    public function rules(): array
    {
        return [];
    }
}
