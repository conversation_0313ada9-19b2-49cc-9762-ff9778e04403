<?php

namespace AwardForce\Modules\Forms\Formables\Http\Controllers;

use AwardForce\Modules\Forms\Formables\Bus\CreateFormable;
use AwardForce\Modules\Forms\Formables\Http\Requests\CreateFormableRequest;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Platform\Http\Controller;

class FormableController extends Controller
{
    use DispatchesJobs;

    public function create(CreateFormableRequest $request)
    {
        $this->dispatch(new CreateFormable($request->form->id, $request->form->type, $request->input()));
    }
}
