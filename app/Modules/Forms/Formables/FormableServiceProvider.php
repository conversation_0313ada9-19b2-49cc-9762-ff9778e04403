<?php

namespace AwardForce\Modules\Forms\Formables;

use AwardForce\Modules\Forms\Formables\Boundary\FormableProvider;
use Illuminate\Support\ServiceProvider;

class FormableServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->registerProvider();
    }

    private function registerProvider(): void
    {
        $this->app->singleton(FormableProvider::class, fn() => new FormableProvider);
    }
}
