<?php

namespace AwardForce\Modules\Forms\Formables\Boundary;

use AwardForce\Modules\Forms\Forms\Enums\Resource;
use Mockery as m;
use Tests\UnitTestCase;

class FormableProviderTest extends UnitTestCase
{
    public function testRegisteredHandlersCanBeCalledUponToProvideNecessaryObjects(): void
    {
        $formableProvider = new FormableProvider;
        $formableProvider->registerProvider($this->emptyFormableProvider());

        $formable = $formableProvider->repository(Resource::Empty);

        $this->assertInstanceOf(FormableAggregateRepository::class, $formable);
    }

    public function testExceptionThrownWhenNoProviderAvailable(): void
    {
        $formableProvider = new FormableProvider;

        $this->expectException(NoProviderAvailable::class);

        $formableProvider->repository(Resource::Empty);
    }

    private function emptyFormableProvider(): HandlesFormables
    {
        return new class implements HandlesFormables
        {
            public function repository(): FormableAggregateRepository
            {
                return m::mock(FormableAggregateRepository::class)->makePartial();
            }

            public function handles(Resource $resource): bool
            {
                return $resource === Resource::Empty;
            }
        };
    }
}
