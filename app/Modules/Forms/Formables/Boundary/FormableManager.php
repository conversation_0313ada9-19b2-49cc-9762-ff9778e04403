<?php

namespace AwardForce\Modules\Forms\Formables\Boundary;

use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Forms\FieldValues\Values;
use AwardForce\Modules\Forms\FieldValues\Values\FieldValue;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use EventSauce\EventSourcing\AggregateRootId;
use Platform\Events\EventDispatcher;

/**
 * Acts as a domain service class, an entry point into the Field Values subdomain. Responsible for the creation,
 * updating and removal of field values, it works by first calling upon Formables to provide the necessary
 * object in order to manage said values.
 */
class FormableManager
{
    use EventDispatcher;

    public function __construct(
        private FormableProvider $formableProvider,
    ) {
    }

    /**
     * Create a new formable resource and associated field values.
     *
     * @param  FieldValue[]  $fieldValues
     */
    public function create(Resource $resource, FieldValuesCollection $fieldValues, array $requestData): AggregateRootId
    {
        $aggregateRootId = $this->formableProvider->repository($resource)
            ->transaction(function (FormableAggregateRepository $repository) use ($requestData, $fieldValues) {
                // Create a formable aggregate
                $aggregate = $repository->create($requestData);
                // Create field values
                $aggregate->createFieldValues($fieldValues);
                // Persist and release events
                $repository->save($aggregate);

                return $aggregate->aggregateRootId();
            });

        return $aggregateRootId;
    }
}
