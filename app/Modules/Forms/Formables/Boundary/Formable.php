<?php

namespace AwardForce\Modules\Forms\Formables\Boundary;

use AwardForce\Modules\Forms\Formables\ResourceId;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Forms\Forms\FormId;

interface Formable
{
    public function formableResource(): Resource;

    public function formableResourceId(): ResourceId;

    public function formableFormId(): FormId;

    /**
     * Returns data on the formable that has changed outside the Formable's scope. Eg. created_at or entry title.
     *
     * @todo come up with a better name!
     */
    public function changed(): array;
}
