<?php

namespace AwardForce\Modules\Forms\Formables\Boundary;

use AwardForce\Library\EventSourcing\SnapshottableAggregateRoot;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use Illuminate\Contracts\Support\Arrayable;

interface FormableAggregate extends Arrayable, SnapshottableAggregateRoot
{
    public function createFieldValues(FieldValuesCollection $fieldValues): void;
}
