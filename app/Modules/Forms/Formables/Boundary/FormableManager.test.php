<?php

namespace AwardForce\Modules\Forms\Formables\Boundary;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\FieldType;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Forms\FieldValues\Key;
use AwardForce\Modules\Forms\FieldValues\Values\FieldValue;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Organisation;
use Tests\IntegratedTestCase;

class FormableManagerTest extends IntegratedTestCase
{
    private FormableManager $manager;

    public function init()
    {
        $this->manager = app(FormableManager::class);
    }

    public function testItCreatesOrganisationAndStoresFieldValues(): void
    {
        $this->markTestSkipped('Skipped until we decide to use a single FormableManager for all formables');
        $this->setupUserWithRole('Entrant');

        $field1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'text']);
        $field2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'textarea']);

        $requestData = [
            'administratorId' => $this->user->id,
            'formType' => Resource::Organisation,
            'name' => 'Organisation title',
            'domains' => [],
        ];

        $fieldValues = new FieldValuesCollection([
            FieldValue::for(FieldType::from($field1->type), Key::from((string) $field1->slug), 'value1'),
            FieldValue::for(FieldType::from($field2->type), Key::from((string) $field2->slug), 'value2'),
        ]);

        $aggregateRootId = $this->manager->create(Resource::Organisation, $fieldValues, $requestData);

        /** @var Organisation $aggregate */
        $aggregate = app(OrganisationRepository::class)->retrieve($aggregateRootId);

        // Aggregate assertions
        $this->assertInstanceOf(Organisation::class, $aggregate);

        $organisation = app(OrganisationProjectionRepository::class)->forAggregateRoot($aggregateRootId)
            ->fields(['name', 'values'])
            ->first();

        // Organisation assertions
        $this->assertEquals($requestData['name'], $organisation->name);

        //  Field values assertions
        $this->assertIsArray($organisation->values);
        $this->assertCount(2, $organisation->values);
        $this->assertEqualsCanonicalizing(
            [
                (string) $field1->slug => 'value1',
                (string) $field2->slug => 'value2',
            ],
            $organisation->values
        );
    }

    //    public function testItCreatesEntryAndStoresFieldValues(): void
    //    {
    //        $this->setupUserWithRole('Entrant');
    //        $chapter = $this->muffin(Chapter::class);
    //        $category = $this->muffin(Category::class);
    //
    //        $field1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'text']);
    //        $field2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => 'textarea']);
    //
    //        $requestData = [
    //            'userId' => $this->user->id,
    //            'formType' => Resource::Organisation,
    //            'title' => 'Entry title',
    //            'chapterId' => $chapter->id,
    //            'categoryId' => $category->id,
    //            'seasonId' => $category->seasonId,
    //            'formId' => $category->formId,
    //        ];
    //
    //        $fieldValues = [
    //            FieldValue::for(FieldType::from($field1->type), Key::from((string) $field1->slug), 'value1'),
    //            FieldValue::for(FieldType::from($field2->type), Key::from((string) $field2->slug), 'value2'),
    //        ];
    //
    //        $entry = $this->manager->create(Resource::Entry, $fieldValues, $requestData)->fresh();
    //
    //        /** @var FieldValues $aggregate */
    //        $aggregate = $this->aggregateRepository->retrieve(FieldValuesId::create(Resource::Entry, new ResourceId($entry->id)));
    //
    //        // Aggregate assertions
    //        $this->assertInstanceOf(FieldValues::class, $aggregate);
    //
    //        // Entry assertions
    //        $this->assertEquals($requestData['title'], $entry->title);
    //        $this->assertEquals($requestData['chapterId'], $entry->chapterId);
    //        $this->assertEquals($requestData['categoryId'], $entry->categoryId);
    //        $this->assertEquals($requestData['seasonId'], $entry->seasonId);
    //        $this->assertEquals($requestData['formId'], $entry->formId);
    //
    //        //         Field values assertions
    //        $this->assertIsArray($entry->values);
    //        $this->assertCount(2, $entry->values);
    //        $this->assertEqualsCanonicalizing(
    //            [
    //                (string) $field1->slug => 'value1',
    //                (string) $field2->slug => 'value2',
    //            ],
    //            $entry->values
    //        );
    //    }
}
