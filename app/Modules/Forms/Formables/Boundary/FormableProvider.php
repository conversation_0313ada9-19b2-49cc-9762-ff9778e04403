<?php

namespace AwardForce\Modules\Forms\Formables\Boundary;

use AwardForce\Modules\Forms\Forms\Enums\Resource;

class FormableProvider
{
    /**
     * @var HandlesFormables[]
     */
    private array $providers = [];

    public function registerProvider(HandlesFormables $provider): void
    {
        $this->providers[] = $provider;
    }

    public function repository(Resource $resource): FormableAggregateRepository
    {
        foreach ($this->providers as $provider) {
            if ($provider->handles($resource)) {
                return $provider->repository();
            }
        }

        throw new NoProviderAvailable($resource);
    }
}
