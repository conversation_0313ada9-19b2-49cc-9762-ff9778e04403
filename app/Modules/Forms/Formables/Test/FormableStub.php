<?php

namespace AwardForce\Modules\Forms\Formables\Test;

use AwardForce\Modules\Forms\Formables\Boundary\Formable;
use AwardForce\Modules\Forms\Formables\ResourceId;
use AwardForce\Modules\Forms\Forms\Enums\Resource;
use AwardForce\Modules\Forms\Forms\FormId;

class FormableStub implements Formable
{
    public function formableResource(): Resource
    {
        return Resource::Entry;
    }

    public function formableResourceId(): ResourceId
    {
        return new ResourceId(2);
    }

    public function formableFormId(): FormId
    {
        return new FormId(1);
    }

    public function changed(): array
    {
        return [];
    }
}
