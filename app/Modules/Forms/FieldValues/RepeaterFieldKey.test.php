<?php

namespace AwardForce\Modules\Forms\FieldValues;

use PHPUnit\Framework\TestCase;

final class RepeaterFieldKeyTest extends TestCase
{
    public function testRepeaterFieldKeysCanBeInitialised()
    {
        $key = new RepeaterFieldKey([1, 2, 3]);

        // This might seem strange, but repeater field initialisation has its own logic around array structures, so
        // we're just testing to ensure that the class can be initialised with valid arguments.
        $this->assertInstanceOf(RepeaterFieldKey::class, $key);
    }

    public function testExceptionIsThrownWhenTheArrayStructureIsInvalid()
    {
        $this->expectException(\Webmozart\Assert\InvalidArgumentException::class);

        new RepeaterFieldKey([1]);
    }

    public function testRepeaterKeyValuesCanBeExtracted()
    {
        $key = new RepeaterFieldKey([1, 2, 3]);

        $this->assertSame(1, $key->repeaterFieldId());
        $this->assertSame(2, $key->repeaterBlock());
        $this->assertSame(3, $key->fieldId());
    }
}
