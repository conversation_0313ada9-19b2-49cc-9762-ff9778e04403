<?php

namespace AwardForce\Modules\Forms\FieldValues\Boundary;

use AwardForce\Modules\Forms\FieldValues\Values\FieldValue;
use Illuminate\Support\Collection;

class FieldValuesCollection extends Collection
{
    public static function fromArray(array $fieldValues): self
    {
        return new self(array_map(fn($fieldValueData) => FieldValue::fromArray($fieldValueData), $fieldValues));
    }

    public function toArrayWithKeys(): array
    {
        return $this->mapWithKeys(fn(FieldValue $fieldValue) => [$fieldValue->key()->serialise() => $fieldValue->value()->value])
            ->toArray();
    }
}
