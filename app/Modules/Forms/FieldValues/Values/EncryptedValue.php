<?php

namespace AwardForce\Modules\Forms\FieldValues\Values;

use Platform\Encryption\Encrypter;

final readonly class EncryptedValue
{
    public function __construct(public string $value)
    {
    }

    public static function fromRaw(RawValue $value): self
    {
        $encrypted = app(Encrypter::class)->maximum($value->value);

        return new self($encrypted);
    }

    public function decrypt(): mixed
    {
        return app(Encrypter::class)->decrypt($this->value);
    }

    public function toRaw(): RawValue
    {
        return new RawValue($this->decrypt());
    }
}
