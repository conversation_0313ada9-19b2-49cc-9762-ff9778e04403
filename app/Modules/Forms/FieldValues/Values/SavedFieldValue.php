<?php

namespace AwardForce\Modules\Forms\FieldValues\Values;

/**
 * Represents a saved field value from the event store. Because such field values have operations they simply
 * cannot do, this ensures that said operations can either throw exceptions or return sensible defaults.
 * Saved field values are not designed to work with, as they are purely a rehydration implementation
 * for the aggregates.
 */
class SavedFieldValue extends FieldValue
{
}
