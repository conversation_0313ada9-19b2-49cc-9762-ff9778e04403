<?php

namespace AwardForce\Modules\Forms\FieldValues\Values;

use AwardForce\Modules\Forms\Fields\Services\FieldType;
use AwardForce\Modules\Forms\FieldValues\Key;
use AwardForce\Modules\Forms\FieldValues\SerialisableKey;
use AwardForce\Modules\Forms\Forms\Enums\Protection;
use Illuminate\Contracts\Support\Arrayable;
use Platform\Encryption\Encrypter;

abstract class FieldValue implements Arrayable
{
    protected function __construct(private SerialisableKey $key, private RawValue|EncryptedValue $value)
    {
    }

    public static function for(FieldType $fieldType, Key $key, mixed $value): static
    {
        return new (__NAMESPACE__.'\\'.$fieldType->name)($key, new RawValue(self::normalise($value)));
    }

    public function key(): SerialisableKey
    {
        return $this->key;
    }

    public function value(): RawValue|EncryptedValue
    {
        return $this->value;
    }

    public function protect(Protection $protection): void
    {
        $encrypter = app(Encrypter::class);

        $this->value = match ($protection) {
            Protection::Elevated => new EncryptedValue($encrypter->encrypt($this->value->value)),
            Protection::Maximum => new EncryptedValue($encrypter->maximum($this->value->value)),
            default => new RawValue($this->value->value),
        };
    }

    public static function fromArray(array $fieldValue): static
    {
        return new SavedFieldValue(
            Key::from($fieldValue['key']),
            new RawValue($fieldValue['value']),
        );
    }

    public function toArray(): array
    {
        return [
            'key' => $this->key->serialise(),
            'value' => $this->value->value,
        ];
    }

    /**
     * Value normalisation is used to ensure that values provided over the wire are safe to store in our persistence
     * layer. Each field type should have its own requirements around normalisation.
     */
    protected static function normalise(mixed $value): mixed
    {
        return $value;
    }

    public function raw(): mixed
    {
        if ($this->value instanceof EncryptedValue) {
            return $this->value->toRaw();
        }

        return $this->value->value;
    }
}
