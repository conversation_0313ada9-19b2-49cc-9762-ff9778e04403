<?php

namespace AwardForce\Modules\Forms\FieldValues\Values;

use AwardForce\Modules\Forms\Fields\Services\FieldType;
use AwardForce\Modules\Forms\FieldValues\FieldKey;
use AwardForce\Modules\Forms\FieldValues\RepeaterFieldKey;
use Eloquence\Behaviours\Slug;
use Tests\LightUnitTestCase;

class FieldValueTest extends LightUnitTestCase
{
    public function testFieldValueCanBeInstantiatedAsARequiredType()
    {
        $this->assertInstanceOf(Checkbox::class, FieldValue::for(FieldType::Checkbox, new FieldKey(Slug::fromId(1)), '1'));
    }

    public function testGettersReturnExpectedValues()
    {
        $slug1 = Slug::fromId(1);
        $slug2 = Slug::fromId(2);
        $fieldValue = FieldValue::fromArray(['key' => $slug1.'.1.'.$slug2, 'value' => 'lksjdf']);

        $this->assertInstanceOf(RepeaterFieldKey::class, $fieldValue->key());
        $this->assertSame('lksjdf', $fieldValue->value()->value);
    }
}
