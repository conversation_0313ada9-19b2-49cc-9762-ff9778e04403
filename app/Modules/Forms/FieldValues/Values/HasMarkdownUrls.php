<?php

namespace AwardForce\Modules\Forms\FieldValues\Values;

trait HasMarkdownUrls
{
    /**
     * When passed markdown, ensures that any links provided as part of the content are
     * forced to https URLs to prevent potential attack vectors.
     */
    protected static function normalise(mixed $value): string
    {
        if (! is_string($value)) {
            return '';
        }

        return preg_replace_callback("/\[(.*?)]\s*+\(http?:\/\/(\S+\.\S+)\)/i", static fn($matches) => '['.$matches[1].'](https://'.$matches[2].')', $value);
    }
}
