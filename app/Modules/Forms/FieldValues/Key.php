<?php

namespace AwardForce\Modules\Forms\FieldValues;

use Illuminate\Support\Str;

/**
 * Keys for field values can come in many flavours, the most common of which is a single integer representing a
 * single field id. However, in the case of repeater-type fields, the key could consist of numerous slugs as
 * well as indexes within the repeater blocks. For this reason, keys can be different depending on the
 * structure of the passed ids.
 */
class Key
{
    public static function from(string $key): FieldKey|RepeaterFieldKey
    {
        return Str::of($key)->contains('.') ? RepeaterFieldKey::unserialise($key) : FieldKey::unserialise($key);
    }
}
