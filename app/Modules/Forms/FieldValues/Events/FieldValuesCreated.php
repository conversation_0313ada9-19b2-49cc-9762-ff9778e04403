<?php

namespace AwardForce\Modules\Forms\FieldValues\Events;

use AwardForce\Library\EventSourcing\SourcedEvent;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use EventSauce\EventSourcing\Serialization\SerializablePayload;

readonly class FieldValuesCreated implements SerializablePayload, SourcedEvent
{
    public function __construct(public FieldValuesCollection $fieldValues)
    {
    }

    public function toPayload(): array
    {
        return [
            'fieldValues' => $this->fieldValues->toArray(),
        ];
    }

    public static function fromPayload(array $payload): static
    {
        return new self(
            FieldValuesCollection::fromArray($payload['fieldValues']),
        );
    }
}
