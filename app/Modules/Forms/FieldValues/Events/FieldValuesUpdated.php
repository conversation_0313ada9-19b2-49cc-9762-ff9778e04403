<?php

namespace AwardForce\Modules\Forms\FieldValues\Events;

use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;

readonly class FieldValuesUpdated
{
    public function __construct(public FieldValuesCollection $fieldValues)
    {
    }

    public function toPayload(): array
    {
        return [
            'fieldValues' => $this->fieldValues->toArray(),
        ];
    }

    public static function fromPayload(array $payload): static
    {
        return new self(
            FieldValuesCollection::fromArray($payload['fieldValues']),
        );
    }
}
