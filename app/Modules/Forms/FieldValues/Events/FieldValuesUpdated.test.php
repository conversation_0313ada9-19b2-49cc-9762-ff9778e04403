<?php

namespace AwardForce\Modules\Forms\FieldValues\Events;

use Eloquence\Behaviours\Slug;
use Tests\LightUnitTestCase;

class FieldValuesUpdatedTest extends LightUnitTestCase
{
    public function testEventCanBeConvertedToAndRestoredFromPayloads(): void
    {
        $slug = Slug::fromId(12);
        $event = FieldValuesUpdated::fromPayload([
            'fieldValues' => [
                [
                    'key' => (string) $slug,
                    'value' => 'value',
                ],
            ],
        ]);

        $this->assertSame($event->toPayload()['fieldValues'], [
            [
                'key' => (string) $slug,
                'value' => 'value',
            ],
        ]);
    }
}
