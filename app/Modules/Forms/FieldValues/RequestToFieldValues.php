<?php

namespace AwardForce\Modules\Forms\FieldValues;

use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Services\FieldType;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Forms\FieldValues\Values\FieldValue;
use Illuminate\Support\Arr;

final class RequestToFieldValues
{
    public function __construct(private FieldRepository $fields)
    {
    }

    /**
     * Maps an array of values from the request, to their associated field value objects.
     */
    public function fromRequest(array $values): FieldValuesCollection
    {
        $fields = $this->fields->fields(['id', 'protection', 'slug', 'type'])->slugs(array_keys($values))->get()->keyBy('slug');

        return (new FieldValuesCollection(
            Arr::map($values, fn($value, $slug) => FieldValue::for(FieldType::from($fields[$slug]->type), Key::from($slug), $value))
        ))->keyBy(fn(FieldValue $fieldValue) => $fieldValue->key()->serialise());
    }
}
