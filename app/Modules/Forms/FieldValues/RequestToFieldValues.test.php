<?php

namespace AwardForce\Modules\Forms\FieldValues;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Forms\FieldValues\Values\FieldValue;
use Eloquence\Behaviours\Slug;
use Mockery as m;
use Tests\LightUnitTestCase;

class RequestToFieldValuesTest extends LightUnitTestCase
{
    public function testRequestDataCanBeMappedToFieldValueObjects()
    {
        $field1 = new Field;
        $field1->id = 1;
        $field1->slug = Slug::fromId(1);
        $field1->type = 'textarea';
        $field1->protection = 'standard';

        $field2 = new Field;
        $field2->id = 2;
        $field2->slug = Slug::fromId(2);
        $field2->type = 'text';
        $field2->protection = 'maximum';

        $mockFieldRepository = m::mock(FieldRepository::class);
        $mockFieldRepository->shouldReceive('fields')->andReturnSelf();
        $mockFieldRepository->shouldReceive('slugs->get')->andReturn(collect([$field1, $field2]));

        $service = new RequestToFieldValues($mockFieldRepository);

        $fieldValues = $service->fromRequest([(string) $field1->slug => 'value1', (string) $field2->slug => 'value2']);

        $this->assertInstanceOf(FieldValuesCollection::class, $fieldValues);
        $this->assertInstanceOf(FieldValue::class, $fieldValues[(string) $field1->slug]);
        $this->assertInstanceOf(FieldValue::class, $fieldValues[(string) $field2->slug]);
    }
}
