<?php

namespace AwardForce\Modules\Forms\FieldValues;

use Assert\Assertion;

final class FieldKey extends Key implements SerialisableKey
{
    public function __construct(private string $key)
    {
        Assertion::true(validate_slug($key), 'Field key must be a valid slug.'.$key);
    }

    public function serialise(): string
    {
        return $this->key;
    }

    public static function unserialise(string $key): static
    {
        return new self($key);
    }
}
