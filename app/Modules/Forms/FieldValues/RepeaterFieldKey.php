<?php

namespace AwardForce\Modules\Forms\FieldValues;

use Webmozart\Assert\Assert;

/**
 * A repeater field key represents not one value, but a number of values, consisting of 1 or more repeater field ids,
 * one or more repeater block indexes, and a field id. The structure looks as follows:
 *
 * - every odd value is a repeater field id, except for the last
 * - every even value is a repeater block index
 * - the last value, is the field id
 *
 * This is useful in being able to work with, and construct key structures that can be used for flat arrays. The
 * current structure is built to support just one level of nested fields. Ie. One repeater field with 1 or more
 * nested custom fields. If this class needs to be extended to support numerous nested repeater field structures
 * then the only thing to change is the assertion in the constructor, and add new methods to support more than
 * 1 of each array value type.
 */
final class RepeaterFieldKey extends Key implements \Stringable, SerialisableKey
{
    /**
     * @param  int[]  $keys
     */
    public function __construct(private array $keys)
    {
        Assert::count($this->keys, 3);
    }

    public function serialise(): string
    {
        return (string) $this;
    }

    public function repeaterFieldId(): int
    {
        return array_first($this->keys);
    }

    public function repeaterBlock(): int
    {
        return $this->keys[1];
    }

    public function fieldId(): int
    {
        return array_last($this->keys);
    }

    public function __toString(): string
    {
        return implode('.', $this->keys);
    }

    public static function unserialise(string $key): static
    {
        return new self(explode('.', $key));
    }
}
