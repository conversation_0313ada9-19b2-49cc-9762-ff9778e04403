<?php

namespace AwardForce\Modules\Forms\FieldValues;

use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Forms\FieldValues\Events\FieldValuesCreated;
use AwardForce\Modules\Forms\FieldValues\Events\FieldValuesUpdated;
use EventSauce\EventSourcing\AggregateAppliesKnownEvents;
use EventSauce\EventSourcing\EventRecorder;
use EventSauce\EventSourcing\EventSourcedAggregate;

class FieldValues implements EventSourcedAggregate
{
    use AggregateAppliesKnownEvents;

    private FieldValuesCollection $fieldValues;

    public function __construct(private EventRecorder $eventRecorder)
    {
        $this->fieldValues = new FieldValuesCollection;
    }

    public function createFieldValues(FieldValuesCollection $fieldValues): void
    {
        $this->eventRecorder->recordThat(new FieldValuesCreated($fieldValues));
    }

    public function updateFieldValues(FieldValuesCollection $fieldValues): void
    {
        $this->eventRecorder->recordThat(new FieldValuesCreated($fieldValues));
    }

    protected function applyFieldValuesCreated(FieldValuesCreated $event): void
    {
        $this->fieldValues = $event->fieldValues;
    }

    protected function applyFieldValuesUpdated(FieldValuesUpdated $event): void
    {
        $this->fieldValues = $event->fieldValues;
    }

    public function toArray(): array
    {
        return $this->fieldValues->toArray();
    }

    public function fieldValuesFromArray($fieldValues): void
    {
        $this->fieldValues = FieldValuesCollection::fromArray($fieldValues);
    }
}
