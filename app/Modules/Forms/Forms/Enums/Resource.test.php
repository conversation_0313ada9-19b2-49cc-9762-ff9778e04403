<?php

namespace AwardForce\Modules\Forms\Forms\Enums;

use Tests\LightUnitTestCase;

class ResourceTest extends LightUnitTestCase
{
    public function testForEntry(): void
    {
        $resource = Resource::Entry;
        $this->assertTrue($resource->forEntry());
    }

    public function testIsGrantReport(): void
    {
        $resource = Resource::GrantReport;
        $this->assertTrue($resource->forGrantReport());
    }

    public function testIsEmpty(): void
    {
        $resource = Resource::Empty;
        $this->assertTrue($resource->forEmpty());
    }
}
