<?php

namespace AwardForce\Modules\Forms\Forms\Enums;

use Platform\Encryption\Encrypter;

enum Protection: string
{
    case Standard = 'standard';
    case Elevated = 'elevated';
    case Maximum = 'maximum';

    /**
     * Returns the appropriate encrypted value depending on the protection level requested.
     */
    public function value(Encrypter $encrypter, mixed $value): mixed
    {
        return match ($this) {
            Protection::Standard => $value,
            Protection::Elevated => $encrypter->encrypt($value),
            Protection::Maximum => $encrypter->maximum($value),
        };
    }
}
