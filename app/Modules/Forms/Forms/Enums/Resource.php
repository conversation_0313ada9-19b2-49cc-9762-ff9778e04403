<?php

namespace AwardForce\Modules\Forms\Forms\Enums;

enum Resource: string
{
    case Entry = 'entry';
    case GrantReport = 'grant_report';
    case Empty = 'empty';
    case Organisation = 'organisation';

    public function forEntry(): bool
    {
        return $this->value === self::Entry->value;
    }

    public function forGrantReport(): bool
    {
        return $this->value === self::GrantReport->value;
    }

    public function forEmpty(): bool
    {
        return $this->value === self::Empty->value;
    }

    public function forOrganisation(): bool
    {
        return $this->value === self::Organisation->value;
    }
}
