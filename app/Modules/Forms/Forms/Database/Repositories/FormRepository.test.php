<?php

namespace AwardForce\Modules\Forms\Forms\Database\Repositories;

use AwardForce\Library\Enums\ScopeOption;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Rounds\Models\Round;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class FormRepositoryTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testReturnsFormsThatApplyToGivenChapters(): void
    {
        $season = current_account()->activeSeason();

        $formAll = $season->forms->first(); // The default form in the season. See account muffin definition

        $chapterA = $this->muffin(Chapter::class);
        $chapterB = $this->muffin(Chapter::class);

        $formSelect = $this->muffin(Form::class, ['chapter_option' => Form::CHAPTER_OPTION_SELECT]);
        $formSelect->chapters()->sync([$chapterB->id]);

        $forms = app(FormRepository::class);

        // Only the default form applies to chapterA
        $this->assertCount(1, $result = $forms->getAllForSeasonAndChapters($season, [$chapterA->id]));
        $this->assertEquals($formAll->id, $result->first()->id);

        // Both forms apply to chapterB
        $this->assertCount(2, $forms->getAllForSeasonAndChapters($season, [$chapterB->id]));
    }

    public function testCheckingIfSeasonIsMultiformOnFeatureEnabled(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();
        Feature::shouldReceive('enabled')->with('multiform')->andReturn(true);

        $this->assertFalse($forms->isSeasonMultiform($season->id));

        $this->muffin(Form::class, ['season_id' => $season->id]);
        $this->assertTrue($forms->isSeasonMultiform($season->id));
    }

    public function testCheckingIfSeasonIsMultiformOnFeatureDisabled(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();
        Feature::shouldReceive('enabled')->with('multiform')->andReturn(false);

        $this->assertFalse($forms->isSeasonMultiform($season->id));

        $this->muffin(Form::class, ['season_id' => $season->id]);
        $this->assertFalse($forms->isSeasonMultiform($season->id));
    }

    public function testPromotedForSeason(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();

        $promoted = $this->muffins(2, Form::class, ['promoted' => 1, 'type' => Form::FORM_TYPE_ENTRY]);
        $this->muffin(Round::class, ['form_id' => $promoted[0]->id, 'round_type' => Round::ROUND_TYPE_ENTRY]);
        // not promoted
        $this->muffins(2, Form::class, ['type' => Form::FORM_TYPE_ENTRY]);

        $results = $forms->promotedForSeason($season->id, false);
        $this->assertCount(2, $results);

        $results = $forms->promotedForSeason($season->id);
        $this->assertCount(1, $results);
    }

    public function testPromotedForSeasonRoundDateNotStarted(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();

        $promoted = $this->muffins(2, Form::class, ['promoted' => 1, 'type' => Form::FORM_TYPE_ENTRY]);
        $round = $this->muffin(Round::class, ['form_id' => $promoted[0]->id, 'round_type' => Round::ROUND_TYPE_ENTRY]);

        $round->startAt(now()->addDay()->format('Y-m-d H:i'), 'UTC');
        $round->save();
        $results = $forms->promotedForSeason($season->id);
        $this->assertCount(0, $results);

        $round->startAt(now()->subDay()->format('Y-m-d H:i'), 'UTC');
        $round->save();
        $results = $forms->promotedForSeason($season->id);
        $this->assertCount(1, $results);
    }

    public function testIncludeChapterIds()
    {
        $form = FormSelector::get();
        $chapters = $this->muffins(3, Chapter::class);
        foreach ($chapters as $chapter) {
            $form->chapters()->attach($chapter->id);
            $form->save();
        }
        $chapter = $this->muffin(Chapter::class);

        $result = app(FormRepository::class)->season($form->seasonId)->listChapterIds()->fields(['forms.id'])->get();

        $this->assertCount(1, $result);
        $this->assertEquals($form->id, $result->first()->id);
        $this->assertCount(3, $chapterIds = explode(',', $result->first()->listChapterIds));
        $this->assertTrue(collect($chapterIds)->contains($chapters[0]->id));
        $this->assertTrue(collect($chapterIds)->contains($chapters[1]->id));
        $this->assertTrue(collect($chapterIds)->contains($chapters[2]->id));
    }

    public function testIsNotAtLimitWhenLimitIsNotSet(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();

        // 4 Muffins and 1 default form
        $this->muffins(4, Form::class, [
            'season_id' => $season->id,
            'type' => fn() => Form::FORM_TYPE_ENTRY,
        ]);

        $this->assertFalse($forms->isAtLimit($season->id));
    }

    public function testIsNotAtLimitWhenLimitIsLargerThanNumberOfEntryForms(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();

        current_account()->formQuantityLimit = 5;

        // 3 Muffins and 1 default form
        $this->muffins(3, Form::class, [
            'season_id' => $season->id,
            'type' => fn() => Form::FORM_TYPE_ENTRY,
        ]);

        $this->assertFalse($forms->isAtLimit($season->id));
    }

    public function testIsAtLimitWhenLimitIsEqualToNumberOfEntryForms(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();

        current_account()->formQuantityLimit = 5;

        // 4 Muffins and 1 default form
        $this->muffins(4, Form::class, [
            'season_id' => $season->id,
            'type' => fn() => Form::FORM_TYPE_ENTRY,
        ]);

        $this->assertTrue($forms->isAtLimit($season->id));
    }

    public function testIsAtLimitOnlyCountsEntryTypes(): void
    {
        $forms = app(FormRepository::class);
        $season = current_account()->activeSeason();

        current_account()->formQuantityLimit = 5;

        // 3 Muffins and 1 default form
        $this->muffins(3, Form::class, [
            'season_id' => $season->id,
            'type' => fn() => Form::FORM_TYPE_ENTRY,
        ]);
        $this->muffins(5, Form::class, [
            'season_id' => $season->id,
            'type' => fn() => Form::FORM_TYPE_REPORT,
        ]);

        $this->assertFalse($forms->isAtLimit($season->id));
    }

    public function testAllowApiUpdates(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);
        $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => false])]);

        $results = app(FormRepository::class)->allowApiUpdates()->just('id');

        $this->assertCount(1, $results);
        $this->assertContains($form->id, $results);
    }

    public function testGetAllForEntriesInExistingGrantReportsInFormId(): void
    {
        $grantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $otherGrantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $entryForms = $this->muffins(3, Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
        $entry1 = $this->muffin(Entry::class, ['form_id' => $entryForms[0]->id]);
        $entry2 = $this->muffin(Entry::class, ['form_id' => $entryForms[1]->id]);
        $entry3 = $this->muffin(Entry::class, ['form_id' => $entryForms[2]->id]);

        $this->muffin(GrantReport::class, ['form_id' => $grantReportForm->id, 'entry_id' => $entry1->id]);
        $this->muffin(GrantReport::class, ['form_id' => $grantReportForm->id, 'entry_id' => $entry2->id]);
        $this->muffin(GrantReport::class, ['form_id' => $otherGrantReportForm->id, 'entry_id' => $entry3->id]);

        $results = app(FormRepository::class)->getAllForEntriesInExistingGrantReports($grantReportForm->id);

        $this->assertCount(2, $results);
        $this->assertEquals($entryForms[0]->id, $results[0]->id);
        $this->assertEquals($entryForms[1]->id, $results[1]->id);
    }

    public function testGetAllForEntriesInExistingGrantReportsInAllForms(): void
    {
        $grantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $otherGrantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $entryForms = $this->muffins(3, Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
        $entry1 = $this->muffin(Entry::class, ['form_id' => $entryForms[0]->id]);
        $entry2 = $this->muffin(Entry::class, ['form_id' => $entryForms[1]->id]);
        $entry3 = $this->muffin(Entry::class, ['form_id' => $entryForms[2]->id]);

        $this->muffin(GrantReport::class, ['form_id' => $grantReportForm->id, 'entry_id' => $entry1->id]);
        $this->muffin(GrantReport::class, ['form_id' => $grantReportForm->id, 'entry_id' => $entry2->id]);
        $this->muffin(GrantReport::class, ['form_id' => $otherGrantReportForm->id, 'entry_id' => $entry3->id]);

        $results = app(FormRepository::class)->getAllForEntriesInExistingGrantReports();

        $this->assertCount(3, $results);
        $this->assertEquals($entryForms[0]->id, $results[0]->id);
        $this->assertEquals($entryForms[1]->id, $results[1]->id);
        $this->assertEquals($entryForms[2]->id, $results[2]->id);
    }

    public function testItReturnsFormsFilteredBySettings()
    {
        $form1 = FormSelector::selectedOrDefault();
        $form1->settings = FormSettings::create([
            'enableCopy' => true,
            'allowApiUpdates' => false,
            'maxApplications' => 50,
        ]);
        $form1->save();

        $form2 = $this->muffin(Form::class, [
            'season_id' => $this->season->id,
            'settings' => FormSettings::create([
                'enableCopy' => false,
                'allowApiUpdates' => true,
                'maxApplications' => 100,
            ]),
        ]);

        $results = app(FormRepository::class)
            ->season($this->season->id)
            ->setting('enableCopy', true)
            ->fields(['forms.slug', 'forms.settings'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals((string) $form1->slug, (string) $results[0]->slug);
    }

    public function testItCanFilterByFormSettings(): void
    {
        $form = $this->muffin(Form::class, [
            'season_id' => $this->season->id,
            'settings' => FormSettings::create([
                'enableCopy' => true,
                'allowApiUpdates' => false,
                'maxApplications' => 50,
            ]),
        ]);

        $results = app(FormRepository::class)
            ->season($this->season->id)
            ->primary($form->id)
            ->selectSettings('enableCopy')
            ->fields(['forms.slug', 'forms.settings', 'forms.type'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals((string) $form->slug, (string) $results[0]->slug);
        $this->assertTrue($results[0]->settings->enableCopy);

        $results = app(FormRepository::class)
            ->season($this->season->id)
            ->primary($form->id)
            ->selectSettings('maxApplications', 'allowApiUpdates')
            ->fields(['forms.slug', 'forms.settings', 'forms.type'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals((string) $form->slug, (string) $results[0]->slug);
        $this->assertEquals(50, $results[0]->settings->maxApplications);
        $this->assertFalse($results[0]->settings->allowApiUpdates);
    }

    public function testItOnlyReturnsFormsWithActiveRounds(): void
    {
        $form1 = $this->muffin(Form::class, [
            'season_id' => $this->season->id,
            'settings' => FormSettings::create([
                'enableCopy' => true,
                'allowApiUpdates' => false,
                'maxApplications' => 50,
            ]),
        ]);

        $form2 = $this->muffin(Form::class, [
            'season_id' => $this->season->id,
            'settings' => FormSettings::create([
                'enableCopy' => true,
                'allowApiUpdates' => false,
                'maxApplications' => 50,
            ]),
        ]);

        $round = $this->muffin(Round::class, [
            'form_id' => $form1->id,
            'round_type' => Round::ROUND_TYPE_ENTRY,
        ]);

        $results = app(FormRepository::class)
            ->season($this->season->id)
            ->activeRounds()
            ->fields(['forms.slug', 'forms.settings', 'forms.type'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals((string) $form1->slug, (string) $results[0]->slug);
    }

    public function testItCanFilterByEntryIds(): void
    {
        $repository = app(FormRepository::class);

        $forms = $this->muffins(3, Form::class);
        $entries0 = $this->muffins(2, Entry::class, ['form_id' => $forms[0]->id]);
        $entries1 = $this->muffins(4, Entry::class, ['form_id' => $forms[1]->id]);
        $entries2 = $this->muffins(1, Entry::class, ['form_id' => $forms[2]->id]);

        $results = $repository
            ->entries(collect($entries0)->pluck('id')->toArray())
            ->fields(['forms.id'])
            ->get();

        $this->assertCount(1, $results);
        $this->assertEquals($forms[0]->id, $results[0]->id);

        $results = $repository
            ->entries(collect($entries0)->merge($entries2)->pluck('id')->toArray())
            ->fields(['forms.id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertTrue(collect($results)->contains(fn($result) => $result->id === $forms[0]->id));
        $this->assertTrue(collect($results)->contains(fn($result) => $result->id === $forms[2]->id));

        $results = $repository
            ->entries(collect($entries1)->merge($entries2)->pluck('id')->toArray())
            ->fields(['forms.id'])
            ->get();

        $this->assertCount(2, $results);
        $this->assertTrue(collect($results)->contains(fn($result) => $result->id === $forms[1]->id));
        $this->assertTrue(collect($results)->contains(fn($result) => $result->id === $forms[2]->id));
    }

    public function testGetAllForSeasonRespectsInvitationOnlyFormSettings(): void
    {
        $form1 = FormSelector::get();
        $form1->settings = FormSettings::create(['invitationOnly' => true]);
        $form1->save();
        $form2 = $this->muffin(Form::class, ['settings' => FormSettings::create(['invitationOnly' => false])]);

        $repository = app(FormRepository::class);
        $results = $repository->getAllForSeason($form1->season, Form::FORM_TYPE_ENTRY, false);
        $this->assertCount(1, $results);
        $this->assertEquals($form2->id, $results->first()->id);

        $results = $repository->getAllForSeason($form1->season, Form::FORM_TYPE_ENTRY, true);
        $this->assertCount(2, $results);
        $this->assertTrue($results->contains($form1));
        $this->assertTrue($results->contains($form2));
    }

    public function testItCanFilterBySelectedRoles()
    {
        $repository = app(FormRepository::class);
        $account = $this->muffin(Account::class);
        CurrentAccount::set($account);

        $roleOne = $this->muffin(Role::class);
        $roleTwo = $this->muffin(Role::class);

        $formSelectedRolesOne = $this->muffin(Form::class, ['settings' => FormSettings::create(['roleVisibility' => ScopeOption::Select->value])]);
        $formSelectedRolesOne->roles()->sync([$roleOne->id]);
        $formSelectedRolesTwo = $this->muffin(Form::class, ['settings' => FormSettings::create(['roleVisibility' => ScopeOption::Select->value])]);
        $formSelectedRolesTwo->roles()->sync([$roleTwo->id]);
        $formNoRoles = $this->muffin(Form::class, ['settings' => FormSettings::create(['roleVisibility' => ScopeOption::Select->value])]);
        $formAllRoles = $this->muffin(Form::class, ['settings' => FormSettings::create(['roleVisibility' => ScopeOption::All->value])]);

        $resultsOne = $repository->roles([$roleOne->id])->fields(['forms.id'])->get(); //3 forms: DefaultOne, SelectedRolesOne, AllRoles
        $resultsTwo = $repository->roles([$roleTwo->id])->fields(['forms.id'])->get(); // 3 forms: DefaultOne, SelectedRolesTwo, AllRoles

        $this->assertCount(3, $resultsOne);
        $this->assertTrue($resultsOne->contains(fn($result) => $result->id === $formSelectedRolesOne->id));
        $this->assertTrue($resultsOne->contains(fn($result) => $result->id === $formAllRoles->id));
        $this->assertTrue($resultsOne->contains(fn($result) => $result->id === FormSelector::get()->id));
        $this->assertCount(3, $resultsTwo);
        $this->assertTrue($resultsTwo->contains(fn($result) => $result->id === $formSelectedRolesTwo->id));
        $this->assertTrue($resultsTwo->contains(fn($result) => $result->id === $formAllRoles->id));
        $this->assertTrue($resultsTwo->contains(fn($result) => $result->id === FormSelector::get()->id));
    }

    public function testItRetrievesFormWhenNoRoleVisibilityIsSelected()
    {
        $repository = app(FormRepository::class);
        $account = $this->muffin(Account::class);
        CurrentAccount::set($account);

        $formWithNoRoleVisibility = $this->muffin(Form::class);
        // empty settings, no role visibility
        $formWithNoRoleVisibility->setRawAttributes(array_merge($formWithNoRoleVisibility->getAttributes(), ['settings' => '[]']));
        $formWithNoRoleVisibility->save();
        $results = $repository->roles()->fields(['id'])->get();

        $this->assertCount(2, $results);
        $this->assertTrue($results->contains(fn($result) => $result->id === $formWithNoRoleVisibility->id));
        $this->assertTrue($results->contains(fn($result) => $result->id === FormSelector::get()->id));
    }
}
