<?php

namespace Tests\Modules\Forms\Middleware;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Middleware\Collaborator;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator as CollaboratorModel;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class CollaboratorTest extends BaseTestCase
{
    use Database;
    use Laravel;

    protected function init()
    {
        $this->setupUserWithRole('Entrant', true);
    }

    public function testCantAccessWhenFormIsNotCollaborative()
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['collaborative' => false])]);
        $submittable = $this->muffin(Entry::class, ['form_id' => $form->id]);

        $this->muffin(Membership::class, ['user_id' => consumer_id()]);

        $this->muffin(CollaboratorModel::class, [
            'submittable_id' => $submittable->id,
            'submittable_type' => $submittable->getMorphClass(),
            'user_id' => consumer_id(),
        ]);

        $request = (new Request())
            ->setRouteResolver(fn() => $this->mock('router')
                ->shouldReceive('parameters')
                ->andReturn([$submittable])
                ->getMock());

        $this->expectException(NotFoundHttpException::class);

        app(Collaborator::class)->handle($request, function ($request) {
            return 'next';
        });
    }

    public function testCanAccessWhenFormIsCollaborative()
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['collaborative' => true])]);
        $submittable = $this->muffin(Entry::class, ['form_id' => $form->id]);

        $this->muffin(Membership::class, ['user_id' => ($user = Consumer::user())->id]);

        $this->muffin(CollaboratorModel::class, [
            'submittable_id' => $submittable->id,
            'submittable_type' => $submittable->getMorphClass(),
            'user_id' => $user->id,
        ]);

        $request = (new Request())
            ->setRouteResolver(fn() => $this->mock('router')
                ->shouldReceive('parameters')
                ->andReturn([$submittable])
                ->getMock());

        $response = app(Collaborator::class)->handle($request, function ($request) {
            return 'next';
        });

        $this->assertSame('next', $response);
    }

    public function testItAllowsOwnersRegardlessOfCollaborativeMode(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['collaborative' => false])]);
        $submittable = $this->muffin(Entry::class, ['form_id' => $form->id, 'user_id' => $this->user->id]);

        $request = (new Request())
            ->setRouteResolver(fn() => $this->mock('router')
                ->shouldReceive('parameters')
                ->andReturn([$submittable])
                ->getMock());

        $response = app(Collaborator::class)->handle($request, function ($request) {
            return 'next';
        });

        $this->assertSame('next', $response);
    }

    public function testCannotAccessIfGuest(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['collaborative' => true])]);
        $submittable = $this->muffin(Entry::class, ['form_id' => $form->id]);

        $this->muffin(Membership::class, ['user_id' => ($user = Consumer::user())->id]);

        $this->muffin(CollaboratorModel::class, [
            'submittable_id' => $submittable->id,
            'submittable_type' => $submittable->getMorphClass(),
            'user_id' => $user->id,
        ]);

        Consumer::shouldReceive('isManager')->andReturn(false);
        Consumer::shouldReceive('isUser')->andReturn(false);
        $request = (new Request())
            ->setRouteResolver(fn() => $this->mock('router')
                ->shouldReceive('parameters')
                ->andReturn([$submittable])
                ->getMock());

        $this->expectException(NotFoundHttpException::class);
        app(Collaborator::class)
            ->handle($request, fn() => 'next');
    }
}
