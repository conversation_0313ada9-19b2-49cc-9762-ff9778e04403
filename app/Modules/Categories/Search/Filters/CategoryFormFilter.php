<?php

namespace AwardForce\Modules\Categories\Search\Filters;

use AwardForce\Library\Search\Filters\EntryFormFilter;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Illuminate\Support\Arr;

class CategoryFormFilter extends EntryFormFilter
{
    public function applies(): bool
    {
        return ! Arr::get($this->input, 'slug');
    }

    protected function limitFormTypes($query)
    {
        $query->where('forms.type', Form::FORM_TYPE_ENTRY);
    }
}
