<?php

namespace AwardForce\Modules\Judging\Search\Leaderboard;

use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Search\Columns\Text;
use AwardForce\Modules\Forms\Fields\Search\Enhancers\FieldValuesEnhancer;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\ScoreSetBased;
use AwardForce\Modules\Judging\Data\Decision;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Fails;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Passes;
use AwardForce\Modules\Judging\Search\Leaderboard\Columns\Responses;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Platform\Search\Columns;

final class QualifyingColumnatorTest extends LeaderboardColumnatorTestBase
{
    protected function scoreSetMode(): string
    {
        $this->markTestSkipped();

        return ScoreSet::MODE_QUALIFYING;
    }

    public function testCanSearchQualifyingLeaderboard(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_QUALIFYING]);
        $judge = $this->newJudge();
        $round = $this->newRound();

        $passEntry = $this->newEntry($this->muffin(Chapter::class)->id, $this->muffin(Category::class)->id);
        $failEntry = $this->newEntry($passEntry->chapterId, $passEntry->categoryId);

        $passAssignment = $this->manual($scoreSet, $round->id, $passEntry->id, $judge->id);
        $failAssignment = $this->manual($scoreSet, $round->id, $failEntry->id, $judge->id);

        $this->muffin(Decision::class, ['assignment_id' => $passAssignment->id, 'decision' => Decision::DECISION_PASS]);
        $this->muffin(Decision::class, ['assignment_id' => $failAssignment->id, 'decision' => Decision::DECISION_FAIL]);

        $results = $this->search([], ['score-set' => $scoreSet->id]);

        $this->assertCount(2, $results);

        $pass = $results->first(function ($assignment) use ($passAssignment) {
            return $assignment->id == $passAssignment->id;
        });
        $this->assertEquals(1, (new Passes)->value($pass));
        $this->assertEquals(0, (new Fails)->value($pass));
        $this->assertEquals(1, (new Responses)->value($pass));

        $fail = $results->first(function ($assignment) use ($failAssignment) {
            return $assignment->id == $failAssignment->id;
        });
        $this->assertEquals(0, (new Passes)->value($fail));
        $this->assertEquals(1, (new Fails)->value($fail));
        $this->assertEquals(1, (new Responses)->value($fail));
    }

    protected function area()
    {
        return ScoreSet::MODE_QUALIFYING.'_leaderboard.search';
    }

    public function testHiddenConditionalFieldsShouldShowEmptyValue(): void
    {
        $field1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $field2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $field1->setConditionalField(true, 'show', $field2->id, 'is', 'value1')->save();

        app(ValuesService::class)->setValuesForObject([
            ((string) $field1->slug) => 'value1',
            ((string) $field2->slug) => 'value2',
        ], $entry = $this->muffin(Entry::class));

        /** @var Assignment $assignment */
        $assignment = $this->muffin(Assignment::class, ['entry_id' => $entry->id]);

        $column1 = new Text($field1, 'entries.id');
        $column2 = new Text($field2, 'entries.id');

        (FieldValuesEnhancer::fromColumns(
            new Columns([$column1, $column2]), app(ScoreSetBased::class)->setScoreSet($assignment->scoreSet))
        )->enhance(collect([$entry]));

        $this->assertNull($entry->{strtolower($field1->slug)});
    }
}
