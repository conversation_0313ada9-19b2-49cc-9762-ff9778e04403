<?php

namespace AwardForce\Modules\Judging\Composers;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Assignments\Models\AssignmentCollection;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Comments\Renderers\AbstentionComments;
use AwardForce\Modules\Comments\Renderers\ConflictOfInterestComments;
use AwardForce\Modules\Comments\Services\Pages\CriterionComments;
use AwardForce\Modules\Comments\Services\Pages\JudgeScoreComments;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\VisibleAttachments;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\AttachmentTypes;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\Factory;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\VisibleFields;
use AwardForce\Modules\Judging\Abstain;
use AwardForce\Modules\Judging\ConflictOfInterest;
use AwardForce\Modules\Judging\Data\ScoreRepository;
use AwardForce\Modules\Judging\Services\FieldCriteriaBlocks;
use AwardForce\Modules\Judging\View\JudgingView;
use AwardForce\Modules\Rounds\Services\RoundStatus;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoringCriteria\Repositories\ScoringCriterionRepository;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Translator\Engine;

class ScoringFormComposer
{
    use Abstain;
    use ConflictOfInterest;
    use JudgingView;

    /**
     * @var ScoringCriterionRepository
     */
    protected $scoringCriteria;

    /**
     * @var CurrentAssignments
     */
    protected $assignments;

    /**
     * @var ScoreRepository
     */
    protected $scores;

    /**
     * @var SettingRepository
     */
    protected $setting;

    /**
     * @var ContentBlockRepository
     */
    protected $contentBlocks;

    /**
     * @var Engine
     */
    protected $translator;

    /**
     * @var ScoreSetRepository
     */
    protected $scoreSets;

    /**
     * @var VisibleAttachments
     */
    protected $visibleAttachments;

    /**
     * @var AttachmentTypes
     */
    private $attachmentTypes;

    /**
     * @var Factory visibleFieldsFactory
     */
    protected $visibleFieldsFactory;

    protected Entry $entry;

    /** @var RoundStatus */
    protected $roundStatus;

    /**
     * ScoringFormComposer constructor.
     */
    public function __construct(
        ScoringCriterionRepository $scoringCriteria,
        CurrentAssignments $assignments,
        ScoreRepository $scores,
        SettingRepository $setting,
        ContentBlockRepository $contentBlocks,
        Engine $translator,
        ScoreSetRepository $scoreSets,
        Factory $factory,
        VisibleAttachments $visibleAttachments,
        AttachmentTypes $attachmentTypes,
        RoundStatus $roundStatus
    ) {
        $this->scoringCriteria = $scoringCriteria;
        $this->assignments = $assignments;
        $this->scores = $scores;
        $this->setting = $setting;
        $this->contentBlocks = $contentBlocks;
        $this->translator = $translator;
        $this->scoreSets = $scoreSets;
        $this->visibleAttachments = $visibleAttachments;
        $this->attachmentTypes = $attachmentTypes;
        $this->visibleFieldsFactory = $factory;
        $this->roundStatus = $roundStatus;
    }

    public function compose(View $view)
    {
        Model::enableCache();
        $this->entry = $view->entry;

        $assignments = $this->getAssignments($this->entry);
        $scoreSets = $this->getScoreSets($assignments);
        $visibleFields = $this->getVisibleFields($scoreSets);

        $displayReferees = $scoreSets->settingEnabled(
            fn(ScoreSet $scoreSet) => data_get($scoreSet->settings, 'referee.displayReferees')
        );

        $fields = $this->translator->shallow($visibleFields->forSubmittable($view->entry));

        $internalComments = $scoreSets->settingEnabled('internalComments');
        $displayGallery = $scoreSets->settingEnabled('galleryFromFileFields');
        $scoreSetIds = $assignments->pluck('score_set_id')->all();

        $entryComments = $this->entryComments($this->entry, $scoreSets);
        $abstentionComments = $this->abstentionComments($this->entry, $scoreSets);
        $criteria = $this->getCriteria($assignments, $this->entry);
        $criteriaComments = $this->criteriaComments($this->entry, $scoreSets, $criteria);
        $conflictOfInterestComments = $this->conflictOfInterestComments($this->entry, $scoreSets);

        $view->with([
            //Abstentions
            'canAbstain' => $this->canAbstain($scoreSets),
            'canDeclareConflictOfInterest' => $this->canDeclareConflictOfInterest($scoreSets),

            // Assignments
            'assignments' => $assignments,
            'scoreSetIds' => $scoreSetIds,

            // Scoring
            'scores' => $this->getScores($assignments),
            'blocks' => (new FieldCriteriaBlocks)->generate($fields, $criteria),
            'additionalCriteria' => $criteria->where('fieldId', false),
            'allCriteria' => $criteria,
            'lockScores' => $assignments->scoringLocked(),
            'isScoringRound' => $this->roundStatus->isScoringJudging($assignments),
            'isViewingRound' => $this->roundStatus->isViewOnlyJudging(),

            // Toggles
            'hasAbstained' => $this->hasAbstained($assignments),
            'hasConflictOfInterest' => $this->hasConflictOfInterest($assignments),

            // Retrieve contributors based on the retrieved fields
            'contributors' => $this->contributors($view->entry),

            // Comments
            'commentOnAbstentionRequired' => $scoreSets->settingEnabled('commentOnAbstention'),
            'commentOnEntries' => $scoreSets->settingEnabled('comments'),
            'commentOnEntriesRequired' => $scoreSets->settingEnabled('commentRequired'),
            'judgesCanSeeEachOthersComments' => $scoreSets->settingEnabled('allComments'),
            'entryComments' => $entryComments,
            'internalComments' => $internalComments,

            'abstentionComments' => (new AbstentionComments($abstentionComments))->data(),
            'conflictOfInterestComments' => (new ConflictOfInterestComments($conflictOfInterestComments))->data(),

            'criteriaComments' => $criteriaComments,

            // Content blocks
            'judgingCommentsBlock' => 'judging-comments',

            // Score matrices
            'shareScores' => $scoreSets->pluck('shareScores')->flatten()->all(),

            // Attachments
            'attachments' => $this->attachments($view->entry, $scoreSets),
            'links' => $this->links($view->entry, $scoreSets->all()),
            'attachmentDownload' => $scoreSets->settingEnabled('attachmentDownload'),
            'anonymiseAttachments' => $scoreSets->settingEnabled('anonymiseAttachments'),

            // Duplicates
            'duplicates' => $this->duplicates(),
            'primaryEntry' => $this->primaryEntry(),

            // Display
            'entrantName' => $scoreSets->settingEnabled('displayEntrantName'),
            'entryName' => $scoreSets->settingEnabled('displayEntryName'),
            'displayMetadata' => $scoreSets->settingEnabled('metadata'),
            'displayGallery' => $displayGallery,
            'galleryFromFileFields' => $displayGallery ? $this->galleryFromFileFields($fields) : collect([]),
            'plagiarismScans' => $this->plagiarismScans($view->entry, $scoreSets),
            'duplicateEntryCount' => $scoreSets->settingEnabled('duplicateEntryCount'),
            'duplicateEntryList' => $scoreSets->settingEnabled('duplicateEntryList'),
            'isVisualLayout' => $scoreSets->settingEnabled(fn($scoreSet) => $scoreSet->isVisualLayout()),
            'isMixedLayout' => $scoreSets->settingAllEnabled(fn($scoreSet) => $scoreSet->isMixedLayout()),

            'referees' => $this->referees($view->entry, $visibleFields),
            'displayReferees' => $displayReferees,
            'refereeFields' => $this->refereeFields($scoreSets, $displayReferees),

        ]);

        VueData::registerTranslations(['judging.form.abstain.label', 'judging.form.conflict_of_interest.label', 'comments.visibility.internal.badge']);

        Model::disableCache();
    }

    /**
     * @return mixed
     */
    protected function getCriteria(Collection $assignments, Entry $entry)
    {
        $criteria = $this->scoringCriteria->getForScoring(
            $entry->formId,
            $entry->categoryId,
            $assignments->pluck('score_set_id')->all()
        );

        return $this->translator->shallow($criteria);
    }

    /**
     * @return array
     */
    protected function getScores(AssignmentCollection $assignments)
    {
        $scores = $this->scores->getAllForAssignments($assignments->just('id'));

        return $scores->pluck('score', 'scoring_criterion_id')->all();
    }

    /**
     * @return Collection
     */
    protected function contributors(Entry $entry)
    {
        $assignments = $this->getAssignments($entry);
        $scoreSets = $this->getScoreSets($assignments);
        $visibleFields = $this->getVisibleFields($scoreSets);

        return $this->translator->shallow($visibleFields->forContributors($entry));
    }

    /**
     * @return Collection
     */
    public function attachments(Entry $entry, Collection $scoreSets)
    {
        $attachments = $this->visibleAttachments->scoringAttachments($entry, $scoreSets->all());
        $attachmentTypes = $this->attachmentTypes->fromScoreSets($scoreSets, $entry->category->attachmentTypes);

        $assignments = $this->getAssignments($entry);
        $scoreSets = $this->getScoreSets($assignments);
        $visibleFields = $this->getVisibleFields($scoreSets);
        $attachmentFields = $visibleFields->forAttachments($entry, $attachments, $attachmentTypes);

        return $this->translator->shallow($attachmentFields);
    }

    /**
     * @return Collection
     */
    public function links(Entry $entry, array $scoreSets)
    {
        return $this->visibleAttachments->links($entry, $scoreSets);
    }

    /**
     * @return Collection
     */
    protected function galleryFromFileFields(Collection $fields)
    {
        return $fields->onlyImages();
    }

    /**
     * @return array
     */
    protected function plagiarismScans(Entry $entry, Collection $scoreSets)
    {
        if (! $scoreSets->settingEnabled('displayPlagiarismScan')) {
            return null;
        }

        return $entry->completedPlagiarismScans->forView();
    }

    protected function getAssignments(Entry $entry): Collection
    {
        return $this->assignments->forVipJudgingEntry(new AssignmentUser(Consumer::get()), $entry->id, true);
    }

    protected function getScoreSets(Collection $assignments): ScoreSetCollection
    {
        return new ScoreSetCollection($assignments->pluck('scoreSet')->filter());
    }

    protected function getVisibleFields(ScoreSetCollection $scoreSets): VisibleFields
    {
        return $this->visibleFieldsFactory->vipJudging($scoreSets);
    }

    protected function entryComments(Entry $entry, ScoreSetCollection $scoreSets)
    {
        return (new JudgeScoreComments($scoreSets))->genericForEntry($entry);
    }

    protected function abstentionComments(Entry $entry, ScoreSetCollection $scoreSets)
    {
        return (new JudgeScoreComments($scoreSets))->abstentionForEntry($entry);
    }

    protected function conflictOfInterestComments(Entry $entry, ScoreSetCollection $scoreSets)
    {
        return (new JudgeScoreComments($scoreSets))->conflictOfInterest($entry);
    }

    /**
     * Scoring criteria comments, keyed by criterion id.
     */
    protected function criteriaComments(Entry $entry, ScoreSetCollection $scoreSets, $criteria): array
    {
        $criterionComments = new CriterionComments($scoreSets);
        $criteriaComments = [];

        foreach ($criteria as $criterion) {
            $criteriaComments[$criterion->id] = $criterionComments->forCriterion($criterion, $entry);
        }

        return $criteriaComments;
    }

    protected function referees(Entry $entry, VisibleFields $visibleFields)
    {
        return $this->translator->shallow($visibleFields->forReferees($entry));
    }

    /**
     * @return array<string, bool>
     */
    protected function refereeFields(ScoreSetCollection $scoreSets, bool $displayReferees = false): array
    {
        if (! $displayReferees) {
            return [];
        }

        return array_keys(array_filter([
            'name' => ! $scoreSets->settingEnabled(fn(ScoreSet $scoreSet) => data_get($scoreSet->settings, 'referee.hideRefereeName')),
            'email' => ! $scoreSets->settingEnabled(fn(ScoreSet $scoreSet) => data_get($scoreSet->settings, 'referee.hideRefereeEmail')),
        ]));
    }
}
