<?php

namespace AwardForce\Modules\Judging\Composers;

use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Referees\Models\Referee;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetSettings;
use AwardForce\Modules\ScoreSets\Values\RefereeSettings;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ScoringPDFComposerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function init()
    {
        $this->setupUserWithRole('Judge', true);
    }

    public function testShouldDisplayRefereesIfAtLeastOneScoreSetIsConfiguredToDisplayThem()
    {
        $judge = $this->setupUserWithRole('Judge', true);

        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES]);
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);
        $scoreSetWithVisibleReferees = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(false))]);
        $scoreSetWithHiddenReferees = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true))]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSetWithVisibleReferees->getKey(), 'role_id' => $this->role->getKey(), 'judge_id' => $judge->getKey(), 'entry_id' => $entry->getKey()]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSetWithHiddenReferees->getKey(), 'role_id' => $this->role->getKey(), 'judge_id' => $judge->getKey(), 'entry_id' => $entry->getKey()]);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->getKey(), 'tab_id' => $tab->getKey()]);
        $refereeField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES, 'visibility' => '|'.ScoreSet::MODE_VIP.'|', 'tab_id' => $tab->getKey()]);
        $refereeField->sets()->attach([$scoreSetWithVisibleReferees->getKey(), $scoreSetWithHiddenReferees->getKey()]);

        app(ValuesService::class)->setValuesForObject([(string) $refereeField->slug => fake()->text(20)], $referee);

        $view = m::spy(View::class);
        $view->entry = $entry;
        $view->user = new AssignmentUser($judge);
        $view->submittable = $entry;

        app(ScoringPDFComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) use ($refereeField) {
            $this->assertArrayHasKey('referees', $args);

            $resourceFieldsCollection = data_get($args, 'referees');
            $this->assertNotEmpty($resourceFieldsCollection);
            $this->assertTrue($resourceFieldsCollection->first()->fields->first()->is($refereeField));

            return true;
        });
    }

    public function testShouldNotDisplayRefereesIfAllScoreSetsAreConfiguredNotToDisplayThem()
    {
        $judge = $this->setupUserWithRole('Judge', true);

        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES]);
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);
        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_VIP,
            'settings' => new ScoreSetSettings(new RefereeSettings(false)),
        ]);
        $assignment = $this->muffin(Assignment::class, ['score_set_id' => $scoreSet->getKey(), 'role_id' => $this->role->getKey(), 'judge_id' => $judge->getKey(), 'entry_id' => $entry->getKey()]);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->getKey(), 'tab_id' => $tab->getKey()]);
        $refereeField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES, 'visibility' => '|'.ScoreSet::MODE_VIP.'|', 'tab_id' => $tab->getKey()]);
        $refereeField->sets()->attach($scoreSet->id);

        app(ValuesService::class)->setValuesForObject([(string) $refereeField->slug => fake()->text(20)], $referee);

        $view = m::spy(View::class);
        $view->entry = $entry;
        $view->user = new AssignmentUser($judge);
        $view->submittable = $entry;

        app(ScoringPDFComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) {
            $this->assertArrayHasKey('referees', $args);

            $resourceFieldsCollection = data_get($args, 'referees');
            $this->assertEmpty($resourceFieldsCollection);

            return true;
        });
    }

    public function testShouldHideRefereeNameAndEmailIfAtLeastOneScoreSetIsConfiguredToHideThem()
    {
        $judge = $this->setupUserWithRole('Judge', true);

        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES]);
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);
        $scoreSetWithVisibleRefereeNameAndEmail = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, false, false))]);
        $scoreSetWithHiddenRefereeNameAndEmail = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, true, true))]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSetWithVisibleRefereeNameAndEmail->getKey(), 'role_id' => $this->role->getKey(), 'judge_id' => $judge->getKey(), 'entry_id' => $entry->getKey()]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSetWithHiddenRefereeNameAndEmail->getKey(), 'role_id' => $this->role->getKey(), 'judge_id' => $judge->getKey(), 'entry_id' => $entry->getKey()]);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->getKey(), 'tab_id' => $tab->getKey()]);
        $refereeField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES, 'visibility' => '|'.ScoreSet::MODE_VIP.'|', 'tab_id' => $tab->getKey()]);
        $refereeField->sets()->attach([$scoreSetWithVisibleRefereeNameAndEmail->getKey(), $scoreSetWithHiddenRefereeNameAndEmail->getKey()]);

        app(ValuesService::class)->setValuesForObject([(string) $refereeField->slug => fake()->text(20)], $referee);

        $view = m::spy(View::class);
        $view->entry = $entry;
        $view->user = new AssignmentUser($judge);
        $view->submittable = $entry;

        app(ScoringPDFComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) {
            $this->assertArrayHasKey('hideRefereeEmail', $args);
            $this->assertArrayHasKey('hideRefereeName', $args);

            $this->assertTrue(data_get($args, 'hideRefereeEmail'));
            $this->assertTrue(data_get($args, 'hideRefereeName'));

            return true;
        });
    }

    public function testShouldNotHideRefereeNameAndEmailIfAllScoreSetAreConfiguredNotToHideThem()
    {
        $judge = $this->setupUserWithRole('Judge', true);

        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES]);
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);
        $scoreSetsWithVisibleRefereeNameAndEmail = collect($this->muffins(2, ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, false, false))]));
        // $scoreSetWithHiddenRefereeNameAndEmail = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, false, false))]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSetsWithVisibleRefereeNameAndEmail->first()->getKey(), 'role_id' => $this->role->getKey(), 'judge_id' => $judge->getKey(), 'entry_id' => $entry->getKey()]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSetsWithVisibleRefereeNameAndEmail->last()->getKey(), 'role_id' => $this->role->getKey(), 'judge_id' => $judge->getKey(), 'entry_id' => $entry->getKey()]);
        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->getKey(), 'tab_id' => $tab->getKey()]);
        $refereeField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES, 'visibility' => '|'.ScoreSet::MODE_VIP.'|', 'tab_id' => $tab->getKey()]);
        $refereeField->sets()->attach($scoreSetsWithVisibleRefereeNameAndEmail);

        app(ValuesService::class)->setValuesForObject([(string) $refereeField->slug => fake()->text(20)], $referee);

        $view = m::spy(View::class);
        $view->entry = $entry;
        $view->user = new AssignmentUser($judge);
        $view->submittable = $entry;

        app(ScoringPDFComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) {
            $this->assertArrayHasKey('hideRefereeEmail', $args);
            $this->assertArrayHasKey('hideRefereeName', $args);

            $this->assertFalse(data_get($args, 'hideRefereeEmail'));
            $this->assertFalse(data_get($args, 'hideRefereeName'));

            return true;
        });
    }
}
