<?php

namespace AwardForce\Modules\Judging\Composers;

use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Entries\Services\VisibleAttachments;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\AttachmentTypes;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\Factory;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\VisibleFields;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Judging\Services\FieldCriteriaBlocks;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetCollection;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoringCriteria\Repositories\ScoringCriterionRepository;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Tectonic\LaravelLocalisation\Translator\Engine;

class ScoringPDFComposer
{
    /**
     * @var CurrentAssignments
     */
    protected $assignments;

    /**
     * @var ScoringCriterionRepository
     */
    protected $scoringCriteria;

    /**
     * @var Engine
     */
    protected $translator;

    /**
     * @var VisibleFields
     */
    protected $visibleFields;

    /**
     * @var VisibleAttachments
     */
    protected $visibleAttachments;

    /**
     * @var AttachmentTypes
     */
    protected $attachmentTypes;

    /**
     * @var ScoreSetRepository
     */
    protected $scoreSets;

    /**
     * @var TabRepository
     */
    protected $tabs;

    /**
     * @var Factory visibleFieldsFactory
     */
    private $visibleFieldsFactory;

    /**
     * EntryScoringPDFComposer constructor.
     */
    public function __construct(
        CurrentAssignments $assignments,
        ScoringCriterionRepository $scoringCriteria,
        Engine $translator,
        Factory $factory,
        VisibleAttachments $visibleAttachments,
        AttachmentTypes $attachmentTypes,
        ScoreSetRepository $scoreSets,
        TabRepository $tabs
    ) {
        $this->assignments = $assignments;
        $this->scoringCriteria = $scoringCriteria;
        $this->translator = $translator;
        $this->visibleAttachments = $visibleAttachments;
        $this->attachmentTypes = $attachmentTypes;
        $this->scoreSets = $scoreSets;
        $this->tabs = $tabs;
        $this->visibleFieldsFactory = $factory;
    }

    public function compose(View $view)
    {
        $mode = ScoreSet::MODE_VIP;

        $user = $view->user;

        $assignments = $this->assignments->forVipJudgingEntry($user, $view->submittable->id);

        /** @var ScoreSetCollection */
        $scoreSets = $this->scoreSets->getByIds($assignments->pluck('score_set_id')->toArray());

        $this->visibleFields = $this->visibleFieldsFactory->vipJudging($scoreSets);

        $displayReferees = $scoreSets->settingEnabled(
            fn(ScoreSet $scoreSet) => data_get($scoreSet->settings, 'referee.displayReferees')
        );

        $fields = $this->translator->translate($this->visibleFields->forSubmittable($view->submittable));

        $criteria = $this->getCriteria($assignments, $view->submittable);

        $view->with([
            'mode' => $mode,
            'tabbedFields' => $this->tabbedFields($this->getTabs($view->submittable), $fields, $criteria),
            'additionalCriteria' => $criteria->where('fieldId', false),
            'contributors' => $this->contributors($view->submittable),
            'attachments' => $this->attachments($view->submittable, $scoreSets),
            'referees' => $this->referees($view->submittable, $displayReferees),
            ...$this->refereeSettings($scoreSets, $displayReferees),
            'links' => $this->links($view->submittable, $scoreSets->all()),
            'scoringBoxes' => $scoreSets->settingEnabled('displayScoringBoxes'),
            'displayEntryName' => $scoreSets->settingAllEnabled('displayEntryName'),
            'displayEntrantName' => $scoreSets->settingAllEnabled('displayEntrantName'),
            'attachmentDownload' => $scoreSets->settingAllEnabled('attachmentDownload'),
        ]);
    }

    /**
     * @return Collection
     */
    protected function getTabs(Submittable $submittable)
    {
        $tabs = $this->tabs->forCategory($submittable->getCategory(), Tab::RESOURCE_ENTRIES, $submittable->getSeasonId());

        return $this->translator->translate($tabs);
    }

    /**
     * @return Collection
     */
    protected function tabbedFields(Collection $tabs, Collection $fields, Collection $criteria)
    {
        return $fields->groupBy('tab_id')
            ->map(function ($fields, $tabId) use ($tabs, $criteria) {
                return collect([
                    'tab' => $tabs->firstWhere('id', $tabId),
                    'blocks' => (new FieldCriteriaBlocks)->generate($fields, $criteria),
                ]);
            })->filter(function ($fields) {
                return $fields->get('blocks')->isNotEmpty();
            })->sortBy(function ($fields) {
                return ! $fields->get('tab');
            });
    }

    /**
     * @return mixed
     */
    protected function getCriteria(Collection $assignments, Submittable $submittable)
    {
        $criteria = $this->scoringCriteria->getForScoring(
            $submittable->getFormId(),
            $submittable->getCategoryId(),
            $assignments->pluck('score_set_id')->all()
        );

        return $this->translator->translate($criteria);
    }

    /**
     * @param  Submittable  $entry
     * @return Collection
     */
    protected function contributors(Submittable $submittable)
    {
        return $this->translator->translate($this->visibleFields->forContributors($submittable));
    }

    protected function referees(Submittable $submittable, bool $displayReferees = false): Collection
    {
        if (! $displayReferees) {
            return collect();
        }

        return $this->translator->translate($this->visibleFields->forReferees($submittable));
    }

    /**
     * @return array<string, bool>
     */
    protected function refereeSettings(ScoreSetCollection $scoreSets, bool $displayReferees = false): array
    {
        if (! $displayReferees) {
            return ['hideRefereeEmail' => true, 'hideRefereeName' => true];
        }

        return [
            'hideRefereeEmail' => $scoreSets->settingEnabled(fn(ScoreSet $scoreSet) => data_get($scoreSet->settings, 'referee.hideRefereeEmail')),
            'hideRefereeName' => $scoreSets->settingEnabled(fn(ScoreSet $scoreSet) => data_get($scoreSet->settings, 'referee.hideRefereeName')),
        ];
    }

    /**
     * @return Collection
     */
    public function attachments(Submittable $submittable, Collection $scoreSets)
    {
        $attachments = $this->visibleAttachments->attachments($submittable, $scoreSets->all());
        $attachmentTypes = $this->attachmentTypes->fromScoreSets($scoreSets, $submittable->getCategory()->attachmentTypes);

        $attachmentFields = $this->visibleFields->forAttachments($submittable, $attachments, $attachmentTypes);

        return $this->translator->translate($attachmentFields);
    }

    /**
     * @return Collection
     */
    public function links(Submittable $submittable, array $scoreSets)
    {
        return $this->visibleAttachments->links($submittable, $scoreSets);
    }
}
