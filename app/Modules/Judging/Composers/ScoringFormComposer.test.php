<?php

namespace AwardForce\Modules\Judging\Composers;

use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\ResourceFieldsCollection;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Referees\Models\Referee;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetSettings;
use AwardForce\Modules\ScoreSets\Values\RefereeSettings;
use Carbon\Carbon;
use Illuminate\Contracts\View\View;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ScoringFormComposerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function init()
    {
        $this->setupUserWithRole('Judge', true);
    }

    public function testItReturnsAllAttachmentsFromVisibleAndInvisibleTabs(): void
    {
        $category = $this->muffin(Category::class);
        $tabVisible = $this->muffin(Tab::class, ['category_option' => Tab::CATEGORY_OPTION_SELECT, 'type' => Tab::TYPE_ATTACHMENTS, 'visible_to_entrants' => 1]);
        $tabHidden = $this->muffin(Tab::class, ['category_option' => Tab::CATEGORY_OPTION_SELECT, 'type' => Tab::TYPE_ATTACHMENTS, 'visible_to_entrants' => 0]);
        $category->tabs()->attach([$tabVisible->id, $tabHidden->id]);

        $entry = $this->muffin(Entry::class, [
            'submitted_at' => Carbon::now(),
            'category_id' => $category->id,
        ]);

        $this->muffin(Attachment::class, ['submittable_id' => $entry->id, 'tab_id' => $tabVisible->id, 'file_id' => $this->muffin(File::class)->id]);
        $this->muffin(Attachment::class, ['submittable_id' => $entry->id, 'tab_id' => $tabHidden->id, 'file_id' => $this->muffin(File::class)->id]);
        $scoreSets = $this->muffins(2, ScoreSet::class, ['attachments' => true, 'attachmentTypes' => 'A', 'mode' => ScoreSet::MODE_VIP]);

        $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSets[0]->id,
            'role_id' => $this->role->id,
            'judge_id' => $this->user->id,
            'entry_id' => $entry->id,
        ]);

        $this->assertCount(2, app(ScoringFormComposer::class)->attachments($entry, collect($scoreSets)));
    }

    public function testItReturnsOnlyAttachmentFromSubmittableCategory(): void
    {
        $category = $this->muffin(Category::class);
        $otherCategory = $this->muffin(Category::class);
        $tabVisible = $this->muffin(Tab::class, ['category_option' => Tab::CATEGORY_OPTION_SELECT, 'type' => Tab::TYPE_ATTACHMENTS]);
        $tabHidden = $this->muffin(Tab::class, ['category_option' => Tab::CATEGORY_OPTION_SELECT, 'type' => Tab::TYPE_ATTACHMENTS]);
        $category->tabs()->attach([$tabVisible->id]);
        $otherCategory->tabs()->attach([$tabHidden->id]);

        $entry = $this->muffin(Entry::class, [
            'submitted_at' => Carbon::now(),
            'category_id' => $category->id,
        ]);

        $this->muffin(Attachment::class, ['submittable_id' => $entry->id, 'tab_id' => $tabVisible->id, 'file_id' => $this->muffin(File::class)->id]);
        $this->muffin(Attachment::class, ['submittable_id' => $entry->id, 'tab_id' => $tabHidden->id, 'file_id' => $this->muffin(File::class)->id]);
        $scoreSets = $this->muffins(2, ScoreSet::class, ['attachments' => true, 'attachmentTypes' => 'A', 'mode' => ScoreSet::MODE_VIP]);

        $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSets[0]->id,
            'role_id' => $this->role->id,
            'judge_id' => $this->user->id,
            'entry_id' => $entry->id,
        ]);

        $this->assertCount(1, app(ScoringFormComposer::class)->attachments($entry, collect($scoreSets)));
    }

    public function testItShouldHaveRefereesKey()
    {
        $category = $this->muffin(Category::class);
        $tabVisible = $this->muffin(Tab::class, ['category_option' => Tab::CATEGORY_OPTION_SELECT, 'type' => Tab::TYPE_ATTACHMENTS, 'visible_to_entrants' => 1]);
        $tabHidden = $this->muffin(Tab::class, ['category_option' => Tab::CATEGORY_OPTION_SELECT, 'type' => Tab::TYPE_ATTACHMENTS, 'visible_to_entrants' => 0]);
        $category->tabs()->attach([$tabVisible->id, $tabHidden->id]);
        $scoreSet = $this->muffin(ScoreSet::class, ['attachments' => true, 'attachmentTypes' => 'A', 'mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, false, false))]);
        $refereeField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES, 'tab_id' => $tabVisible->id, 'type' => Field::TYPE_TEXT]);
        $scoreSet->fields()->attach($refereeField->id);
        $refereeFieldHidden = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES, 'tab_id' => $tabHidden->id, 'type' => Field::TYPE_TEXT]);

        $entry = $this->muffin(Entry::class, [
            'submitted_at' => Carbon::now(),
            'category_id' => $category->id,
        ]);

        $referee = $this->muffin(Referee::class, ['submittable_id' => $entry->id]);

        $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSet->id,
            'role_id' => $this->role->id,
            'judge_id' => $this->user->id,
            'entry_id' => $entry->id,
        ]);

        app(ValuesService::class)->setValuesForObject(
            [
                (string) $refereeField->slug => $shouldShowValue = 'SomeValue',
                (string) $refereeFieldHidden->slug => 'SomeOtherValue',
            ],
            $referee
        );

        $view = m::spy(View::class);
        $view->entry = $entry;

        app(ScoringFormComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) use ($shouldShowValue) {
            $this->assertArrayHasKey('referees', $args);
            $this->assertInstanceOf(ResourceFieldsCollection::class, $args['referees']);
            $this->assertInstanceOf(Referee::class, $args['referees']->first()->resource);
            $this->assertCount(1, $args['referees']->first()->fields);
            $this->assertEquals($shouldShowValue, $args['referees']->first()->fields->first()->value);
            $this->assertArrayHasKey('refereeFields', $args);
            $this->assertEquals(['name', 'email'], $args['refereeFields']);

            return true;
        });
    }

    public function testShouldHideRefereeFieldsIfAtLeastOneScoreSetIsConfiguredToHideThem()
    {
        $scoreSetWithHiddenRefereeNameAndEmail = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, true, true))]);
        $scoreSetWithVisibleRefereeNameAndEmail = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, false, false))]);
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);
        $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSetWithHiddenRefereeNameAndEmail->getKey(),
            'role_id' => $this->role->id,
            'judge_id' => $this->user->id,
            'entry_id' => $entry->id,
        ]);
        $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSetWithVisibleRefereeNameAndEmail->getKey(),
            'role_id' => $this->role->id,
            'judge_id' => $this->user->id,
            'entry_id' => $entry->id,
        ]);
        $view = m::spy(View::class);
        $view->entry = $entry;

        app(ScoringFormComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) {
            $this->assertArrayHasKey('refereeFields', $args);
            $this->assertEmpty(data_get($args, 'refereeFields'));

            return true;

        });
    }

    public function testShouldOnlyReturnRefereeFieldsIfAllScoreSetsAreConfiguredNotToHideThem()
    {
        $scoreSets = collect($this->muffins(2, ScoreSet::class, ['attachments' => true, 'attachmentTypes' => 'A', 'mode' => ScoreSet::MODE_VIP, 'settings' => new ScoreSetSettings(new RefereeSettings(true, false, false))]));
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);
        $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSets->first()->getKey(),
            'role_id' => $this->role->id,
            'judge_id' => $this->user->id,
            'entry_id' => $entry->id,
        ]);
        $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSets->last()->getKey(),
            'role_id' => $this->role->id,
            'judge_id' => $this->user->id,
            'entry_id' => $entry->id,
        ]);
        $view = m::spy(View::class);
        $view->entry = $entry;

        app(ScoringFormComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) {
            $this->assertArrayHasKey('refereeFields', $args);
            $this->assertEquals(['name', 'email'], data_get($args, 'refereeFields'));

            return true;

        });
    }

    public function testItShouldReturnInternalCommentsEnabled()
    {
        $category = $this->muffin(Category::class);
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now(), 'category_id' => $category->id]);
        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'internalComments' => true]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSet->getKey(), 'role_id' => $this->role->id, 'judge_id' => $this->user->id, 'entry_id' => $entry->id]);
        $view = m::spy(View::class);
        $view->entry = $entry;

        app(ScoringFormComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) {
            $this->assertArrayHasKey('internalComments', $args);
            $this->assertTrue(array_get($args, 'internalComments'));

            return true;
        });
    }

    public function testItShouldReturnInternalCommentsDisabled()
    {
        $category = $this->muffin(Category::class);
        $entry = $this->muffin(Entry::class, ['submitted_at' => Carbon::now(), 'category_id' => $category->id]);
        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VIP, 'internalComments' => false]);
        $this->muffin(Assignment::class, ['score_set_id' => $scoreSet->getKey(), 'role_id' => $this->role->id, 'judge_id' => $this->user->id, 'entry_id' => $entry->id]);
        $view = m::spy(View::class);
        $view->entry = $entry;

        app(ScoringFormComposer::class)->compose($view);

        $view->shouldHaveReceived('with')->withArgs(function (array $args) {
            $this->assertArrayHasKey('internalComments', $args);
            $this->assertFalse(array_get($args, 'internalComments'));

            return true;
        });
    }
}
