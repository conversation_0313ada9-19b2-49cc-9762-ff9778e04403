<?php

namespace AwardForce\Modules\Identity\Roles\Services;

use Illuminate\Support\Collection;

class PermissionResourcesService
{
    /**
     * The following array represents the default resources supported by Shift 2.
     *
     * @var array
     */
    protected $resources = [
        'ApiKeys' => ['create', 'view', 'update', 'delete'],
        'Audit' => ['view'],
        'Broadcasts' => ['create', 'view', 'update', 'delete'],
        'Categories' => ['create', 'view', 'update', 'delete'],
        'Chapters' => ['create', 'view', 'update', 'delete'],
        'Content' => ['create', 'view', 'update', 'delete'],
        'Documents' => ['create', 'view', 'update', 'delete'],
        'DocumentTemplates' => ['create', 'view', 'update', 'delete'],
        'EntriesAll' => ['create', 'view', 'update', 'delete'],
        'ContractAll' => ['view', 'delete'],
        'EntriesOwner' => ['create', 'view', 'update', 'delete'],
        'Fields' => ['create', 'view', 'update', 'delete'],
        'Funding' => ['create', 'view', 'update', 'delete'],
        'Forms' => ['create', 'view', 'update', 'delete'],
        'Grants' => ['create', 'view', 'update', 'delete'],
        'Integrations' => ['create', 'view', 'update', 'delete'],
        'Notifications' => ['create', 'view', 'update', 'delete'],
        'Orders' => ['create', 'view', 'update', 'delete'],
        'Organisations' => ['create', 'view', 'update', 'delete'],
        'Panels' => ['create', 'view', 'update', 'delete'],
        'Payments' => ['create', 'view', 'update', 'delete'],
        'Roles' => ['create', 'view', 'update', 'delete'],
        'Rounds' => ['create', 'view', 'update', 'delete'],
        'ScoresAll' => ['create', 'view', 'update', 'delete'],
        'ScoresOwner' => ['create', 'view', 'update', 'delete'],
        'ScoringCriteria' => ['create', 'view', 'update', 'delete'],
        'ScoreSets' => ['create', 'view', 'update', 'delete'],
        'Seasons' => ['create', 'view', 'update', 'delete'],
        'Settings' => ['create', 'view', 'update', 'delete'],
        'Tags' => ['create', 'view', 'update', 'delete'],
        'Users' => ['create', 'view', 'update', 'delete'],
        'Webhooks' => ['create', 'view', 'update', 'delete'],
    ];

    /**
     * @var array
     */
    protected $allowedGuestResources = [
        'ScoresOwner' => ['create', 'view', 'update', 'delete'],
    ];

    /**
     * @var array
     */
    protected $allowedRegistrationResources = [
        'EntriesOwner' => ['create', 'view', 'update', 'delete'],
        'ScoresOwner' => ['create', 'view', 'update', 'delete'],
    ];

    protected array $disallowedChapterLimitedResources = [
        'ApiKeys' => ['create', 'view', 'update', 'delete'],
        'Chapters' => ['create', 'update', 'delete'],
        'Forms' => ['create', 'update', 'delete'],
        'Integrations' => ['create', 'view', 'update', 'delete'],
        'Roles' => ['create', 'update', 'delete'],
        'Webhooks' => ['create', 'view', 'update', 'delete'],
    ];

    /**
     * The available permitted actions for all resources.
     *
     * @var array
     */
    protected $actions = [
        'create',
        'view',
        'update',
        'delete',
    ];

    protected array $sensitiveResources = [
        'ApiKeys',
        'Audit',
        'Integrations',
        'Settings',
        'Webhooks',
    ];

    /**
     * Add a resource to the resources array. Will only add the resource if it does
     * not already exist within the array.
     */
    public function add(string $resource)
    {
        if (! $this->exists($resource)) {
            $this->resources[$resource] = $this->actions;
        }
    }

    /**
     * Determines whether or not a resource exists within the resource registry.
     */
    public function exists(string $resource): bool
    {
        return isset($this->resources[$resource]);
    }

    /**
     * Return the array of permitted actions.
     */
    public function actions(): array
    {
        return $this->actions;
    }

    /**
     * Retrieve all permission resources, except the ones listed.
     *
     * @param  string[]  $resources
     */
    public function except(string ...$resources): array
    {
        return array_filter($this->get(), function ($value) use ($resources) {
            return ! in_array($value, $resources);
        });
    }

    /**
     * Inverse of except - grabbing only the required resources.
     *
     * @param  string[]  $resources
     */
    public function only(string ...$resources): array
    {
        return array_filter($this->get(), function ($value) use ($resources) {
            return in_array($value, $resources);
        });
    }

    /**
     * Return all registered resources.
     *
     * @return array
     */
    public function get()
    {
        return array_keys($this->all()->toArray());
    }

    /**
     * Returns all resources, and their available actions.
     *
     * @param  null  $keys
     */
    public function all($keys = null): Collection
    {
        $featureMap = config('features.permissions', []);

        return collect($this->resources)->filter(function ($resource, $key) use ($featureMap) {
            return ! isset($featureMap[$key]) || feature_enabled($featureMap[$key]);
        });
    }

    /**
     * Returns all guest resources
     *
     * @return array
     */
    public function getGuest()
    {
        return $this->allowedGuestResources;
    }

    /**
     * Returns true if guest users are allowed to be assigned the specific resource action.
     *
     * @param  string  $resource
     * @param  string  $action
     * @return bool
     */
    public function isGuestAllowed($resource, $action)
    {
        return isset($this->allowedGuestResources[$resource]) && in_array($action, $this->allowedGuestResources[$resource]);
    }

    /**
     * Returns all allowed public registration resources
     *
     * @return array
     */
    public function getRegistration()
    {
        return $this->allowedRegistrationResources;
    }

    /**
     * Returns true if public registration users are allowed to be assigned the specific resource action.
     *
     * @param  string  $resource
     * @param  string  $action
     * @return bool
     */
    public function isRegistrationAllowed($resource, $action)
    {
        return isset($this->allowedRegistrationResources[$resource]) && in_array($action, $this->allowedRegistrationResources[$resource]);
    }

    public function isChapterLimitedNotAllowed(string $resource, string $action): bool
    {
        return isset($this->disallowedChapterLimitedResources[$resource]) && in_array($action, $this->disallowedChapterLimitedResources[$resource]);
    }

    public function isSensitiveResource(string $resource): bool
    {
        return in_array($resource, $this->sensitiveResources);
    }

    public function getFormattedPermissions(?\Closure $reject = null): Collection
    {
        return $this->all()
            ->reject($reject)
            ->map(function ($actions, $resource) use (&$formatted) {
                return $formatted[$resource] = [
                    'resource' => $resource,
                    'name' => trans('roles.matrix.permissions.'.$resource.'.name'),
                    'actions' => $actions,
                    'description' => trans('roles.matrix.permissions.'.$resource.'.description'),
                ];
            })
            ->sortBy('name');
    }
}
