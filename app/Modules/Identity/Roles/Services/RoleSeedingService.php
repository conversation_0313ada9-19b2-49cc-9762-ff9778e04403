<?php

namespace AwardForce\Modules\Identity\Roles\Services;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Identity\Roles\Contracts\PermissionRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Roles\Models\RoleFactory;
use AwardForce\Modules\Identity\Roles\ValueObjects\Mode;

/**
 * Class RoleSeedingService
 *
 * Used to create the default roles and permissions for a given account, using
 * the roles and actions permitted provided by the PermissionResourcesService.
 */
class RoleSeedingService
{
    public function __construct(
        private PermissionRepository $permissions,
        private PermissionResourcesService $permissionResources,
        private RoleFactory $roleFactory,
        private RoleRepository $roles
    ) {
    }

    /**
     * The chapter manager looks after the entries and users of a given chapter.
     */
    public function setupChapterManager(Account $account): Role
    {
        $chapterManager = $this->roleFactory->add(false, false, true);
        $chapterManager->accountId = $account->id;

        $this->roles->save($chapterManager);

        $this->addTranslation($account, $chapterManager, 'chapter-manager');
        $this->addPermissions(
            $chapterManager,
            $this->permissionResources->only('EntriesAll', 'Panels', 'Tags')
        );

        if ($users = $this->permissionResources->only('Users')) {
            $this->addPermission($chapterManager, array_values($users)[0], 'view');
        }

        return $chapterManager;
    }

    /**
     * Create the convenor role for the account. Convenors generally have the same
     * level of permissions as the owner.
     */
    public function setupProgramManager(Account $account): Role
    {
        $programManager = $this->roleFactory->add(false);
        $programManager->accountId = $account->id;

        $this->roles->save($programManager);
        $this->addTranslation($account, $programManager, 'program-manager');
        $this->addPermissions($programManager, $this->permissionResources->except('ScoresOwner'));

        return $programManager;
    }

    /**
     * Application managers are the top-level managers of the platform. Basically, people who
     * work for Tectonic and help with supporting our clients.
     */
    public function setupApplicationManager(Account $account): Role
    {
        $appManager = $this->roleFactory->add(false);
        $appManager->accountId = $account->id;

        $this->roles->save($appManager);
        $this->addTranslation($account, $appManager, 'app-manager');
        $this->addPermissions($appManager, $this->permissionResources->except('ScoresOwner'));
        $this->addPermissions($appManager, ['AccountsAll']);

        return $appManager;
    }

    public function setupJudge(Account $account): Role
    {
        $judge = $this->roleFactory->add(false);
        $judge->accountId = $account->id;

        $this->roles->save($judge);

        $this->addTranslation($account, $judge, 'judge');
        $this->addPermissions($judge, ['ScoresOwner']);

        return $judge;
    }

    public function setupLeadJudge(Account $account): Role
    {
        $judge = $this->roleFactory->add(false);
        $judge->accountId = $account->id;

        $this->roles->save($judge);

        $this->addTranslation($account, $judge, 'lead-judge');
        $this->addPermissions($judge, [
            'ScoresOwner',
            'ScoresAll',
        ]);

        return $judge;
    }

    public function setupBookkeeper(Account $account): Role
    {
        $bookkeeper = $this->roleFactory->add(false);
        $bookkeeper->accountId = $account->id;

        $this->roles->save($bookkeeper);
        $this->addTranslation($account, $bookkeeper, 'bookkeeper');
        $this->addPermissions($bookkeeper, [
            'Funding' => ['view'],
            'Grants' => ['view'],
            'Orders' => ['view', 'update'],
            'Organisations' => ['view'],
            'Payments' => ['create', 'view', 'update', 'delete'],
        ]);

        return $bookkeeper;
    }

    public function setupAuditor(Account $account): Role
    {
        $auditor = $this->roleFactory->add(false);
        $auditor->accountId = $account->id;

        $this->roles->save($auditor);
        $this->addTranslation($account, $auditor, 'auditor');
        $this->addPermissions($auditor, [
            'EntriesAll' => ['view'],
            'Audit' => ['view'],
            'Broadcasts' => ['view'],
            'ContractAll' => ['view'],
            'Documents' => ['view'],
            'DocumentTemplates' => ['view'],
            'Forms' => ['view'],
            'Funding' => ['view'],
            'Grants' => ['view'],
            'Notifications' => ['view'],
            'Orders' => ['view'],
            'Organisations' => ['view'],
            'Payments' => ['view'],
            'Panels' => ['view'],
            'Roles' => ['view'],
            'Rounds' => ['view'],
            'ScoreSets' => ['view'],
            'ScoresAll' => ['view'],
            'ScoringCriteria' => ['view'],
            'Seasons' => ['view'],
            'Users' => ['view'],
        ]);
        $this->addPermissions($auditor, [
            'EntriesOwner' => ['create', 'view', 'update', 'delete'],
            'ScoresOwner' => ['create', 'view', 'update', 'delete'],
        ], 'deny');

        return $auditor;
    }

    public function setupEntrant(Account $account): Role
    {
        $entrant = $this->roleFactory->add(true);
        $entrant->accountId = $account->id;

        $this->roles->save($entrant);
        $this->addTranslation($account, $entrant, 'entrant');
        $this->addPermissions($entrant, ['EntriesOwner']);

        return $entrant;
    }

    public function setupVoter(Account $account): ?Role
    {
        if (! $account->isAwardForce()) {
            return null;
        }

        $voter = $this->roleFactory->add(false);
        $voter->accountId = $account->id;

        $this->roles->save($voter);
        $this->addTranslation($account, $voter, 'voter');
        $this->addPermissions($voter, ['ScoresOwner']);

        return $voter;
    }

    /**
     * Set up the guest role for the account.
     */
    public function setupGuest(Account $account): Role
    {
        $guest = $this->roleFactory->add(false, true);
        $guest->accountId = $account->id;

        $this->roles->save($guest);
        $this->addTranslation($account, $guest, 'guest');

        return $guest;
    }

    /**
     * Add a translation for the name of the role, to the account, and its default language code.
     */
    private function addTranslation(Account $account, Role $role, string $name): void
    {
        foreach ($account->languages as $language) {
            $role->saveTranslation(
                $language->code,
                'name',
                trans('roles.'.Vertical::translationsKey().'.'.$name, [], $language->code),
                $account->id
            );
        }
    }

    /**
     * Add the required permissions to the role.
     *
     * @internal param $resources
     */
    private function addPermissions(Role $role, array $resources, string $mode = 'allow'): void
    {
        foreach ($resources as $resource => $permissions) {
            // If it's not an array, it means that the $permissions array is actually the resource, and
            // we need to create a separate permission record for all available actions.
            if (! is_array($permissions)) {
                $resource = $permissions;
                $permissions = ['create', 'view', 'update', 'delete'];
            }

            foreach ($permissions as $action) {
                $this->addPermission($role, $resource, $action, $mode);
            }
        }
    }

    /**
     * Add the required permission to the role.
     */
    private function addPermission(Role $role, string $resource, string $action, string $mode = 'allow'): void
    {
        $permission = new Permission([
            'resource' => $resource,
            'action' => $action,
            'mode' => new Mode($mode),
        ]);

        $permission->roleId = $role->id;

        $this->permissions->save($permission);
    }
}
