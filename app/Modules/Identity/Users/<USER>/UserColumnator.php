<?php

namespace AwardForce\Modules\Identity\Users\Search;

use AwardForce\Library\Search\Actions\AddToOrganisationListAction;
use AwardForce\Library\Search\Actions\BlockUserAction;
use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Library\Search\Actions\EmulateUserAction;
use AwardForce\Library\Search\Actions\FormInvitationAction;
use AwardForce\Library\Search\Actions\ShowEditAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Columns\Star;
use AwardForce\Library\Search\SeasonalColumnator;
use AwardForce\Modules\Entries\Search\Filters\FieldColumnOrderFilter;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Search\Columns\SearchField;
use AwardForce\Modules\Forms\Fields\Search\Columns\UserFields;
use AwardForce\Modules\Forms\Fields\Search\Enhancers\UserFieldValuesEnhancer;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Search\Actions\CreateDocumentAction;
use AwardForce\Modules\Identity\Users\Search\Columns\AnalyticsCookies;
use AwardForce\Modules\Identity\Users\Search\Columns\BroadcastEmails;
use AwardForce\Modules\Identity\Users\Search\Columns\Comments;
use AwardForce\Modules\Identity\Users\Search\Columns\Confirmation;
use AwardForce\Modules\Identity\Users\Search\Columns\ConfirmationDate;
use AwardForce\Modules\Identity\Users\Search\Columns\Created;
use AwardForce\Modules\Identity\Users\Search\Columns\CreatedBy;
use AwardForce\Modules\Identity\Users\Search\Columns\Email;
use AwardForce\Modules\Identity\Users\Search\Columns\FirstName;
use AwardForce\Modules\Identity\Users\Search\Columns\Language;
use AwardForce\Modules\Identity\Users\Search\Columns\LastName;
use AwardForce\Modules\Identity\Users\Search\Columns\MarketingCookies;
use AwardForce\Modules\Identity\Users\Search\Columns\Mobile;
use AwardForce\Modules\Identity\Users\Search\Columns\Name;
use AwardForce\Modules\Identity\Users\Search\Columns\NecessaryCookies;
use AwardForce\Modules\Identity\Users\Search\Columns\NotificationEmails;
use AwardForce\Modules\Identity\Users\Search\Columns\NotificationSms;
use AwardForce\Modules\Identity\Users\Search\Columns\Preferences;
use AwardForce\Modules\Identity\Users\Search\Columns\ProfilePhoto;
use AwardForce\Modules\Identity\Users\Search\Columns\Roles;
use AwardForce\Modules\Identity\Users\Search\Columns\SocialSharing;
use AwardForce\Modules\Identity\Users\Search\Enhancers\Comments as CommentsEnhancer;
use AwardForce\Modules\Identity\Users\Search\Enhancers\TemporaryAuthLock;
use AwardForce\Modules\Identity\Users\Search\Filters\AuthenticatorEnabledFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\ConfirmationFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\LanguageFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\RoleFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\SeasonJoinedFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\TrashedUsersFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\UserAccountFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\UserEntriesFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\UserFieldSearchFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\UserKeywordFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\UserSlugFilter;
use AwardForce\Modules\Identity\Users\Search\Filters\WithActivityInSeasonFilter;
use AwardForce\Modules\Settings\Services\Settings;
use AwardForce\Modules\Stars\Search\Filters\StarredSearchFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\PaginationFilter;

class UserColumnator extends Columnator implements ApiColumnator
{
    use ApiColumns;
    use SeasonalColumnator;

    protected function baseColumns(): Columns
    {
        return new Columns([
            new ReactiveMarker,
            $this->actionOverflow(),
            new Star,
            Slug::forResource('users'),
            new ProfilePhoto,
            new Name,
            new FirstName,
            new LastName,
            new Email,
            new Mobile,
            new Roles,
            new ConfirmationDate,
            new Confirmation,
            new BroadcastEmails,
            new NotificationEmails,
            new NotificationSms,
            new NecessaryCookies,
            new AnalyticsCookies,
            new MarketingCookies,
            new SocialSharing,
            new Created,
            new CreatedBy,
            new Preferences,
            app(UserFields::class)->setFieldColumns($this->fieldColumns()),
            Updated::forResource('users', consumer()->dateLocale()),
            new Language,
            new Comments,
        ]);
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $columns = $this->columns($view);
        $dependencies = new Dependencies;

        // Filters
        $dependencies->add((new ColumnFilter(...$columns))->with('users.id', 'users.slug', 'users.invalid_channels', 'users.first_name', 'users.last_name', 'users.created_by', 'users.email'));
        $dependencies->add(new AuthenticatorEnabledFilter);
        $dependencies->add(new IncludeFilter('currentMembership'));
        $dependencies->add(new RoleFilter($this->input));
        $dependencies->add(new TrashedUsersFilter($this->input));
        $dependencies->add(UserKeywordFilter::frominput($this->input, ['first_name', 'last_name', 'email', 'users.slug']));
        $dependencies->add(new ConfirmationFilter($this->input));
        $dependencies->add(new LanguageFilter($this->input));
        $dependencies->add(new SeasonJoinedFilter($this->input));
        $dependencies->add(new WithActivityInSeasonFilter($this->input));
        $dependencies->add(new UserAccountFilter);
        $dependencies->add(UserFieldSearchFilter::fromInput($this->input));
        $dependencies->add(new UserSlugFilter($this->input));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new GroupingFilter('memberships.user_id'));
        $dependencies->add(FieldColumnOrderFilter::fromColumns($columns, $this->input, 'users.updated')->uniqueColumn('memberships.user_id'));

        $joinRoles = empty($this->input['role']) || $this->input['role'] == 'owner';
        $dependencies->add(new UserEntriesFilter($this->input, $joinRoles));

        // Enhancers
        $dependencies->add(UserFieldValuesEnhancer::fromColumns($columns));
        $dependencies->add(app(TemporaryAuthLock::class));
        $dependencies->add(app(CommentsEnhancer::class));

        $dependencies->add(new StarredSearchFilter($this->input, new User));

        return $dependencies;
    }

    public function resource()
    {
        return Field::RESOURCE_USERS;
    }

    protected function fieldColumns()
    {
        $fields = translate(app(FieldRepository::class)->getByResourceWhereNot($this->resource(), 'content', $this->seasonId()));

        return $fields->map(function (Field $field) {
            return SearchField::fromField($field, 'users.id');
        });
    }

    public static function key(): string
    {
        return 'users.search';
    }

    public static function exportKey(): string
    {
        return 'users.export';
    }

    public function repository(): Repository
    {
        return app(UserRepository::class);
    }

    private function actionOverflow(): ActionOverflow
    {
        return (new ActionOverflow($this->key()))
            ->addAction(new ShowEditAction('users', $this->resource()))
            ->when(
                $this->showAddToOrganisation(),
                fn($actionOverflow) => $actionOverflow->addAction(new AddToOrganisationListAction('organisations', $this->resource()))
            )
            ->addAction(new DeleteAction('user', $this->resource()))
            ->addAction(new BlockUserAction('users', $this->resource()))
            ->addAction(new FormInvitationAction('users', $this->resource(), true))
            ->when(\Consumer::isOwnerOrProgramManager() && ! trashed_filter_active(), fn($actionOverflow) => $actionOverflow->addAction(new EmulateUserAction))
            ->addAction(new CreateDocumentAction('user', $this->resource()));
    }

    private function showAddToOrganisation(): bool
    {
        return feature_enabled('organisations') &&
            app(Settings::class)->organisationSettings()->managerAdd;
    }
}
