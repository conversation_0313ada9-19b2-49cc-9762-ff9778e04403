<?php

namespace AwardForce\Modules\Identity\Users\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Search\Actions\FormInvitationAction;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Search\SearchRepository;
use AwardForce\Modules\Search\Services\ActiveSettings;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Search\Services\SavedViews;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Settings\Services\Settings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

class SearchUsers extends View
{
    use SavedViews;

    /**
     * SearchUsers constructor.
     */
    public function __construct(
        private Request $request,
        private SeasonRepository $seasons,
        private Engine $translator,
        private RoleRepository $roleRepository,
        private SearchRepository $searchSettings,
        private ActiveSettings $activeSettings,
        private ColumnatorFactory $columnatorFactory,
        private FieldRepository $fields,
        private Settings $settings
    ) {
    }

    public function users()
    {
        $search = new ColumnatorSearch($this->columnator);
        $results = translate($search->search());

        return $results;
    }

    public function area()
    {
        return 'users.search';
    }

    public function columnator()
    {
        return $this->columnatorFactory->forArea('users.search', $this->request->all());
    }

    public function canConfirm()
    {
        return Consumer::can('update', 'Users');
    }

    public function canDelete()
    {
        return Consumer::can('delete', 'Users');
    }

    public function entriesFilterOptions()
    {
        return [
            '' => '',
            'with-entries-current-season' => trans('users.search.entries.options.with-entries-current-season'),
            'no-entries-current-season' => trans('users.search.entries.options.no-entries-current-season'),
            'with-entries-archived-season' => trans('users.search.entries.options.with-entries-archived-season'),
            'no-entries' => trans('users.search.entries.options.no-entries'),
        ];
    }

    public function confirmationFilterOptions()
    {
        return [
            '' => '',
            'confirmed' => trans('users.table.confirmed.active'),
            'pending' => trans('users.table.confirmed.pending'),
        ];
    }

    public function roles()
    {
        return $this->translator->shallow($this->roleRepository->getAllWithPermissions());
    }

    public function filterableRoles()
    {
        $roles = clone $this->roles;

        return for_select_sorted($roles->withOwner(), null, true);
    }

    public function accountOwnerId()
    {
        return current_account()->userId;
    }

    public function assignableRoles()
    {
        $roles = $this->roles;

        if (! $this->user()->ownerOf(current_account())) {
            $roles = $this->roles->assignable($this->user()->highestRolePermissionLevel());
        }

        return translate($roles)
            ->map(function (Role $role) {
                return [
                    'id' => $role->id,
                    'name' => lang($role, 'name'),
                ];
            })
            ->sortBy('name')
            ->values()
            ->all();
    }

    public function removableRoles()
    {
        $roles = $this->roles;

        if (! $this->user()->ownerOf(current_account())) {
            $roles = $this->roles->revokable($this->user()->highestRolePermissionLevel());
        }

        return translate($roles)
            ->map(function (Role $role) {
                return [
                    'id' => $role->id,
                    'name' => lang($role, 'name'),
                ];
            })
            ->sortBy('name')
            ->values()
            ->all();
    }

    public function user(): User
    {
        return Auth::user();
    }

    public function hasIncompleteResults(): bool
    {
        $ids = array_filter(array_values($this->request->all(['season_joined', 'active-in-season'])));

        if (empty($ids)) {
            return false;
        }

        return $this->seasons->wereCreatedBefore($ids, config('awardforce.milestones.activity-logging'));
    }

    public function usersIds()
    {
        return $this->users->pluck('id')->toArray();
    }

    public function hasFileFields()
    {
        return (bool) $this->fields->countFieldsOfType(Field::RESOURCE_USERS, Field::TYPE_FILE);
    }

    public function hasDocuments(): bool
    {
        return feature_enabled('documents');
    }

    public function translations()
    {
        return FormInvitationAction::translations();
    }

    public function showAddToOrganisation(): bool
    {
        return feature_enabled('organisations') &&
            $this->settings->organisationSettings()->managerAdd;
    }
}
