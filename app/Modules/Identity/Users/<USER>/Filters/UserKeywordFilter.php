<?php

namespace AwardForce\Modules\Identity\Users\Search\Filters;

use Illuminate\Support\Str;
use Platform\Search\Filters\KeywordFilter;

class UserKeywordFilter extends KeywordFilter
{
    public function applyToEloquent($query)
    {
        if ($keywords = $this->keywords) {
            $keywordsSplit = preg_split('/\\s+/', $keywords);

            if ($this->isEmail($keywordsSplit)) {
                $query->where('users.email', '=', $keywords);
            } elseif ($this->isPartialEmail($keywordsSplit)) {
                $query->where('users.email', 'LIKE', '%'.html_entity_decode($keywords, ENT_QUOTES).'%');
            } elseif ($this->containsReservedCharacter($keywords)) {
                $query->where(function ($query) use ($keywords, $keywordsSplit) {
                    $query->orWhere('users.first_name', 'LIKE', '%'.$keywordsSplit[0].'%');
                    $query->orWhere('users.last_name', 'LIKE', '%'.implode(' ', $keywordsSplit).'%');
                    $query->orWhere('users.email', 'LIKE', '%'.$keywords.'%');
                });
            } else {
                $query->whereRaw('MATCH(users.first_name, users.last_name, users.email) AGAINST (? IN BOOLEAN MODE)', $keywords);
            }
        }

        return $query;
    }

    private function isEmail($keywordsSplit)
    {
        return filter_var($keywordsSplit[0], FILTER_VALIDATE_EMAIL) !== false;
    }

    private function isPartialEmail($keywordsSplit)
    {
        return count($keywordsSplit) == 1 && (Str::contains($keywordsSplit[0], '@') || Str::contains($keywordsSplit[0], '.'));
    }

    private function containsReservedCharacter($keywordsSplit)
    {
        return Str::contains($keywordsSplit, ['-', '+', '<', '>', '(', ')', '~', '*', '@', '\'', '"', '%']);
    }
}
