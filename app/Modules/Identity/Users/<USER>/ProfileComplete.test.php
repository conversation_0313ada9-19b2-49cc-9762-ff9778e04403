<?php

namespace AwardForce\Modules\Identity\Users\Middleware;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Agreements\Services\Consent;
use AwardForce\Modules\Forms\Fields\Services\RequiredUserFieldsService;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Roles\Models\Roles;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Illuminate\Http\Request;
use Illuminate\Session\Store;
use Illuminate\Support\Str;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class ProfileCompleteTest extends BaseTestCase
{
    use Laravel;

    const SEASON = 3;

    const USER = 7;

    const ROLE = 9;

    /**
     * @var m\MockInterface
     */
    protected $request;

    /**
     * @var m\MockInterface
     */
    protected $session;

    /**
     * @var m\MockInterface
     */
    protected $requiredFields;

    /**
     * @var m\MockInterface
     */
    protected $seasons;

    /**
     * @var m\MockInterface
     */
    protected $manager;

    /**
     * @var m\MockInterface
     */
    protected $consent;

    /**
     * @var ProfileComplete
     */
    protected $middleware;

    public function init()
    {
        $this->request = $this->mock(Request::class);
        $this->request->shouldReceive('session')->andReturn($this->session = $this->mock(Store::class));

        $this->requiredFields = $this->mock(RequiredUserFieldsService::class);

        $this->seasons = $this->mock(SeasonRepository::class);
        $this->seasons->shouldReceive('getActiveId')->andReturn(self::SEASON);

        $this->manager = $this->mock(Manager::class);
        $this->manager->shouldReceive('get')->andReturnSelf();
        $this->manager->shouldReceive('id')->andReturn(self::USER);
        $this->manager->shouldReceive('roles')->andReturn($this->roles());
        $this->request->shouldReceive('ajax')->andReturnFalse()->byDefault();

        $this->consent = $this->mock(Consent::class);

        $this->middleware = new ProfileComplete($this->requiredFields, $this->seasons, $this->manager, $this->consent);
    }

    public function testNextIfNoFieldsRequiredInSession(): void
    {
        $this->manager->shouldReceive('isGuest')->once()->andReturn(false);

        $this->session->shouldReceive('has')->andReturn(false);
        $this->session->shouldReceive('get')->with('role.requested')->once()->andReturn(null);
        $this->session->shouldReceive('get')->with('profile.completed', false)->once()->andReturn(true);
        $this->session->shouldReceive('put')->with('profile.completed', true);

        $response = $this->middleware->handle($this->request, function ($request) {
            $this->assertEquals($this->request, $request);

            return 'One Ring to rule them all';
        });
        $this->assertEquals('One Ring to rule them all', $response);
    }

    public function testNextIfNoFieldsRequiredNotInSession(): void
    {
        $this->manager->shouldReceive('isGuest')->once()->andReturn(false);
        $this->manager->shouldReceive('isManager')->once()->andReturn(false);
        $this->manager->shouldReceive('user')->once()->andReturn(new User(['id' => self::USER]));

        $this->session->shouldReceive('has')->andReturn(false);
        $this->session->shouldReceive('get')->with('profile.completed', false)->once()->andReturn(false);
        $this->session->shouldReceive('get')->with('role.requested')->once()->andReturn(null);

        $userMock = $this->mock(User::class);
        $userMock->shouldReceive('hasName')->andReturnTrue();
        $this->request->shouldReceive('user')->andReturn($userMock);

        $this->requiredFields->shouldReceive('valid')
            ->with(m::type(User::class), self::SEASON, m::any(Roles::class), false)->once()
            ->andReturn(true);

        $this->manager->shouldReceive('get->user')->andReturn($this->mock(User::class));
        $this->consent->shouldReceive('collected')->andReturn(true);

        $this->session->shouldReceive('put')
            ->with('profile.completed', true)->once();

        $response = $this->middleware->handle($this->request, function ($request) {
            $this->assertEquals($this->request, $request);

            return 'One Ring to find them';
        });

        $this->assertEquals('One Ring to find them', $response);
    }

    public function testRedirectIfFieldsRequired(): void
    {
        $this->manager->shouldReceive('isGuest')->once()->andReturn(false);
        $this->manager->shouldReceive('isManager')->once()->andReturn(false);
        $this->manager->shouldReceive('user')->once()->andReturn(new User(['id' => self::USER]));

        $this->session->shouldReceive('has')->andReturn(false);
        $this->session->shouldReceive('get')->with('profile.completed', false)->once()->andReturn(false);
        $this->session->shouldReceive('get')->with('role.requested')->once()->andReturn(null);

        $this->requiredFields->shouldReceive('valid')
            ->with(m::type(User::class), self::SEASON, m::any(Roles::class), false)->once()
            ->andReturn(false);

        $this->request->shouldReceive('fullUrl')->once()->andReturn('https://full/url');
        $this->session->shouldReceive('put')->with('intended', 'https://full/url')->once();

        $response = $this->middleware->handle($this->request, function ($request) {
            throw new \Exception('This should not be thrown!');
        });

        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertTrue(Str::contains($response->getTargetUrl(), 'profile/complete'));
    }

    public function testRedirectIfPendingRoleRegistration(): void
    {
        $this->manager->shouldReceive('isGuest')->once()->andReturn(false);
        $this->session->shouldReceive('has')->andReturn(false);
        $this->session->shouldReceive('get')->with('role.requested')->once()->andReturn('rOlEsLuG');

        $this->request->shouldReceive('fullUrl')->once()->andReturn('https://full/url');
        $this->session->shouldReceive('put')->with('intended', 'https://full/url')->once();

        $response = $this->middleware->handle($this->request, function ($request) {
            throw new \Exception('This should not be thrown!');
        });

        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertTrue(Str::contains($response->getTargetUrl(), ['profile/complete', 'rOlEsLuG']));
    }

    public function testNextIfGuest(): void
    {
        $this->manager->shouldReceive('isGuest')->once()->andReturn(true);

        $response = $this->middleware->handle($this->request, function ($request) {
            $this->assertEquals($this->request, $request);

            return 'One Ring to bring them all';
        });
        $this->assertEquals('One Ring to bring them all', $response);
    }

    public function testRedirectIfUserDoesNotHaveFilledName(): void
    {
        $this->manager->shouldReceive('isGuest')->once()->andReturn(false);
        $this->manager->shouldReceive('isManager')->once()->andReturn(false);
        $this->manager->shouldReceive('user')->once()->andReturn(new User(['id' => self::USER]));

        $this->session->shouldReceive('has')->andReturn(false);
        $this->session->shouldReceive('get')->with('profile.completed', false)->once()->andReturn(false);
        $this->session->shouldReceive('get')->with('role.requested')->once()->andReturn(null);

        $this->requiredFields->shouldReceive('valid')
            ->with(m::type(User::class), self::SEASON, m::any(Roles::class), false)->once()
            ->andReturn(true);

        $userMock = $this->mock(User::class);
        $userMock->shouldReceive('hasName')->andReturn(false);
        $this->manager->shouldReceive('get->user')->andReturn($userMock);
        $this->consent->shouldReceive('collected')->andReturn(true);

        $this->request->shouldReceive('fullUrl')->once()->andReturn('https://full/url');
        $this->session->shouldReceive('put')->with('intended', 'https://full/url')->once();
        $this->request->shouldReceive('user')->andReturn($userMock);

        $response = $this->middleware->handle($this->request, function ($request) {
            throw new \Exception('This should not be thrown!');
        });

        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertTrue(Str::contains($response->getTargetUrl(), 'profile/complete'));
    }

    public function testNextIfAjaxRequest(): void
    {
        $this->manager->shouldReceive('isGuest')->once()->andReturnFalse();
        $this->request->shouldReceive('ajax')->once()->andReturnTrue();

        $response = $this->middleware->handle($this->request, function ($request) {
            $this->assertEquals($this->request, $request);

            return 'AJAX response';
        });

        // The middleware should ignore the AJAX request and pass it through.
        $this->assertEquals('AJAX response', $response);
    }

    private function roles()
    {
        $role = new Role;
        $role->id = self::ROLE;

        return new Roles([$role]);
    }
}
