<?php

namespace AwardForce\Modules\Identity\Users\Events;

use AwardForce\Modules\Audit\Events\Activity;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Webhooks\Contracts\TriggersWebhooks;
use AwardForce\Modules\Webhooks\Traits\ResolveUserWebhooks;
use Platform\Database\Database;
use Platform\Database\Selector;

class UserWasUpdated implements Activity, TriggersWebhooks
{
    use ResolveUserWebhooks;

    /**
     * @var User
     */
    public $user;

    /**
     * @var bool
     */
    public $updateGlobalUserRecord;

    /**
     * @var Database
     */
    private $connection;

    public function __construct(User $user)
    {
        $this->user = $user;
        $this->connection = app(Selector::class)->current();
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource('user'),
            'updated',
            'audit.user.updated',
            ['user' => $this->user, 'membership' => $this->user->currentMembership, 'connection' => $this->connection],
            $this->user->id,
            (string) $this->user->slug,
        );
    }

    public function user(): User
    {
        return $this->user;
    }
}
