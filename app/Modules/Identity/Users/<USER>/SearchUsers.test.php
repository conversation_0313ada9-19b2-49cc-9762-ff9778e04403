<?php

namespace AwardForce\Modules\Identity\Users\View;

use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Settings\Services\Settings;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class SearchUsersTest extends BaseTestCase
{
    use Laravel;

    public function testItDoesNotShowAddToOrganisationWhenFeatureIsDisabled(): void
    {
        $view = app(SearchUsers::class);

        Feature::shouldReceive('enabled')
            ->with('organisations')
            ->andReturn(false);

        $this->assertFalse($view->showAddToOrganisation());
    }

    public function testItDoesNotShowAddToOrganisationWhenSettingIsDisabled(): void
    {
        $view = app(SearchUsers::class, [
            'settings' => $settings = m::mock(Settings::class),
        ]);

        Feature::shouldReceive('enabled')
            ->with('organisations')
            ->andReturn(true);

        $settings->shouldReceive('organisationSettings')
            ->andReturn(new OrganisationsSettings([
                'managerAdd' => false,
            ]));

        $this->assertFalse($view->showAddToOrganisation());
    }

    public function testItShowsAddToOrganisationWhenFeatureAndSettingAreEnabled(): void
    {
        $view = app(SearchUsers::class, [
            'settings' => $settings = m::mock(Settings::class),
        ]);

        Feature::shouldReceive('enabled')
            ->with('organisations')
            ->andReturn(true);

        $settings->shouldReceive('organisationSettings')
            ->andReturn(new OrganisationsSettings([
                'managerAdd' => true,
            ]));

        $this->assertTrue($view->showAddToOrganisation());
    }
}
