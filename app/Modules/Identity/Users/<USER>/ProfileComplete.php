<?php

namespace AwardForce\Modules\Identity\Users\Middleware;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Agreements\Services\Consent;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use AwardForce\Modules\Forms\Fields\Services\RequiredUserFieldsService;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Closure;
use Illuminate\Http\Request;

class ProfileComplete
{
    /**
     * @var RequiredUserFieldsService
     */
    protected $requiredFields;

    /**
     * @var SeasonRepository
     */
    protected $seasons;

    /**
     * @var Manager
     */
    protected $manager;

    /**
     * @var Consent
     */
    protected $consent;

    public function __construct(
        RequiredUserFieldsService $requiredFields,
        SeasonRepository $seasons,
        Manager $manager,
        Consent $consent
    ) {
        $this->requiredFields = $requiredFields;
        $this->seasons = $seasons;
        $this->manager = $manager;
        $this->consent = $consent;
    }

    /**
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function handle(Request $request, Closure $next)
    {
        $session = $request->session();

        if ($this->manager->isGuest() || JediEmulator::active() || $request->ajax()) {
            return $next($request);
        }

        if ($role = $session->get('role.requested')) {
            $session->put('intended', $request->fullUrl());

            return redirect()->route('profile.complete.role', ['registrationRole' => $role]);
        }

        if ($session->get('profile.completed', false) || ($this->allFieldsCompleted() && $this->consentCollected() && $request->user()->hasName())) {
            $session->put('profile.completed', true);

            return $next($request);
        }

        $session->put('intended', $request->fullUrl());

        return redirect()->route('profile.complete');
    }

    protected function allFieldsCompleted(): bool
    {
        return $this->requiredFields->valid(
            $this->manager->get()->user(),
            $this->seasons->getActiveId(),
            $this->manager->get()->roles(),
            $this->manager->isManager()
        );
    }

    protected function consentCollected(): bool
    {
        return $this->consent->collected($this->manager->user());
    }
}
