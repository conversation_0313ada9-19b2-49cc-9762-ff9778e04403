<?php

namespace AwardForce\Modules\Identity\Users\Models;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Modules\Accounts\Events\MembershipWasRegistered;
use AwardForce\Modules\Accounts\Events\MembershipWasRestored;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\Invitation;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Audit\Data\EventLog;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Comments\Contracts\Commentable;
use AwardForce\Modules\Comments\HasComments;
use AwardForce\Modules\Comments\Models\Comment;
use AwardForce\Modules\Documents\Contracts\HasDocuments as HasDocumentsContract;
use AwardForce\Modules\Documents\Contracts\HasMergeFields;
use AwardForce\Modules\Documents\Models\Document;
use AwardForce\Modules\Documents\Models\HasDocuments;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\Fieldable;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFields;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasNestedValues;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Events\MembershipWasBlocked;
use AwardForce\Modules\Identity\Users\Events\MembershipWasDeleted;
use AwardForce\Modules\Identity\Users\Events\MembershipWasUnblocked;
use AwardForce\Modules\Identity\Users\Events\OwnerWasUpdated;
use AwardForce\Modules\Identity\Users\Events\RoleWasGranted;
use AwardForce\Modules\Identity\Users\Events\RoleWasRevoked;
use AwardForce\Modules\Identity\Users\Events\UserCommunicationWasInvalidated;
use AwardForce\Modules\Identity\Users\Events\UserUsedDiscount;
use AwardForce\Modules\Identity\Users\Events\UserWasAddedToAccount;
use AwardForce\Modules\Identity\Users\Events\UserWasConfirmed;
use AwardForce\Modules\Identity\Users\Events\UserWasUpdated;
use AwardForce\Modules\Identity\Users\Exceptions\AtLeaseEmailOrMobileException;
use AwardForce\Modules\Identity\Users\Services\ProfilePhoto\ImageProfilePhoto;
use AwardForce\Modules\Identity\Users\Services\ProfilePhoto\MissingProfilePhoto;
use AwardForce\Modules\Identity\Users\Services\ProfilePhoto\ProfilePhoto;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\Payments\Models\Discount;
use AwardForce\Modules\PaymentSubscriptions\Models\PaymentSubscription;
use AwardForce\Modules\PaymentSubscriptions\Models\SubscriptionCustomer;
use AwardForce\Modules\Stars\Models\HasStars;
use AwardForce\Modules\Stars\Models\Starrable;
use Carbon\Carbon;
use Eloquence\Behaviours\HasSlugs;
use Eloquence\Behaviours\Slug;
use Illuminate\Auth\Authenticatable;
use Illuminate\Auth\Passwords\CanResetPassword;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Contracts\Auth\CanResetPassword as CanResetPasswordContract;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Platform\Authorisation\FeatureRoles\ChapterManager;
use Platform\Authorisation\FeatureRoles\ProgramManager;
use Platform\Database\Eloquent\ReferencesGlobals;
use Platform\Language\Language;
use Platform\Search\HasValues;

/**
 * AwardForce\Modules\Identity\Users\Models\User
 *
 * @property int $id
 * @property string|null $globalId
 * @property \Eloquence\Behaviours\Slug|null $slug
 * @property string $firstName
 * @property string $lastName
 * @property string|null $email
 * @property string|null $mobile
 * @property int $requireAuthenticator
 * @property string|null $confirmationToken
 * @property array|null $invalidChannels
 * @property string|null $language
 * @property string|null $rememberToken
 * @property string|null $authToken
 * @property string|null $guestToken
 * @property string|null $createdBy
 * @property string|null $color
 * @property bool $membershipCreated
 * @property string|null $confirmedAt
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property string|null $deletedAt
 * @property-read \Platform\Database\Eloquent\Collection|\AwardForce\Modules\Accounts\Models\Account[] $accounts
 * @property-read int|null $accountsCount
 * @property-read File | null $profilePhotoFile
 * @property-read \AwardForce\Modules\Accounts\Models\Membership $currentMembership
 * @property-read \AwardForce\Modules\Accounts\Models\Membership $currentMembershipWithTrashed
 * @property-read \Platform\Database\Eloquent\Collection|\AwardForce\Modules\Payments\Models\Discount[] $discounts
 * @property-read \Platform\Database\Eloquent\Collection|\AwardForce\Modules\Entries\Models\Entry[] $entries
 * @property-read \Platform\Database\Eloquent\Collection|\AwardForce\Modules\Audit\Data\EventLog[] $eventLogs
 * @property-read \Platform\Database\Eloquent\Collection|\AwardForce\Modules\Forms\Fields\Database\DataAccess\FieldValue[] $fieldValues
 * @property \AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields|\AwardForce\Modules\Forms\Fields\Database\Entities\Field[] $fields
 * @property-read \Platform\Database\Eloquent\Collection<int, Account> $accounts
 * @property-read int|null $accountsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Membership> $allMemberships
 * @property-read int|null $allMembershipsCount
 * @property-read \AwardForce\Modules\Assignments\Models\AssignmentCollection<int, Assignment> $assignments
 * @property-read int|null $assignmentsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Document> $associatedDocuments
 * @property-read int|null $associatedDocumentsCount
 * @property-read \AwardForce\Modules\Comments\Models\CommentCollection<int, Comment> $comments
 * @property-read int|null $commentsCount
 * @property-read Membership|null $currentMembership
 * @property-read Membership|null $currentMembershipWithTrashed
 * @property-read \Platform\Database\Eloquent\Collection<int, Discount> $discounts
 * @property-read int|null $discountsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Document> $documents
 * @property-read int|null $documentsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Entry> $entries
 * @property-read int|null $entriesCount
 * @property array $fieldValues
 * @property \AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields $fields
 * @property-read mixed $manualCreated
 * @property-read string $name
 * @property bool $star
 * @property-read mixed $wasInvited
 * @property-read \AwardForce\Modules\Identity\Users\Models\GlobalUser|null $globalUser
 * @property-read \Platform\Database\Eloquent\Collection<int, Chapter> $managedChapters
 * @property-read int|null $managedChaptersCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Membership> $memberships
 * @property-read int|null $membershipsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Order> $orders
 * @property-read int|null $ordersCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Account> $ownedAccounts
 * @property-read int|null $ownedAccountsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Panel> $panels
 * @property-read int|null $panelsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, PaymentSubscription> $paymentSubscriptions
 * @property-read int|null $paymentSubscriptionsCount
 * @property-read Invitation|null $pendingInvitation
 * @property-read \AwardForce\Modules\Identity\Roles\Models\Roles<int, Role> $roles
 * @property-read int|null $rolesCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Document> $sharedDocuments
 * @property-read int|null $sharedDocumentsCount
 * @property-read \Platform\Database\Eloquent\Collection<int, \AwardForce\Modules\Stars\Models\Star> $stars
 * @property-read int|null $starsCount
 * @property-read SubscriptionCustomer|null $subscriptionCustomer
 * @property-read mixed $userId
 *
 * @method static \Platform\Database\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|User inAccount(\AwardForce\Modules\Accounts\Models\Account $account, bool $allowSoftDeleted = false)
 * @method static \Platform\Database\Eloquent\Builder|User newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|User newQuery()
 * @method static \Platform\Database\Eloquent\Builder|User preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|User query()
 * @method static \Platform\Database\Eloquent\Builder|User whereAuthToken($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereConfirmationToken($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereConfirmedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereCreatedBy($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereEmail($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereFirstName($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereGlobalId($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereGuestToken($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereInvalidChannels($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereLanguage($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereLastName($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereMobile($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereRememberToken($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereRequireAuthenticator($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|User whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class User extends Model implements AuthenticatableContract, CanResetPasswordContract, Commentable, HasDocumentsContract, HasFields, HasMergeFields, Starrable, ValuesProvider
{
    use Authenticatable;
    use CanResetPassword;
    use Fieldable {
        defaultProvider as defaultProviderBase;
    }
    use HasComments;
    use HasDocuments;
    use HasNestedValues;
    use HasSlugs;
    use HasStars;
    use ReferencesGlobals;

    const CREATED_MANUAL = 'manual';

    const CREATED_REGISTER = 'register';

    const CREATED_IMPORT = 'import';

    const CREATED_SOCIAL = 'social';

    const CREATED_GUEST = 'guest';

    const CREATED_API = 'api';

    const CREATED_SAML = 'saml';

    const CREATED_INVITED = 'invited';

    const CREATED_LOGIN = 'login';

    const CREATED_INSTALL = 'install';

    protected $table = 'users';
    public $fillable = ['firstName', 'lastName', 'email', 'mobile', 'createdBy', 'membershipCreated'];
    public $casts = [
        'invalid_channels' => 'array',
        'slug' => Slug::class,
    ];
    private array $notAllowedCharacters = ['{', '}', '"', '\\', ';', ':', '@', '[', ']', ''];

    /**
     * Utilises User as a sort of DTO, converting a GlobalUser object into a User object.
     *
     * @return User
     */
    public static function fromGlobalUser(GlobalUser $globalUser)
    {
        $user = new User;
        $user->firstName = $globalUser->firstName;
        $user->lastName = $globalUser->lastName;
        $user->email = $globalUser->email;
        $user->mobile = $globalUser->mobile;

        return $user;
    }

    /**
     * Returns the accounts that the user owns.
     *
     * @return mixed
     */
    public function ownedAccounts()
    {
        return $this->hasMany(Account::class);
    }

    public function files(): HasMany
    {
        return $this->hasMany(File::class);
    }

    public function accounts()
    {
        return $this->belongsToMany(Account::class, 'memberships')->withPivot('deleted_at');
    }

    public function memberships()
    {
        return $this->hasMany(Membership::class);
    }

    public function allMemberships()
    {
        return $this->hasMany(Membership::class)->withTrashed();
    }

    public function eventLogs()
    {
        return EventLog::where('user_id', $this->id);
    }

    /**
     * Return the recent logs for the user. In this case, the last 10 actions.
     *
     * @return mixed
     */
    public function recentLogs()
    {
        return $this->eventLogs()->orderBy('created_at', 'DESC')->take(50)->get() ?: collect();
    }

    /**
     * @return BelongsToMany
     */
    public function panels()
    {
        return $this->belongsToMany(Panel::class, 'panel_judges');
    }

    /**
     * This is identical to the membership's relationship, but it returns only the membership for the current account.
     *
     * @return mixed
     */
    public function currentMembership()
    {
        return $this->hasOne(Membership::class)->whereAccountId(current_account_id());
    }

    public function pendingInvitation()
    {
        return $this->hasOne(Invitation::class)->whereAccountId(current_account_id());
    }

    /**
     * @return mixed
     */
    public function currentMembershipWithTrashed()
    {
        return $this->hasOne(Membership::class)->whereAccountId(current_account_id())->withTrashed();
    }

    /**
     * @return mixed
     */
    public function currentMembershipWithSeasons()
    {
        return $this->currentMembership()->with('seasons')->first();
    }

    /**
     * User has many Entries.
     *
     * @return HasMany
     */
    public function entries()
    {
        return $this->hasMany(Entry::class);
    }

    /**
     * User has many Orders.
     *
     * @return HasMany
     */
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Users can manage many Chapters.
     *
     * @return BelongsToMany
     */
    public function managedChapters()
    {
        return $this->belongsToMany(Chapter::class);
    }

    /**
     * A user may use many discounts across various accounts.
     *
     * @return BelongsToMany
     */
    public function discounts()
    {
        return $this->belongsToMany(Discount::class)->withTimestamps();
    }

    /**
     * A user has many roles, but only for the current account that has been loaded.
     *
     * @return mixed
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class)->whereAccountId(current_account_id());
    }

    public function subscriptionCustomer()
    {
        return $this->hasOne(SubscriptionCustomer::class);
    }

    public function paymentSubscriptions()
    {
        return $this->hasManyThrough(PaymentSubscription::class, SubscriptionCustomer::class);
    }

    /**
     * Add the user to the provided account.
     *
     * @param  null  $languageCode
     */
    public function addToAccount(Account $account, $languageCode = null)
    {
        $this->accounts()->attach($account->id, ['language' => $languageCode, 'created_at' => Carbon::now(), 'deleted_at' => null]);

        $this->raise(new UserWasAddedToAccount($this, $account));
    }

    /**
     * Set the contact information for the user. Either an email address or mobile MUST be present.
     *
     * @param  string|null  $email
     * @param  string|null  $mobile
     *
     * @throws AtLeaseEmailOrMobileException
     */
    public function setContactInformation($email = null, $mobile = null)
    {
        if (empty($email) && empty($mobile)) {
            throw new AtLeaseEmailOrMobileException;
        }

        $this->email = $email;
        $this->mobile = $mobile;
    }

    /**
     * @param  string  $firstName
     * @param  string  $lastName
     * @param  null  $email
     * @param  null  $mobile
     */
    private function updateMe(
        $firstName,
        $lastName,
        $email,
        $mobile,
        $confirmedAt
    ) {
        $this->firstName = $firstName;
        $this->lastName = $lastName;
        $this->email = $email;
        $this->mobile = $mobile;

        if ($confirmedAt) {
            $this->confirmedAt = $confirmedAt;
        }

        if ($this->ownedAccounts->isNotEmpty() && $this->isDirty(['email', 'first_name', 'last_name'])) {
            $this->raise(new OwnerWasUpdated($this, new User([
                'email' => $this->getOriginal('email'),
                'firstName' => $this->getOriginal('first_name'),
                'lastName' => $this->getOriginal('last_name'),
            ])));
        }
    }

    public function edit(
        $firstName,
        $lastName,
        $email = null,
        $mobile = null,
        $confirmedAt = null
    ) {
        $this->updateMe($firstName, $lastName, $email, $mobile, $confirmedAt);

        if (! is_null(current_account()) && $this->isDirty()) {
            $this->raise(new UserWasUpdated($this));
        }
    }

    /**
     * Set the preferred language of the user.
     */
    public function setPreferredLanguage(int $accountId, string $language)
    {
        $this->accounts()->updateExistingPivot($accountId, ['language' => $language]);
    }

    /**
     * Determine whether or not the user is an owner of the provided account.
     *
     * @return bool
     */
    public function ownerOf(Account $account)
    {
        if ($this->ownedAccounts()->count() == 0) {
            return false;
        }

        $ownedAccountIds = $this->ownedAccounts()->pluck('id')->toArray();

        return in_array($account->getId(), $ownedAccountIds);
    }

    /**
     * Determine whether or not the user is owner of current account.
     *
     * @return bool
     */
    public function ownerOfCurrentAccount()
    {
        return current_account()->userId == $this->id;
    }

    /**
     * Check if user is hard-deleted in this program. Meaning user has no current membership.
     * User deletion can't be checked with trashed() as user can belong to multiple programs and
     * deleting the User itself would affect all programs.
     */
    public function isHardDeleted(): bool
    {
        return ! $this->currentMembershipWithTrashed;
    }

    /**
     * Check if user is soft-deleted in this program.
     * User deletion can't be checked with trashed() as user can belong to multiple programs and
     * deleting the User itself would affect all programs.
     */
    public function isSoftDeleted(): bool
    {
        return ! $this->currentMembership || $this->currentMembership->deletedAt;
    }

    public function isAccesibleForAccount($accountId): bool
    {
        return $this->hasOne(Membership::class)->whereAccountId($accountId)->withTrashed() && empty($this->deletedAt) && ! empty($this->globalUser);
    }

    /**
     * Returns a concatenated version of both first and last names.
     *
     * @return string
     */
    public function getName()
    {
        return $this->fullName();
    }

    /**
     * Returns the full name of the user.
     */
    public function fullName(): string
    {
        $invitedText = $this->wasInvited ? '('.mb_strtolower(trans('users.table.invited')).')' : null;

        if ($this->hasName()) {
            return implode(
                ' ',
                array_filter([
                    $this->firstName,
                    $this->lastName,
                    $invitedText,
                ])
            );
        }

        if ($invitedText) {
            return $this->email.' '.$invitedText;
        }

        return trans('users.status.deleted');
    }

    /**
     * Returns the full name alongside the email address
     * in brackets. If the email address is not available
     * we display the mobile number instead if exists.
     */
    public function fullNameWithEmailOrPhone(): string
    {
        $additionalInfo = '';

        if (isset($this->email) && ! empty($this->email)) {
            $additionalInfo = ' ('.$this->email.')';
        } elseif (isset($this->mobile) && ! empty($this->mobile)) {
            $additionalInfo = ' ('.$this->mobile.')';
        }

        return $this->fullName().$additionalInfo;
    }

    public function hasName(): bool
    {
        return ! empty($this->firstName.$this->lastName);
    }

    /**
     * Returns a concatenated version of both first and last names.
     *
     * @return string
     */
    public function getNameAttribute()
    {
        return $this->getName();
    }

    public function getInitialsAttribute(): ?string
    {
        if ($this->firstName && $this->lastName) {
            return strtoupper(mb_substr($this->firstName, 0, 1).mb_substr($this->lastName, 0, 1));
        }

        if ($this->wasInvited && $this->email) {
            return strtoupper(substr($this->email, 0, 2));
        }

        return null;
    }

    /**
     * Get the unique identifier for the user.
     *
     * @return mixed
     */
    public function getAuthIdentifier()
    {
        return $this->getKey();
    }

    /**
     * Get the password for the user.
     *
     * @return string
     */
    public function getAuthPassword()
    {
        return $this->fresh()->globalUser?->password;
    }

    /**
     * Get the token value for the "remember me" session.
     *
     * @return string
     */
    public function getRememberToken()
    {
        return $this->remember_token;
    }

    /**
     * Set the token value for the "remember me" session.
     *
     * @param  string  $value
     * @return void
     */
    public function setRememberToken($value)
    {
        $this->remember_token = $value;
    }

    /**
     * Get the column name for the "remember me" token.
     *
     * @return string
     */
    public function getRememberTokenName()
    {
        return 'remember_token';
    }

    /**
     * Get the e-mail address where password reminders are sent.
     *
     * @return string
     */
    public function getReminderEmail()
    {
        return $this->email;
    }

    /**
     * Convert email addresses to lowercase.
     */
    public function setEmailAttribute(?string $email): void
    {
        $email = strtolower($email ?? '');
        if (! $email) {
            $email = null;
        }
        $existing = array_get($this->attributes, 'email');

        if ($email != $existing) {
            $this->removeInvalidChannel('email');
        }

        $this->attributes['email'] = $email;
    }

    /**
     * First name attribute mutator
     */
    public function setFirstNameAttribute($value)
    {
        $this->attributes['first_name'] = trim($value);
    }

    /**
     * Last name attribute mutator
     */
    public function setLastNameAttribute($value)
    {
        $this->attributes['last_name'] = trim($value);
    }

    /**
     * Whenever the mobile attribute is set, make sure we strip any whitespace.
     *
     * @param  string  $value
     */
    public function setMobileAttribute($value)
    {
        $mobile = preg_replace('#\s+#', '', $value ?? '');
        if (! $mobile) {
            $mobile = null;
        }
        $existing = array_get($this->attributes, 'mobile');

        if ($mobile != $existing) {
            $this->removeInvalidChannel('mobile');
        }

        $this->attributes['mobile'] = $mobile;
    }

    /**
     * Confirm a user by setting the confirmed_at date.
     */
    public function confirm(bool $sync = true)
    {
        $this->confirmedAt = $this->freshTimestamp();

        $this->raiseUnique(new UserWasConfirmed($this, $sync));
    }

    /**
     * Returns true if the user has confirmed the account.
     *
     * @return bool
     */
    public function hasConfirmed()
    {
        return ! is_null($this->confirmedAt);
    }

    /**
     * Get the e-mail address where password reset links are sent.
     *
     * @return string
     */
    public function getEmailForPasswordReset()
    {
        // TODO: Implement getEmailForPasswordReset() method.
    }

    /**
     * Returns the notification destination for a user. If the email field is available and valid,
     * then the email will be returned. If, however - the email is not available but the mobile
     * number is, the mobile will be used.
     *
     * @return string
     */
    public function notificationDestination()
    {
        if (! empty($this->email)) {
            return $this->email;
        }

        if (! empty($this->mobile)) {
            return $this->mobile;
        }
    }

    /**
     * Query scope to limit User queries to the specified Account.
     *
     * @param  \Illuminate\Database\Query\Builder  $query
     * @return \Illuminate\Database\Query\Builder
     */
    public function scopeInAccount($query, Account $account, bool $allowSoftDeleted = false)
    {
        return $query->join('memberships', function ($join) use ($account, $allowSoftDeleted) {
            $join->on('memberships.user_id', '=', 'users.id');
            $join->where('memberships.account_id', '=', $account->id);
            if (! $allowSoftDeleted) {
                $join->whereNull('memberships.deleted_at');
            }
        });
    }

    /**
     * Returns true if the User object represents a guest.
     *
     * @return bool
     */
    public function guest()
    {
        return (bool) $this->guestToken;
    }

    /**
     * Returns the users preferred language for the current account.
     * First by checking with the user and if none specified,
     * then defaulting to the account's preferred (default) language.
     */
    public function preferredLanguage(): Language
    {
        $language = $this->currentMembership->language ?? null;

        return $language ? new Language($language) : current_account()->defaultLanguage();
    }

    /**
     * Returns true if the user uses the default language of the current account.
     */
    public function usesDefaultLanguage(): bool
    {
        return $this->preferredLanguage()->code === current_account()->defaultLanguage()->code;
    }

    /**
     * Returns true if the user's preferred language is active in the current account supported languages.
     */
    public function isActivePreferredLanguage(): bool
    {
        return in_array($this->preferredLanguage()->code, current_account()->supportedLanguageCodes());
    }

    /**
     * Returns the users preferred language for the specified account.
     * First by checking with the user and if none specified,
     * then defaulting to the account's preferred (default) language.
     */
    public function preferredLanguageFor(Account $account): Language
    {
        $membership = $this->memberships()->where('account_id', $account->id)->first();

        return $membership && $membership->language ? new Language($membership->language) : $account->defaultLanguage();
    }

    /**
     * Returns the users preferred language, first looking into the current account.
     * If there is no language preference in the current account, search for user memberships
     * in other accounts with preferred language in current account supported languages,
     * otherwise return the fallback language if set, or the current account preferred language.
     *
     * @param  string|null  $fallback
     */
    public function preferredLanguageForUser($fallback = null): Language
    {
        if ($this->currentMembership && $this->currentMembership->language) {
            return new Language($this->currentMembership->language);
        }

        $membership = $this->memberships()->whereIn('language', current_account()->supportedLanguageCodes())->first();

        if ($membership && $membership->language) {
            return new Language($membership->language);
        }

        return ! is_null($fallback) ? new Language($fallback) : current_account()->defaultLanguage();
    }

    /**
     * @param  string  $key
     * @param  mixed  $default
     * @return mixed
     */
    public function getSetting($key, $default = null)
    {
        return $this->currentMembership ? $this->currentMembership->getSetting($key, $default) : $default;
    }

    /**
     * @param  string  $key
     * @param  mixed  $value
     */
    public function setSetting($key, $value)
    {
        if ($this->currentMembership) {
            $this->currentMembership->setSetting($key, $value);
        }
    }

    /**
     * Preferred contact method for the user. If email is empty, then mobile will be returned.
     *
     * @return mixed
     */
    public function preferredContact()
    {
        return $this->email ?: $this->mobile;
    }

    public function started(Entry $entry): bool
    {
        return $entry->userId === $this->id;
    }

    /**
     * Registers a membership for the user in the given account.
     */
    public function registerMembership(Account $account, $language)
    {
        $deleted = $this->memberships()->where('account_id', $account->id)->onlyTrashed()->first();

        if ($deleted) {
            $this->restoreMembership($account);

            return;
        }

        $active = $membership = $this->memberships()->where('account_id', $account->id)->first();

        if (! $active) {
            $membership = Membership::register($account, $this, $language);
            $this->raise(new MembershipWasRegistered($membership));
        }
    }

    /**
     * Soft-deletes the user's membership to the given account.
     */
    public function deleteMembership(Account $account)
    {
        $membership = $this->memberships()->where('account_id', $account->id)->first();

        if ($membership) {
            $membership->delete();

            $this->touch();
            $this->raise(new MembershipWasDeleted($membership));
        }
    }

    public function collaborators(): HasMany
    {
        return $this->hasMany(Collaborator::class);
    }

    /**
     * Restores the user's membership to the current account.
     */
    public function restoreMembership(Account $account)
    {
        $membership = $this->memberships()->where('account_id', $account->id)->onlyTrashed()->first();

        if ($membership) {
            $membership->restore();

            $this->touch();
            $this->raise(new MembershipWasRestored($membership));
        }
    }

    /**
     * Synchronise the user's roles without re-adding those that are already assigned.
     */
    public function syncRoles(array $roles, bool $revokeExistingRoles = true)
    {
        $current = $this->roles->just('id');

        $this->grantRoles(array_diff($roles, $current));
        if ($revokeExistingRoles) {
            $this->revokeRoles(array_diff($current, $roles));
        }
    }

    /**
     * Assigns the user to the given roles.
     *
     * @param  int|array  $roles A single role id, or an array of role ids.
     */
    public function grantRoles($roles)
    {
        $roles = (array) $roles;

        foreach ($roles as $roleId) {
            if (! $this->roles()->whereRoleId($roleId)->count()) {
                $this->roles()->attach($roleId);
                $role = $this->roles()->whereRoleId($roleId)->first();

                $this->raise(new RoleWasGranted($this, $role));
            }
        }
    }

    /**
     * Revokes the given roles from the user.
     */
    public function revokeRoles($roles)
    {
        $roles = (array) $roles;

        foreach ($roles as $roleId) {
            if ($role = $this->roles()->whereRoleId($roleId)->first()) {
                $this->roles()->detach($roleId);

                $this->raise(new RoleWasRevoked($this, $role));
            }
        }
    }

    /**
     * Removes all roles from the user.
     */
    public function clearRoles()
    {
        $this->syncRoles([]);
    }

    /**
     * Returns the total number of permissions that the user has.
     */
    public function permissionLevel(): int
    {
        return $this->getConnection()->table('permissions')
            ->join('role_user', 'role_user.role_id', '=', 'permissions.role_id')
            ->join('roles', function ($join) {
                $join->on('roles.id', '=', 'role_user.role_id');
                $join->whereNull('roles.deleted_at');
            })
            ->where('permissions.mode', '=', 'allow')
            ->where('role_user.user_id', '=', $this->id)
            ->count();
    }

    /**
     * Returns the user's highest permission level in their roles.
     */
    public function highestRolePermissionLevel(): int
    {
        return $this->roles->reduce(function ($total, Role $role) {
            $level = $role->permissionLevel();

            return $level > $total ? $level : $total;
        }, 0);
    }

    public function chapterLimited(): bool
    {
        return $this->roles->contains->chapterLimited;
    }

    /**
     * When a user uses a discount, apply it here.
     */
    public function useDiscount(Discount $discount, int $seasonId)
    {
        $this->discounts()->attach($discount->id, ['season_id' => $seasonId]);

        $this->raise(new UserUsedDiscount($this, $discount));
    }

    /**
     * Count the number of times this user has used the given discount.
     *
     * @return bool
     */
    public function countDiscountUses(Discount $discount)
    {
        return $this->discounts()
            ->wherePivot('discount_id', $discount->id)
            ->count();
    }

    /**
     * Blocks the user's membership in the account.
     */
    public function blockMembership(Account $account)
    {
        $membership = $this->memberships()->withTrashed()->where('account_id', $account->id)->first();

        if ($membership) {
            $membership->block();

            $this->touch();
            $this->raise(new MembershipWasBlocked($membership));
        }
    }

    /**
     * Unblocks the user's membership in the account.
     */
    public function unblockMembership(Account $account)
    {
        $membership = $this->memberships()->withTrashed()->where('account_id', $account->id)->first();

        if ($membership) {
            $membership->unblock();

            $this->touch();
            $this->raise(new MembershipWasUnblocked($membership));
        }
    }

    public function isBlocked(): bool
    {
        $membership = $this->memberships()->withTrashed()->where('account_id', current_account_id())->first();

        if ($membership && $membership->blocked) {
            return true;
        }

        return false;
    }

    /**
     * Flags the user as having an invalid communication channel(s).
     */
    public function invalidateCommunication(string $channel)
    {
        $this->addInvalidChannel($channel);
        $this->save();

        $this->raise(new UserCommunicationWasInvalidated($this, $channel));
    }

    public function validateCommunication()
    {
        $this->removeInvalidChannel('email');
        $this->removeInvalidChannel('mobile');
        $this->save();
    }

    /**
     * Returns true if the user's record has valid communication channels.
     */
    public function hasValidChannels(): bool
    {
        return ! count((array) $this->invalidChannels);
    }

    /**
     * Add invalid channels to the user record.
     */
    public function addInvalidChannel(string $channel)
    {
        $this->invalidChannels = array_merge((array) $this->invalidChannels, [$channel]);
    }

    /**
     * Remove the required channel from the user's invalid channels.
     */
    public function removeInvalidChannel(string $channel)
    {
        $channels = (array) $this->invalidChannels;

        array_splice($channels, array_search($channel, $channels));

        $this->invalidChannels = $channels;
    }

    public function invalidChannelMessage()
    {
        if (count($this->invalidChannels) == 2) {
            return 'both';
        } elseif (in_array('email', $this->invalidChannels)) {
            return 'email';
        } elseif (in_array('mobile', $this->invalidChannels)) {
            return 'mobile';
        }
    }

    /**
     * @param  bool  $broadcastEmails
     * @param  bool  $notificationEmails
     * @param  bool  $notificationSms
     */
    public function updateNotificationPreferences($broadcastEmails, $notificationEmails, $notificationSms)
    {
        $this->currentMembershipWithTrashed->broadcastEmails = $broadcastEmails;
        $this->currentMembershipWithTrashed->notificationEmails = $notificationEmails;
        $this->currentMembershipWithTrashed->notificationSms = $notificationSms;

        $this->currentMembershipWithTrashed->save();
    }

    public function subscribedToEmailBroadcasts(): bool
    {
        return $this->currentMembership->broadcastEmails ?? true;
    }

    public function subscribedToEmailNotifications(): bool
    {
        return $this->currentMembership->notificationEmails ?? true;
    }

    public function subscribedToSmsNotifications(): bool
    {
        return $this->currentMembership->notificationSms ?? true;
    }

    /**
     * @return void
     */
    public function setAgreementToTermsRequestedTimestamp()
    {
        $this->currentMembership->agreementToTerms = Carbon::now();
        $this->currentMembership->save();
    }

    /**
     * @return void
     */
    public function setConsentToNotificationsRequestedTimestamp()
    {
        $this->currentMembership->consentToNotifications = Carbon::now();
        $this->currentMembership->save();
    }

    public function agreementToTermsCollected(): bool
    {
        return ! is_null($this->currentMembership?->agreementToTerms);
    }

    public function consentToNotificationsCollected(): bool
    {
        return ! is_null($this->currentMembership->consentToNotifications);
    }

    public function cookieConsentCollected(): bool
    {
        return ! is_null($this->currentMembership->cookies ?? null);
    }

    /**
     * @param  string  $cookieType
     * @return bool
     */
    public function acceptsCookieType($cookieType)
    {
        return in_array($cookieType, $this->currentMembership ? (array) $this->currentMembership->cookies : []);
    }

    /**
     * Returns true if user is a member of more than one program.
     *
     * @return bool
     */
    public function hasMultipleMemberships()
    {
        $memberships = $this->globalUser->memberships ?? [];

        return $memberships && count($memberships) > 1;
    }

    protected function getValuesProviders(): Collection
    {
        return collect([$this->defaultProvider()])->filter();
    }

    public function defaultProvider(): ?HasValues
    {
        if (! isset($this->currentMembership)) {
            $this->load('currentMembership');
        }

        return $this->currentMembership;
    }

    public function roleIds()
    {
        return $this->roles->sortBy('id')->pluck('id')->toArray();
    }

    /**
     * @return SubscriptionCustomer
     */
    public function addSubscriptionCustomer($gateway, $customerId, $currency)
    {
        return $this->subscriptionCustomer()->updateOrCreate([], [
            'gateway' => $gateway,
            'customer_id' => $customerId,
            'currency' => $currency,
        ]);
    }

    public function ownsAccounts()
    {
        return ! $this->ownedAccounts->isEmpty();
    }

    public function editableByConsumer()
    {
        return ! ($this->ownerOf(current_account()) &&
            $this->ownedAccounts->count() === 1 &&
            $this->id !== \consumer_id());
    }

    public function existsGlobally()
    {
        return $this->globalId;
    }

    /**
     * Return the columns that have a uuid.
     */
    protected function uuids(): array
    {
        return ['global_id'];
    }

    public function globalUser()
    {
        return $this->belongsTo(GlobalUser::class, 'global_id');
    }

    public function globalInstance()
    {
        return $this->globalUser();
    }

    public function hasMembershipForAccount($accountId, $withTrashed = false)
    {
        $query = $this->memberships()->where('account_id', $accountId);

        if ($withTrashed) {
            $query->withTrashed();
        }

        return $query->exists();
    }

    public function getWasInvitedAttribute()
    {
        return $this->createdBy == User::CREATED_INVITED && ! $this->hasConfirmed();
    }

    public function getManualCreatedAttribute()
    {
        return $this->createdBy == User::CREATED_MANUAL;
    }

    public function getColorAttribute(): string
    {
        return $this->currentMembership?->getColour() ?? random_color();
    }

    public function profilePhotoFile(): HasOne
    {
        return $this->hasOne(File::class)->whereResource(File::RESOURCE_PROFILE_PHOTO)->whereAccountId(current_account_id())->orderByDesc('id');
    }

    public function profilePhoto(): ProfilePhoto
    {
        if ($this->profilePhotoFile !== null) {
            return new ImageProfilePhoto($this->profilePhotoFile->file, $this);
        }

        return new MissingProfilePhoto($this);
    }

    /**
     * User has many Comments.
     *
     * @return HasMany
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    public function assignments(): HasMany
    {
        return $this->hasMany(Assignment::class, 'judge_id');
    }

    public function createdByThirdParty()
    {
        return in_array($this->createdBy, [User::CREATED_SAML, User::CREATED_SOCIAL]);
    }

    public function ownsCommunicationChannel(string $channel): bool
    {
        return in_array($channel, [$this->email, $this->mobile]);
    }

    public function associatedDocuments(): HasMany
    {
        return $this->hasMany(Document::class);
    }

    public function sharedDocuments(): HasMany
    {
        return $this->associatedDocuments()->where('shared', true);
    }

    public function getMergeFields(): array
    {
        return [
            'entrant_name' => htmlspecialchars($this->fullName()),
            'entrant_email' => $this->email,
            'first_name' => htmlspecialchars($this->first_name),
            'last_name' => htmlspecialchars($this->last_name),
            'user_slug' => (string) $this->slug,
        ];
    }

    protected function userId(): Attribute
    {
        return Attribute::make(
            get: fn() => $this->id,
        );
    }

    public function getFirstNameAttribute($firstName)
    {
        return $this->removeInvalidCharacters($firstName);
    }

    public function getLastNameAttribute($lastName)
    {
        return $this->removeInvalidCharacters($lastName);
    }

    private function removeInvalidCharacters($value)
    {
        return trim(Str::replace($this->notAllowedCharacters, '', $value ?? ''));
    }

    /**
     * Returns true if the user is a manager.
     */
    public function isManager(): bool
    {
        return $this->roles->managerRoles()->isNotEmpty();
    }

    /**
     * Returns true if the user is a program manager.
     */
    public function isProjectManager(): bool
    {
        return ProgramManager::appliesTo(new UserConsumer($this));
    }

    /**
     * Returns true if the user is a chapter manager.
     */
    public function isChapterManager(): bool
    {
        return ChapterManager::appliesTo(new UserConsumer($this));
    }
}
