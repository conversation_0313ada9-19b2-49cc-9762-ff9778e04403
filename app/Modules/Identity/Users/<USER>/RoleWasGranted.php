<?php

namespace AwardForce\Modules\Identity\Users\Events;

use AwardForce\Modules\Audit\Events\Activity;
use AwardForce\Modules\Audit\Events\Log;
use AwardForce\Modules\Audit\Events\SystemResource;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Webhooks\Contracts\TriggersWebhooks;
use AwardForce\Modules\Webhooks\Traits\ResolveUserWebhooks;

class RoleWasGranted implements Activity, TriggersWebhooks
{
    use ResolveUserWebhooks;

    /**
     * @var User
     */
    public $user;

    /**
     * @var Role
     */
    public $role;

    public function __construct(User $user, Role $role)
    {
        $this->user = $user;
        $this->role = $role;
    }

    /**
     * Returns a log of the activity that was undertaken.
     */
    public function log(): Log
    {
        return Log::withDefaults(
            new SystemResource('user'),
            'role-granted',
            'audit.user.role-granted',
            [$this->user, $this->role],
            $this->user->id,
            (string) $this->user->slug,
        );
    }

    public function user(): User
    {
        return $this->user;
    }
}
