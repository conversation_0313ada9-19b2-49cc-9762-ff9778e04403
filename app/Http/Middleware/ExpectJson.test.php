<?php

namespace AwardForce\Http\Middleware;

use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotAcceptableHttpException;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class ExpectJsonTest extends BaseTestCase
{
    use Laravel;

    public function testPassesThroughWhenRequestExpectsJson(): void
    {
        $request = Request::create('/some-route', server: ['HTTP_ACCEPT' => 'application/json']);
        $middleware = new ExpectJson();

        $response = $middleware->handle($request, fn() => response('Next middleware called'));

        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals('Next middleware called', $response->getContent());
    }

    public function testItThrowsAnErrorWhenRequestIsNotExpectingJson(): void
    {
        $request = Request::create('/some-route', server: ['HTTP_ACCEPT' => 'text/html']);
        $middleware = new ExpectJson();

        $this->expectException(NotAcceptableHttpException::class);

        $middleware->handle($request, fn() => response('Next middleware called'));
    }
}
