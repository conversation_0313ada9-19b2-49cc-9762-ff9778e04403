<?php

namespace AwardForce\Http\Requests\Setting;

use AwardForce\Library\Http\FormRequest;

class UpdateOrganisationsSettingsRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'enabled' => 'required|boolean',
            'joinOnRegisteredEmail' => 'required|boolean',
            //            'userSelect' => 'required|boolean',
            //            'userAdd' => 'required|boolean',
            //            'administratorInvite' => 'required|boolean',
            'managerAdd' => 'required|boolean',
        ];
    }
}
