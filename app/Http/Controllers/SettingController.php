<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Setting\UpdateAccountSettingsRequest;
use AwardForce\Http\Requests\Setting\UpdateLanguagesSettingsRequest;
use AwardForce\Http\Requests\Setting\UpdateOrganisationsSettingsRequest;
use AwardForce\Http\Requests\Setting\UpdateRegistrationSettingsRequest;
use AwardForce\Http\Requests\Setting\UpdateSocialSettingsRequest;
use AwardForce\Modules\Settings\Commands\UpdateAccountSettingsCommand;
use AwardForce\Modules\Settings\Commands\UpdateLanguagesSettingsCommand;
use AwardForce\Modules\Settings\Commands\UpdateOrganisationsSettings;
use AwardForce\Modules\Settings\Commands\UpdateRegistrationSettingsCommand;
use AwardForce\Modules\Settings\Commands\UpdateSocialSettingsCommand;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use AwardForce\Modules\Settings\View\Organisations;
use AwardForce\Modules\Settings\View\Settings;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Platform\Http\Controller;

class SettingController extends Controller
{
    use DispatchesJobs;

    /**
     * Display editable Account settings.
     *
     * @return mixed
     */
    public function account(Settings $view)
    {
        return $this->respond('setting.account', $view);
    }

    /**
     * Update modified Account settings.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateAccount(UpdateAccountSettingsRequest $request)
    {
        $this->dispatch(new UpdateAccountSettingsCommand(
            current_account(),
            $request->get('setting', []),
            $request->get('translated', [])
        ));

        return back();
    }

    /**
     * Display editable Registration settings.
     *
     * @return mixed
     */
    public function registration(Settings $view)
    {
        return $this->respond('setting.registration', $view);
    }

    /**
     * Update modified Registration settings.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateRegistration(UpdateRegistrationSettingsRequest $request)
    {
        $this->dispatch(new UpdateRegistrationSettingsCommand(
            current_account(),
            $request->get('setting', []),
            $request->get('translated', [])
        ));

        return back();
    }

    /**
     * Display editable Entries settings.
     *
     * @return mixed
     */
    public function entries(Settings $view)
    {
        return $this->respond('setting.entries', $view);
    }

    /**
     * Update modified Entries settings.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateEntries(\Request $request)
    {
        return back();
    }

    /**
     * Display editable Languages settings.
     *
     * @return mixed
     */
    public function languages(Settings $view)
    {
        return $this->respond('setting.languages', $view);
    }

    /**
     * Update modified Languages settings.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateLanguages(UpdateLanguagesSettingsRequest $request)
    {
        $this->dispatch(new UpdateLanguagesSettingsCommand(
            current_account(),
            $request->get('setting', []),
            $request->get('languages', []),
            $request->get('default_language')
        ));

        return back();
    }

    /**
     * Display editable Social settings.
     *
     * @return mixed
     */
    public function social(Settings $view)
    {
        return $this->respond('setting.social', $view);
    }

    /**
     * Update modified Social settings.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateSocial(UpdateSocialSettingsRequest $request)
    {
        $this->dispatch(new UpdateSocialSettingsCommand(
            current_account(),
            $request->get('setting', [])
        ));

        return back();
    }

    public function organisations(Organisations $view)
    {
        return $this->respond('setting.organisations', $view);
    }

    public function updateOrganisations(UpdateOrganisationsSettingsRequest $request)
    {
        $this->dispatchSync(new UpdateOrganisationsSettings(new OrganisationsSettings($request->all())));

        return response()->json([
            'success' => true,
        ]);
    }
}
