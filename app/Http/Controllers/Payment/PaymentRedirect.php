<?php

namespace AwardForce\Http\Controllers\Payment;

use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;

trait PaymentRedirect
{
    protected function redirectToNextTab(Cart $cart)
    {
        $tabs = app(TabRepository::class)
            ->getFromForm(FormSelector::getForEntry($entry = $cart->firstEntry()))
            ->sortBy('order')->values();
        $tab = $tabs[1] ?? $tabs[0];

        $message = $cart->isInvoice() ? trans('entries.messages.awaiting-payment') : trans('payments.messages.payment_successful');

        return redirect()
            ->route('entry-form.entrant.edit', ['entry' => $entry->slug, 'tabSlug' => (string) $tab->slug ?? null])
            ->with(['message' => $message, 'type' => 'success']);
    }
}
