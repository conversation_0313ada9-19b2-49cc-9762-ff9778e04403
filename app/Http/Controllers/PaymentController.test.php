<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Middleware\SessionPostParameters;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\CartRepository;
use AwardForce\Modules\Ecommerce\Cart\Services\LocksPayment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Payments\Commands\CompletePaymentCommandHandler;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Mockery as m;
use Platform\Bus\Dispatcher;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Concerns\Passport;

final class PaymentControllerTest extends BaseTestCase
{
    use Database;
    use Laravel;
    use Passport;

    private Cart $cart;
    private Entry $entry;
    private Tab $tab;
    private Request $request;
    private LocksPayment $locksPayment;
    private SessionPostParameters $sessionPostParameters;

    public function init()
    {
        $params = ['param1' => 'val1', 'param2' => 'val2'];
        app()->instance(Dispatcher::class, $dispatcher = m::mock(Dispatcher::class));
        $handler = m::mock(CompletePaymentCommandHandler::class);
        $handler->shouldReceive('handle')->andReturn($response = m::mock(Response::class));
        $response->shouldReceive('success')->andReturnTrue();
        app()->instance(CompletePaymentCommandHandler::class, $handler);
        $this->request = m::mock(Request::class);
        $this->request->shouldReceive('all')->andReturn($params);
        $this->request->gateway = 'Gateway name';
        $this->sessionPostParameters = m::mock(SessionPostParameters::class);
        $this->sessionPostParameters
            ->shouldReceive('retrieve')
            ->with($this->request)
            ->andReturn($params);

        $this->entry = m::mock(Entry::class);
        $form = $this->muffin(Form::class);
        $this->muffin(Tab::class, [
            'type' => Tab::TYPE_FIELDS,
            'resource' => Tab::RESOURCE_ENTRIES,
            'form_id' => $form->id,
            'order' => 3,
        ]);
        $this->muffin(Tab::class, [
            'type' => Tab::TYPE_FIELDS,
            'resource' => Tab::RESOURCE_ENTRIES,
            'form_id' => $form->id,
            'order' => 1,
        ]);
        $this->tab = $this->muffin(Tab::class, [
            'type' => Tab::TYPE_FIELDS,
            'resource' => Tab::RESOURCE_ENTRIES,
            'form_id' => $form->id,
            'order' => 2,
        ]);
        $this->entry = $this->muffin(Entry::class, [
            'form_id' => $form->id,
        ]);

        $this->cart = m::mock(Cart::class);
        $this->cart
            ->shouldReceive('firstEntry')->andReturn($this->entry)
            ->shouldReceive('isInvoice')->andReturnTrue();
        $this->locksPayment = LocksPayment::create(current_account_id(), consumer_id());
    }

    public function testLog(): void
    {
        $this->locksPayment->acquireLock();
        $this->cart->shouldReceive('isPaymentModePrepayment')->andReturnFalse();
        Log::spy();
        (new PaymentController($this->sessionPostParameters))->gatewayReturn($this->request, $this->cart);
        Log::shouldHaveReceived('info')->withArgs(function ($message) {
            if (Str::startsWith($message, '[')) {
                $this->assertStringContainsString(current_account_slug(), $message);
                $this->assertStringContainsString('"param1":"val1"', $message);

                return true;
            }
        });
        $this->assertFalse($this->locksPayment->processLocked());
    }

    public function testRedirectToNextTabAfterPayment(): void
    {
        $this->locksPayment->acquireLock();
        $this->cart->shouldReceive('isPaymentModePrepayment')->andReturnTrue();
        $mustRedirectTo = route('entry-form.entrant.edit', ['entry' => $this->entry->slug])."?tabSlug={$this->tab->slug}";
        $res = (new PaymentController($this->sessionPostParameters))->gatewayReturn($this->request, $this->cart);
        $this->assertEquals($mustRedirectTo, $res->getTargetUrl());

        $this->cart->shouldReceive('isPaymentModePrepayment')->andReturnFalse();
        $this->assertEquals($mustRedirectTo, $res->getTargetUrl());
        $this->assertFalse($this->locksPayment->processLocked());
    }

    public function testRedirectToEntryComplete(): void
    {
        $this->locksPayment->acquireLock();
        $this->cart->shouldReceive('isPaymentModePrepayment')->andReturnFalse();
        $mustRedirectTo = route('entry.entrant.complete');
        $res = (new PaymentController($this->sessionPostParameters))->gatewayReturn($this->request, $this->cart);
        $this->assertEquals($mustRedirectTo, $res->getTargetUrl());
        $this->assertFalse($this->locksPayment->processLocked());
    }

    public function testRedirectToCartCheckoutWhenPaymentIsCancelled()
    {
        $cart = app(CartRepository::class)->forUser($this->muffin(User::class), current_account());

        $res = (new PaymentController($this->sessionPostParameters))->gatewayCancel($this->request, $cart);

        $this->assertInstanceOf(RedirectResponse::class, $res);
        $this->assertEquals(route('cart.checkout'), $res->getTargetUrl());
        $this->assertFalse($this->locksPayment->processLocked());
    }
}
