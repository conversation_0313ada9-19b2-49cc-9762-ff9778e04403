<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Controllers\Payment\PaymentRedirect;
use AwardForce\Http\Requests\Cart\CartCompleteRequest;
use AwardForce\Http\Requests\Cart\PayTagFee;
use AwardForce\Http\Requests\Payment\ProcessPaymentFormRequest;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Commands\PayTagFeeCommand;
use AwardForce\Modules\Ecommerce\Cart\Commands\ProcessCartCommand;
use AwardForce\Modules\Ecommerce\Cart\Commands\RemoveItemFromCartCommand;
use AwardForce\Modules\Ecommerce\Cart\Commands\UpdateCartCommand;
use AwardForce\Modules\Ecommerce\Cart\Exceptions\RecentPaymentException;
use AwardForce\Modules\Ecommerce\Cart\Services\LocksPayment;
use AwardForce\Modules\Ecommerce\Cart\Services\RefreshCart;
use AwardForce\Modules\Payments\Commands\Process3DS2Challenge;
use AwardForce\Modules\Payments\Commands\ProcessPaymentFormCommand;
use AwardForce\Modules\Payments\Composers\PaymentGateway;
use AwardForce\Modules\Payments\Contracts\ValidatesJWT;
use AwardForce\Modules\Payments\DataObjects\CreditCard;
use AwardForce\Modules\Payments\Enums\CardNetwork;
use AwardForce\Modules\Payments\Exceptions\InvalidCreditCardDataException;
use AwardForce\Modules\Payments\Exceptions\InvalidGatewayConfigException;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Exception;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Controller;
use Illuminate\Support\Arr;
use Omnipay\Common\Exception\InvalidCreditCardException;
use Omnipay\Common\Exception\InvalidRequestException;
use Omnipay\Common\Exception\InvalidResponseException;
use Platform\Http\Respondable;
use Symfony\Component\HttpFoundation\Request;

class CartController extends Controller
{
    use DispatchesJobs;
    use PaymentGateway;
    use PaymentRedirect;
    use Respondable;

    public function __construct(private SettingRepository $settings)
    {
    }

    /**
     * View cart
     *
     * @return mixed
     */
    public function view(Cart $cart, RefreshCart $refreshCart)
    {
        $refreshCart->refresh($cart);

        return $this->respond('ecommerce.cart', ['cart' => $cart]);
    }

    /**
     * @return \Illuminate\Http\RedirectResponse
     */
    public function payTagFee(PayTagFee $request)
    {
        $this->dispatch(new PayTagFeeCommand($request->route('entry'), $request->route('price')));

        return redirect()->route('cart.view');
    }

    /**
     * Update the cart with new settings
     *
     * @return \Illuminate\Http\RedirectResponse|\Illuminate\Routing\Redirector
     */
    public function update(Cart $cart, Request $request)
    {
        $this->dispatch(new UpdateCartCommand(
            $request->get('currency'),
            $request->get('vatNumber'),
            $request->get('companyName'),
            $request->get('streetAddress'),
            $request->get('city'),
            $request->get('state'),
            $request->get('postcode'),
            $request->get('country'),
            $request->get('region'),
            $request->get('entryFee'),
            $request->get('entrantFee'),
            $request->get('paymentMethod'),
            $request->get('discountCode'),
            $request->get('memberNumber'),
            $request->getClientIp()
        ));

        return $this->respond('ecommerce.cart', ['cart' => $cart]);
    }

    /**
     * Remove an item from the cart
     *
     * @return mixed
     */
    public function remove(Cart $cart, $key)
    {
        $this->dispatch(new RemoveItemFromCartCommand($cart, $key));

        return redirect()->route('cart.view');
    }

    /**
     * @return \Illuminate\Http\RedirectResponse
     */
    public function pay(Cart $cart, CartCompleteRequest $request)
    {
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id());

        $this->dispatch(new UpdateCartCommand(
            $request->get('currency'),
            $request->get('vatNumber'),
            $request->get('companyName'),
            $request->get('streetAddress'),
            $request->get('city'),
            $request->get('state'),
            $request->get('postcode'),
            $request->get('country'),
            $request->get('region'),
            $request->get('entryFee'),
            $request->get('entrantFee'),
            $request->get('paymentMethod'),
            $request->get('discountCode'),
            $request->get('memberNumber'),
            $request->getClientIp(),
            true
        ));

        if ($cart->requiresOnsitePayment()) {
            return redirect()->route('cart.checkout');
        }

        if (! $cart->free() && ! $locksPayment->acquireLock()) {
            return redirect()->route('cart.view')->withErrors(trans('ecommerce.cart.validation.payment_in_progress'));
        }

        try {
            $response = $this->dispatchSync(new ProcessCartCommand($cart));

            if ($response && $response->isRedirect()) {
                return redirect($response->redirectUrl());
            }

            $locksPayment->releaseLock();

            if ($response && $response->failed()) {
                return redirect()->route('cart.view')->withErrors([$response->message()]);
            }
        } catch (Exception $e) {
            return $this->handlePaymentError($e, 'cart.view', $locksPayment);
        }

        if ($cart->isPaymentModePrepayment()) {
            return $this->redirectToNextTab($cart);
        }

        return redirect()->route('entry.entrant.complete');
    }

    /**
     * Display checkout form
     *
     * @return mixed
     */
    public function checkout(Cart $cart)
    {
        return $this->respond('ecommerce.checkout', ['cart' => $cart]);
    }

    /**
     * Handle payment processing via credit card form.
     *
     * @return RedirectResponse
     */
    public function challenge(Cart $cart, ProcessPaymentFormRequest $request)
    {
        try {
            $response = $this->dispatchSync(new Process3DS2Challenge(
                $cart,
                $request->get('name'),
                $request->get('card'),
                $request->get('cardNumber'),
                $request->get('expiryMonth'),
                $request->get('expiryYear'),
                $request->get('cvv')
            ));

            if ($response->failed()) {
                return redirect()->route('cart.checkout')
                    ->withErrors(Arr::get((array) $response->json(), 'errors.0.detail'));
            }

            return redirect()->route('cart.checkout')
                ->with(array_merge(['challenge' => true], $request->validated()));
        } catch (\Exception $e) {
            return redirect()->route('cart.checkout')
                ->withErrors($e->getMessage());
        }
    }

    /**
     * Handle payment processing via credit card form.
     *
     * @return mixed
     */
    public function process(Cart $cart, ProcessPaymentFormRequest $request)
    {
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id());

        if (($gateway = $this->gateway()) instanceof ValidatesJWT) {
            if (! $gateway->validateJWT($request->get('jwt'))) {
                return redirect()->route('cart.checkout')
                    ->withErrors(trans('ecommerce.cart.validation.invalid_server_response'));
            }
        }

        if (! $cart->free() && ! $locksPayment->acquireLock()) {
            return redirect()->route('cart.checkout')->withErrors(trans('ecommerce.cart.validation.payment_in_progress'));
        }

        try {
            $response = $this->dispatchSync(new ProcessPaymentFormCommand(
                $cart,
                new CreditCard(
                    $request->input('name'),
                    $request->input('cardNumber'),
                    $request->input('expiryMonth'),
                    $request->input('expiryYear'),
                    $request->input('cvv'),
                    $request->input('card'),
                    $request->enum('network', CardNetwork::class),
                ),
            ));

            if ($response->isRedirect()) {
                return $this->respond(
                    'ecommerce.external',
                    ['url' => $response->redirectUrl(), 'data' => $response->data() ?? []]
                );
            }

            $locksPayment->releaseLock();

            if ($response->failed()) {
                return redirect()->route('cart.checkout')->withErrors($response->message());
            }
        } catch (Exception $e) {
            return $this->handlePaymentError($e, 'cart.checkout', $locksPayment);
        }

        if ($cart->isPaymentModePrepayment()) {
            return $this->redirectToNextTab($cart);
        }

        return redirect()->route('entry.entrant.complete');
    }

    private function handlePaymentError(Exception $e, $redirectRoute, LocksPayment $locksPayment)
    {
        $locksPayment->releaseLock();
        try {
            $errorMessage = match (get_class($e)) {
                InvalidCreditCardException::class => trans('ecommerce.cart.validation.invalid_card_number'),
                InvalidCreditCardDataException::class => trans('ecommerce.cart.validation.invalid_card_data'),
                InvalidRequestException::class, InvalidResponseException::class => $e->getMessage(),
                InvalidGatewayConfigException::class => trans('payments.invalid-configuration'),
                RecentPaymentException::class => trans('payments.recent-payment', ['seconds' => config('recent_payment_limit')]),
            };
        } catch (\UnhandledMatchError $newException) {
            throw $e;
        }

        return redirect()->route($redirectRoute)->withErrors($errorMessage);
    }

    public function itemsCount(Cart $cart): array
    {
        return $this->formatJson([
            'count' => $cart->itemCollection()->count(),
        ]);
    }
}
