<?php

namespace AwardForce;

use AwardForce\Library\Localisation\BaseTranslator;
use AwardForce\Library\Providers\Providable;
use AwardForce\Modules\Forms\FormsAggregateServiceProvider;
use AwardForce\Modules\Organisations\OrganisationsAggregateServiceProvider;
use Illuminate\Contracts\Filesystem\Factory;
use Illuminate\Database\Events\QueryExecuted;
use Illuminate\Http\Request;
use Illuminate\Support\AggregateServiceProvider;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\ParallelTesting;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;
use Illuminate\Translation\Translator;
use League\Flysystem\Filesystem;
use League\Flysystem\FilesystemOperator;
use League\Flysystem\MountManager;
use Platform\Http\PjaxFilter;
use Platform\Kessel\Hyperdrive;

class AwardForceServiceProvider extends AggregateServiceProvider
{
    use Providable;

    /**
     * A collection of custom aliases to register
     *
     * @var array
     */
    protected $aliases = [
        'Asset' => 'Orchestra\Support\Facades\Asset',
        'LanguageConfig' => 'Platform\Language\Facades\LanguageConfig',
        'QrCode' => 'SimpleSoftwareIO\QrCode\Facades\QrCode',
    ];

    /**
     * Files that require loading to bootstrap shift
     *
     * @var array
     */
    protected $filesToBoot = [
        'validators',
    ];

    /**
     * Files we need to register (include)
     *
     * @var array
     */
    protected $filesToRegister = [
        'composers',
    ];

    /**
     * A collection of Shift service providers to load/register.
     *
     * @var array
     */
    protected $providers = [
        'Eloquence\EloquenceServiceProvider',
        'Maatwebsite\Excel\ExcelServiceProvider',
        'Tectonic\LaravelLocalisation\ServiceProvider',
        'AwardForce\Modules\Authentication\AuthenticationServiceProvider',
        'AwardForce\Modules\Authentication\Saml\SamlServiceProvider',
        'AwardForce\Library\Authorization\AuthorizationServiceProvider',
        'AwardForce\Library\LibraryServiceProvider',
        \AwardForce\Library\Monitoring\DataDogServiceProvider::class,
        \AwardForce\Modules\AIAgents\AIAgentServiceProvider::class,
        'AwardForce\Library\PaymentSubscriptions\PaymentSubscriptionsServiceProvider',
        'AwardForce\Library\Providers\BusServiceProvider',
        'AwardForce\Library\Providers\ImgixServiceProvider',
        'AwardForce\Library\Providers\ModelObserversServiceProvider',
        'AwardForce\Modules\Accounts\AccountsServiceProvider',
        'AwardForce\Modules\Agreements\AgreementsServiceProvider',
        'AwardForce\Modules\AllocationPayments\AllocationPaymentsServiceProvider',
        'AwardForce\Modules\Api\V2\ApiV2ServiceProvider',
        'AwardForce\Modules\Awards\AwardsServiceProvider',
        'AwardForce\Modules\Assignments\AssignmentsServiceProvider',
        'AwardForce\Modules\Audit\AuditServiceProvider',
        'AwardForce\Modules\Forms\Collaboration\CollaboratorsServiceProvider',
        'AwardForce\Modules\Billing\BillingServiceProvider',
        'AwardForce\Modules\Broadcasts\BroadcastsServiceProvider',
        'AwardForce\Modules\Categories\CategoriesServiceProvider',
        'AwardForce\Modules\Chapters\ChaptersServiceProvider',
        'AwardForce\Modules\Comments\CommentsServiceProvider',
        'AwardForce\Modules\Content\Blocks\ContentBlocksServiceProvider',
        'AwardForce\Modules\Content\InterfaceText\InterfaceTextServiceProvider',
        'AwardForce\Modules\Content\Terms\TermsServiceProvider',
        'AwardForce\Modules\Documents\DocumentsServiceProvider',
        'AwardForce\Modules\DocumentTemplates\DocumentTemplatesServiceProvider',
        'AwardForce\Modules\Downloads\DownloadsServiceProvider',
        'AwardForce\Modules\Ecommerce\EcommerceServiceProvider',
        'AwardForce\Modules\Entries\EntriesServiceProvider',
        'AwardForce\Modules\Referees\RefereeServiceProvider',
        'AwardForce\Modules\Exports\ExportsServiceProvider',
        'AwardForce\Modules\Features\FeaturesServiceProvider',
        'AwardForce\Modules\Files\FilesServiceProvider',
        'AwardForce\Modules\Funding\FundingServiceProvider',
        'AwardForce\Modules\GrantReports\GrantReportsServiceProvider',
        'AwardForce\Modules\Identity\GeoIP\GeoIPServiceProvider',
        'AwardForce\Modules\Identity\Users\UsersServiceProvider',
        'AwardForce\Modules\Identity\Roles\RolesServiceProvider',
        'AwardForce\Modules\Integrations\IntegrationsServiceProvider',
        'AwardForce\Modules\Judging\JudgingServiceProvider',
        'AwardForce\Modules\Holocron\HolocronImgixServiceProvider',
        'AwardForce\Modules\Localisation\LocalisationServiceProvider',
        'AwardForce\Modules\Holocron\HolocronServiceProvider',
        'AwardForce\Modules\Notifications\NotificationsServiceProvider',
        'AwardForce\Modules\Panels\PanelsServiceProvider',
        'AwardForce\Modules\Payments\PaymentsServiceProvider',
        'AwardForce\Modules\PaymentMethods\PaymentMethodsServiceProvider',
        'AwardForce\Modules\PaymentScheduleTemplates\PaymentScheduleTemplatesServiceProvider',
        'AwardForce\Modules\Regions\RegionsServiceProvider',
        'AwardForce\Modules\Reports\ReportsServiceProvider',
        'AwardForce\Modules\ReviewFlow\ReviewFlowServiceProvider',
        'AwardForce\Modules\Rounds\RoundsServiceProvider',
        'AwardForce\Modules\ScheduledTasks\ScheduledTasksServiceProvider',
        'AwardForce\Modules\ScoreSets\ScoreSetServiceProvider',
        'AwardForce\Modules\ScoringCriteria\ScoringCriteriaServiceProvider',
        'AwardForce\Modules\Search\SearchServiceProvider',
        'AwardForce\Modules\Seasons\SeasonsServiceProvider',
        'AwardForce\Modules\Settings\SettingsServiceProvider',
        'AwardForce\Modules\Notifications\Providers\SmsCalculatorServiceProvider',
        'AwardForce\Modules\Stars\StarsServiceProvider',
        'AwardForce\Modules\Tags\TagsServiceProvider',
        'AwardForce\Modules\Theme\ThemeServiceProvider',
        'AwardForce\Modules\Webhooks\WebhooksServiceProvider',
        'AwardForce\Modules\Grants\GrantStatusServiceProvider',
        'AwardForce\Library\Providers\ContextServiceProvider',
        'AwardForce\Modules\Menu\MenuServiceProviders',
        'AwardForce\Modules\Clear\ClearServiceProvider',
        'AwardForce\Providers\HorizonServiceProvider',

        // Module aggregates
        FormsAggregateServiceProvider::class,
        OrganisationsAggregateServiceProvider::class,

        // The default routes have to be set after the Module routes
        'AwardForce\Library\Providers\RouteServiceProvider',

    ];

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        parent::register();

        $this->registerTrustedProxies();
        $this->registerAliases();
        $this->requireFiles($this->filesToRegister);
        $this->registerMiddleware();
        $this->registerBaseTranslator();
        $this->registerHyperdrive();
        $this->registerMountManager();
        $this->bootParallelTesting();
    }

    private function bootParallelTesting()
    {
        ParallelTesting::setUpProcess(function ($token) {
            $dbName = $this->app->make('db')->getDatabaseName().'_'.$token;
            try {
                if (config('database.connections.testing.drop')) {
                    Schema::dropDatabaseIfExists($dbName);
                }
            } catch (\Illuminate\Database\QueryException $e) {
                if (Str::contains($e->getMessage(), 'Unknown database') === false) {
                    throw ($e);
                }
            }

            try {
                Schema::createDatabase($dbName);
            } catch (\Illuminate\Database\QueryException $e) {
                if (Str::contains($e->getMessage(), 'database exists') === false) {
                    throw ($e);
                }
            }
        });
    }

    /**
     * Register the various classes required to Bootstrap Shift
     *
     * @returns void
     */
    public function boot()
    {
        $this->requireFiles($this->filesToBoot);
        $this->forceHttps();
        $this->setupLogging();
        Blade::withoutDoubleEncoding();
    }

    /**
     * Helper method for requiring boot files. These are files that generally have some basic configuration,
     * routes, global macros, or Laravel 4 commands that need to be registered.etc.
     *
     * @returns void
     */
    public function requireFiles(array $files)
    {
        foreach ($files as $file) {
            require __DIR__.'/../bootstrap/'.$file.'.php';
        }
    }

    protected function forceHttps()
    {
        if (! $this->app->environment('local')) {
            $this->app['url']->forceScheme('https');
        }
    }

    public function setupLogging()
    {
        if (config('database.log')) {
            $path = storage_path().'/logs/query.log';

            Event::listen(QueryExecuted::class, function (QueryExecuted $event) use ($path) {
                // Uncomment this if you want to include bindings to queries
                $sql = str_replace(['%', '?', "\n", "\r"], ['%%', "'%s'", ' ', ' '], $event->sql);
                try {
                    $sql = count($event->bindings) > 0
                        ? vsprintf($sql, $event->bindings)
                        : $event->sql;
                } catch (\Exception $e) {
                    $sql = $event->sql;
                }
                $time_now = (new \DateTime)->format('Y-m-d H:i:s');
                $log = $time_now.' | '.$event->time.'ms'.' | '.$sql.PHP_EOL;
                File::append($path, $log);
            });
        }
    }

    protected function registerTrustedProxies()
    {
        // Dirty hack to get trusted proxies working, since we trust all traffic to the app server
        collect([
            Request::HEADER_X_FORWARDED_FOR,
            Request::HEADER_X_FORWARDED_HOST,
            Request::HEADER_X_FORWARDED_PORT,
            Request::HEADER_X_FORWARDED_PROTO,
            Request::HEADER_X_FORWARDED_AWS_ELB,
        ])->each(function ($header) {
            Request::setTrustedProxies(array_merge(['127.0.0.1'], $this->app['request']->getClientIps()), $header);
        });
    }

    private function registerMiddleware()
    {
        PjaxFilter::setVersion($this->app['config']->get('app.version'));
    }

    private function registerBaseTranslator()
    {
        if (! ($this->app->make('translator') instanceof BaseTranslator)) {
            $this->app->extend('translator', function (Translator $translator, $app) {
                return new BaseTranslator($translator->getLoader(), $translator->locale());
            });
        }
    }

    private function registerHyperdrive()
    {
        $this->app->bind(Hyperdrive::class, function () {
            return Hyperdrive::forMyAF();
        });
    }

    private function registerMountManager(): void
    {
        $this->app->bind(FilesystemOperator::class, function ($app) {
            $s3 = $this->getAdapter($app, config('filesystems.disks.s3-default'));
            $local = $this->getAdapter($app, 'local');

            return new MountManager(['local' => $local, 's3' => $s3]);
        });
    }

    private function getAdapter($app, $disk): FilesystemOperator
    {
        return new Filesystem($app->make(Factory::class)->disk($disk)->getAdapter());
    }
}
