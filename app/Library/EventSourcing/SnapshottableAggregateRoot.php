<?php

namespace AwardForce\Library\EventSourcing;

use EventSauce\EventSourcing\Snapshotting\AggregateRootWithSnapshotting;

interface SnapshottableAggregateRoot extends AggregateRoot, AggregateRootWithSnapshotting
{
    /**
     * Returns the recorded events on the aggregate. Our underlying library EventSauce unfortunately does not
     * expose this detail, hence we have it here.
     */
    public function recordedEvents(): array;

    /**
     * Returns the total number of events, useful when we do not care for the array of events itself.
     */
    public function totalEvents(): int;

    /**
     * Returns a number representing the total number of recorded events required to create a snapshot.
     */
    public function threshold(): int;
}
