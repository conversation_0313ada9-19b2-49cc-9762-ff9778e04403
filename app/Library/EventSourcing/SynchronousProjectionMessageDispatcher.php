<?php

namespace AwardForce\Library\EventSourcing;

use EventSauce\EventSourcing\Message;
use Platform\Database\Eloquent\Enums\TrashedMode;

class SynchronousProjectionMessageDispatcher implements ProjectionMessageDispatcher
{
    /**
     * @var Projection[]
     */
    private array $projections = [];

    public function dispatch(Message ...$messages): void
    {
        foreach ($messages as $message) {
            $metadata = Metadata::fromMessage($message);
            foreach ($this->projections as $projectionClass) {
                $projection = $projectionClass::repository()->forAggregateRoot($message->aggregateRootId())
                    ->trashed(TrashedMode::All)
                    ->fields(['*'])
                    ->first() ?? new $projectionClass(['id' => $message->aggregateRootId()]);

                if ($projection->apply($message->payload(), $metadata)) {
                    $projection->persist();
                }
            }
        }
    }

    public function registerProjection(Projection $projection): void
    {
        $this->projections[] = $projection;
    }
}
