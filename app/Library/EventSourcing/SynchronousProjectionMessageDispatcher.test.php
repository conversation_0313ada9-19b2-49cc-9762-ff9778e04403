<?php

namespace AwardForce\Library\EventSourcing;

use AwardForce\Library\Database\EventSourcing\ProjectionRepository;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\EventSourcing\Header;
use EventSauce\EventSourcing\Message;
use Mockery as m;
use Tests\BaseTestCase;

class SynchronousProjectionMessageDispatcherTest extends BaseTestCase
{
    public function testDispatchMessagesToTheRightHandlers(): void
    {
        $event = new TestEvent('test');
        $message = new Message($event, [
            Header::AGGREGATE_ROOT_VERSION => 1,
            Header::TIME_OF_RECORDING => '2025-02-17 13:36:29.019175+0000',
            Header::AGGREGATE_ROOT_ID => StubAggregateRootId::fromString('123'),
            Header::AGGREGATE_ROOT_TYPE => snake_case(class_basename(StubAggregateRootId::class)),
        ]);

        $projection = new TestProjection;
        $otherProjection = new OtherProjection;
        $dispatcher = new SynchronousProjectionMessageDispatcher;
        $dispatcher->registerProjection($projection);
        $dispatcher->registerProjection($otherProjection);
        $dispatcher->dispatch($message);
        $this->assertTrue(TestProjection::$applied);
        $this->assertFalse(OtherProjection::$applied);
    }
}

class TestEvent implements SourcedEvent
{
    public function __construct(public string $name)
    {
    }

    public function toPayload(): array
    {
        return [
            'name' => $this->name,
        ];
    }

    public static function fromPayload(array $payload): static
    {
        return new self($payload['name']);
    }
}

class OtherEvent implements SourcedEvent
{
    public function __construct(public string $name)
    {
    }

    public function toPayload(): array
    {
        return [
            'name' => $this->name,
        ];
    }

    public static function fromPayload(array $payload): static
    {
        return new self($payload['name']);
    }
}

class TestProjection implements Projection
{
    use ProjectionLifecycle;

    public static bool $applied = false;

    public function applyTestEvent(TestEvent $event, Metadata $metadata): void
    {
        self::$applied = true;
    }

    public function persist(): void
    {
    }

    public static function repository(): ProjectionRepository
    {
        $repository = m::mock(ProjectionRepository::class);
        $repository->shouldReceive('forAggregateRoot')->andReturnSelf();
        $repository->shouldReceive('trashed')->andReturnSelf();
        $repository->shouldReceive('fields')->andReturnSelf();
        $repository->shouldReceive('first')->andReturnNull();

        return $repository;
    }
}

class OtherProjection implements Projection
{
    use ProjectionLifecycle;

    public static bool $applied = false;

    public function applyOtherEvent(OtherEvent $event, Message $message): void
    {
        self::$applied = true;
    }

    public function persist(): void
    {
    }

    public static function repository(): ProjectionRepository
    {
        $repository = m::mock(ProjectionRepository::class);
        $repository->shouldReceive('forAggregateRoot')->andReturnSelf();
        $repository->shouldReceive('trashed')->andReturnSelf();
        $repository->shouldReceive('fields')->andReturnSelf();
        $repository->shouldReceive('first')->andReturnNull();

        return $repository;
    }
}
class StubAggregateRootId implements AggregateRootId
{
    public function __construct(private string $value)
    {
    }

    public function toString(): string
    {
        return $this->value;
    }

    public static function fromString(string $aggregateRootId): static
    {
        return new static($aggregateRootId);
    }
}
