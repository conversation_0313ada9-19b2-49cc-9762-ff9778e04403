<?php

namespace AwardForce\Library\EventSourcing;

/**
 * Simple trait that can be dropped into aggregates and aggregate roots to access events recorded on that object.
 * This is particular important for snapshotting, where we want to make decisions around event sourcing snapshots,
 * based on the number of recorded events since the last snapshot.
 */
trait HasSnapshots
{
    public function recordedEvents(): array
    {
        return $this->recordedEvents;
    }

    public function threshold(): int
    {
        return 30;
    }

    public function totalEvents(): int
    {
        return count($this->recordedEvents());
    }
}
