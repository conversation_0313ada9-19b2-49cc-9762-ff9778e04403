<?php

namespace AwardForce\Library\EventSourcing;

trait ProjectionLifecycle
{
    public function apply(SourcedEvent $event, Metadata $metadata): bool
    {
        $method = 'apply'.class_basename($event);

        if (method_exists($this, $method)) {
            $this->{$method}($event, $metadata);

            return true;
        }

        return false;
    }

    public function persist(): void
    {
        self::repository()->save($this);
    }
}
