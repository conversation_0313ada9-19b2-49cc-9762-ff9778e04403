<?php

namespace AwardForce\Library\EventSourcing;

use DateTimeImmutable;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\EventSourcing\Message;

readonly class Metadata
{
    public function __construct(
        public AggregateRootId $aggregateRootId,
        public string $aggregateRootType,
        public int $version,
        public DateTimeImmutable $timeOfRecording,
        public array $headers
    ) {
    }

    public static function fromMessage(Message $message): self
    {
        return new self(
            $message->aggregateRootId(),
            $message->aggregateRootType(),
            $message->aggregateVersion(),
            $message->timeOfRecording(),
            $message->headers()
        );
    }
}
