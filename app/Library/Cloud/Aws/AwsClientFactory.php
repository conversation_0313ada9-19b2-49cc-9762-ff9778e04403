<?php

namespace AwardForce\Library\Cloud\Aws;

use Aws\AwsClientInterface;
use Aws\Credentials\CredentialProvider;
use Aws\Credentials\InstanceProfileProvider;

class AwsClientFactory
{
    public static function makeDefault(string $client, array $config = []): AwsClientInterface
    {
        return new $client(array_merge(self::config(CredentialProvider::defaultProvider()), $config));
    }

    public static function makeAssumingRole(string $client, array $config = []): AwsClientInterface
    {
        return new $client(array_merge(self::config(new InstanceProfileProvider), $config));
    }

    private static function config($provider): array
    {
        return array_merge(
            config('filesystems.disks.'.config('filesystems.default')),
            [
                'version' => 'latest',
                'credentials' => $provider,
            ]
        );
    }
}
