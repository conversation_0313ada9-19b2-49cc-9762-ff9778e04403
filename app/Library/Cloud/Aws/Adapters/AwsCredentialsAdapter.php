<?php

namespace AwardForce\Library\Cloud\Aws\Adapters;

use AwardForce\Library\Cloud\Aws\AwsClientFactory;
use Aws\Credentials\AssumeRoleCredentialProvider;
use Aws\Credentials\CredentialProvider;
use Aws\Sts\StsClient;
use Illuminate\Contracts\Config\Repository;

class AwsCredentialsAdapter
{
    /**
     * Default duration for temporary credentials - 12 hours
     */
    const DEFAULT_DURATION = 43200;

    const SESSION_NAME = 'tf-sts-role-session';

    private array $tempCredentials = [];

    public function __construct(private Repository $config)
    {
    }

    public function key(): string
    {
        return $this->tempCredentials()['key'];
    }

    public function secret(): string
    {
        return $this->tempCredentials()['secret'];
    }

    public function token(): string
    {
        return $this->tempCredentials()['token'];
    }

    private function tempCredentials(): array
    {
        if (! empty($this->tempCredentials)) {
            return $this->tempCredentials;
        }

        return $this->tempCredentials = $this->getCredentials();
    }

    private function getCredentials()
    {
        return app()->environment(['local', 'testing']) ?
            $this->stsCredentials() :
            $this->iamCredentials();
    }

    private function stsCredentials(): array
    {
        $client = AwsClientFactory::makeDefault(StsClient::class);
        $credentials = $client->getCredentials()->wait();

        return [
            'key' => $credentials->getAccessKeyId(),
            'secret' => $credentials->getSecretKey(),
            'token' => $credentials->getSecurityToken(),
        ];
    }

    private function iamCredentials(): array
    {
        $assumeRoleCredentials = new AssumeRoleCredentialProvider([
            'client' => AwsClientFactory::makeAssumingRole(StsClient::class),
            'assume_role_params' => [
                // Resolved from the pod environment variables (not laravel's)
                'RoleArn' => env('AWS_ROLE_ARN'),
                'RoleSessionName' => env('AWS_ROLE_SESSION_NAME', self::SESSION_NAME),
                'DurationSeconds' => self::DEFAULT_DURATION,
            ],
        ]);

        return CredentialProvider::memoize($assumeRoleCredentials)()
            ->wait()
            ->toArray();
    }
}
