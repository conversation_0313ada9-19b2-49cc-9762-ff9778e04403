<?php

namespace AwardForce\Library\Search\Filters;

use AwardForce\Library\Database\Eloquent\JoinAwareness;
use Platform\Search\Filters\ColumnatorFilter;

class FormJoinFilter implements ColumnatorFilter
{
    use JoinAwareness;

    public function __construct(protected string $submittableTable)
    {
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (! $this->tableJoined($query->getQuery(), 'forms')) {
            $query->join('forms', 'forms.id', "{$this->submittableTable}.form_id");
        }

        return $query;
    }

    public function applies(): bool
    {
        return true;
    }
}
