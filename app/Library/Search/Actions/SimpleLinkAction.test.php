<?php

namespace AwardForce\Library\Search\Actions;

use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class SimpleLinkActionTest extends BaseTestCase
{
    use Laravel;

    public function testRender(): void
    {
        $action = new SimpleLinkAction('the label', $url = route('category.new'));
        $this->assertStringContainsStringIgnoringCase('<a href="'.$url.'" target="_blank"', $html = $action->render(null));
        $this->assertStringContainsStringIgnoringCase('the label', $html);
    }
}
