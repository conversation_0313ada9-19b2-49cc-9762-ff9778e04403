<?php

namespace AwardForce\Library\Search\Actions;

use AwardForce\Library\Html\VueData;
use Illuminate\Support\HtmlString;

class AddToOrganisationListAction extends SimpleAction
{
    public function action(): string
    {
        return 'add-to-organisation';
    }

    protected function permissionAction(): string
    {
        return 'update';
    }

    public function render($record): HtmlString
    {
        VueData::registerTranslations([
            'organisations.titles.organisation',
            'organisations.titles.add_member',
            'organisations.selector',
            'buttons.ok',
            'buttons.cancel',
            'buttons.confirm',
            'buttons.search',
            'buttons.reset',
            'multiselect.select_all',
            'search.nothing-found',
        ]);

        return parent::render($record);
    }
}
