<?php

namespace AwardForce\Library\Search\Actions;

use Illuminate\Support\HtmlString;

abstract class SimpleAction extends ActionOverflowAction
{
    public function __construct(protected string $routeResource, private string $permissionResource, private string $key = 'id')
    {
    }

    abstract public function action(): string;

    protected function permissionAction(): string
    {
        return $this->action();
    }

    public function viewData($record): array
    {
        return [
            'resource' => $this->routeResource,
            'selected' => (string) $record->{$this->key},
            'record' => $record,
            'routeIdentifier' => $this->routeIdentifier(),
            'params' => [
                'redirect' => request()->fullUrl(),
            ],
        ];
    }

    public function applies($record = null): bool
    {
        return \Consumer::can($this->permissionAction(), $this->permissionResource);
    }

    public function render($record): HtmlString
    {
        return new HtmlString(view('partials.list-actions.'.$this->action(), $this->viewData($record))->render());
    }

    protected function routeIdentifier(): string
    {
        return 'slug';
    }
}
