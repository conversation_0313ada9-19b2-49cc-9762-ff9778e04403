<?php

namespace AwardForce\Library\Search\Columns;

use AwardForce\Library\Identifier\Snowflake\SnowflakeFactory;
use AwardForce\Library\Identifier\Snowflake\SnowflakeId;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class SnowflakeReactiveMarkerTest extends BaseTestCase
{
    use Laravel;

    public function testItRendersReactiveMarketWithSnowflake(): void
    {
        $record = new class
        {
            public SnowflakeId $id;

            public function __construct()
            {
                $this->id = app(SnowflakeFactory::class)->create();
            }

            public function resourceLabel(): string
            {
                return 'The label';
            }
        };

        $column = new SnowflakeReactiveMarker();
        $html = $column->html($record)->toHtml();

        $this->assertStringContainsString((string) $record->id, $html);
        $this->assertStringContainsString($record->resourceLabel(), $html);
    }
}
