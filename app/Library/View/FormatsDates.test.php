<?php

namespace AwardForce\Library\View;

use AwardForce\Auth\Models\Setting;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class FormatsDatesTest extends BaseTestCase
{
    use Database;
    use FormatsDates;
    use Laravel;

    public function testFormatDateWhenAccountTimezoneSettingIsNull(): void
    {
        $date = today()->addDays(5);

        $this->assertSame($date->format('d M'), $this->formatDate($date));
    }

    public function testFormatDateWhenAccountTimezoneIsNotUTC(): void
    {
        $this->muffin(Setting::class, [
            'account_id' => current_account_id(),
            'key' => 'timezone',
            'value' => 'Asia/Colombo', // UTC+5:30
        ]);

        $date = today()->addDays(5)->addHours(18)->addMinutes(30);

        $this->assertSame(today()->addDays(6)->format('d M'), $this->formatDate($date));
    }

    public function testFormatsDateForDatepickerWithoutConversion(): void
    {
        $date = today();
        $timezone = 'UTC';

        $formattedDate = $this->formatDateForDatepicker('date', $date, $timezone, false);

        $this->assertSame([
            'datetime' => $date->format('Y-m-d H:i'),
            'timezone' => $timezone,
        ], $formattedDate);
    }

    public function testFormatsDateForDatepickerWithConversion(): void
    {
        $date = today()->addDays(5)->addHours(18)->addMinutes(30);
        $timezone = 'Asia/Colombo'; // UTC+5:30

        $formattedDate = $this->formatDateForDatepicker('date', $date, $timezone);
        $convertedDate = today()->addDays(6);

        $this->assertSame([
            'datetime' => $convertedDate->format('Y-m-d H:i'),
            'timezone' => $timezone,
        ], $formattedDate);
    }
}
