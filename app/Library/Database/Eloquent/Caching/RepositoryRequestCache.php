<?php

namespace AwardForce\Library\Database\Eloquent\Caching;

use AwardForce\Modules\Accounts\Contracts\DomainRepository;
use AwardForce\Modules\Content\Terms\Contracts\TermRepository;
use AwardForce\Modules\Features\Data\FeatureRepository;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\ReviewFlow\Data\ReviewStageRepository;
use AwardForce\Modules\ReviewFlow\Data\ReviewTaskRepository;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Theme\Repositories\ThemeRepository;
use Platform\Database\Repository;

/**
 * @mixin DomainRepository $repository
 * @mixin FeatureRepository $repository
 * @mixin FieldRepository $repository
 * @mixin FormRepository $repository
 * @mixin ReviewStageRepository $repository
 * @mixin ReviewTaskRepository $repository
 * @mixin RoleRepository $repository
 * @mixin SeasonRepository $repository
 * @mixin TabRepository $repository
 * @mixin ThemeRepository $repository
 * @mixin TermRepository $repository
 * @mixin RoundRepository $repository
 */
class RepositoryRequestCache
{
    use RepositoryCacheManager;

    protected array $requestCaches = [];

    public function __construct(private Repository $repository)
    {
    }

    protected function hasCachedResult(string $cacheKey): bool
    {
        return isset($this->requestCaches[$cacheKey]);
    }

    protected function cachedResult(string $cacheKey): mixed
    {
        return $this->requestCaches[$cacheKey];
    }

    protected function cacheResult(string $cacheKey, mixed $result): void
    {
        $this->requestCaches[$cacheKey] = $result;
    }

    protected function repository(): Repository
    {
        return $this->repository;
    }
}
