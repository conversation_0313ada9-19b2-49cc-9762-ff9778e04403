<?php

namespace AwardForce\Library\Database\Eloquent\Caching;

use AwardForce\Library\Database\Eloquent\Model;
use Platform\Database\Repository;
use ReflectionMethod;

trait RepositoryCacheManager
{
    protected array $methodChain = [];

    protected function generateCacheKey(string $method, mixed $parameters): string
    {
        $key = count($this->methodChain) > 1 ? 'methodChain' : $method;
        $parameters = count($this->methodChain) > 1 ? $this->methodChain : $parameters;

        return current_account_id().':'.get_class($this->repository()).':'.$key.':'.$this->parameterKey($parameters);
    }

    protected function parameterKey(mixed $parameters): string
    {
        return match (true) {
            is_array($parameters) => implode(':', array_map(fn($arg) => $this->parameterKey($arg), $parameters)),
            $parameters instanceof Model => $parameters->getKey(),
            is_bool($parameters) => boolean_string_value($parameters),
            $parameters instanceof \BackedEnum => $parameters->value,
            $parameters instanceof \UnitEnum => $parameters->name,
            default => (string) $parameters,
        };
    }

    public function __call(string $method, array $parameters)
    {
        $reflection = new ReflectionMethod($this->repository(), $method);
        $this->methodChain[] = [$method, $parameters];

        // Check if the method's return type is the same as the repository class or a self/static type
        if (in_array($reflection->getReturnType()?->getName(), ['self', 'static', $this->repository()::class])) {
            return $this;
        }

        $cacheKey = $this->generateCacheKey($method, $parameters);

        // If the result is already cached, return it
        if ($this->hasCachedResult($cacheKey)) {
            return tap($this->cachedResult($cacheKey), fn() => $this->methodChain = []);
        }

        // Execute the chained methods sequentially
        $result = array_reduce(
            $this->methodChain,
            fn($carry, $chain) => $carry->{$chain[0]}(...$chain[1]),
            $this->repository()
        );

        $this->cacheResult($cacheKey, $result);

        // Cache the result and reset the method chain
        return tap($result, fn() => $this->methodChain = []);
    }

    abstract protected function hasCachedResult(string $cacheKey): bool;

    abstract protected function cachedResult(string $cacheKey): mixed;

    abstract protected function cacheResult(string $cacheKey, mixed $result): void;

    abstract protected function repository(): Repository;
}
