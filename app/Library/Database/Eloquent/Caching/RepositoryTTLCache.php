<?php

namespace AwardForce\Library\Database\Eloquent\Caching;

use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Illuminate\Support\Facades\Cache;
use Platform\Database\Repository;

/**
 * @mixin ScoreSetRepository $repository
 */
class RepositoryTTLCache
{
    use RepositoryCacheManager;

    public function __construct(private Repository $repository, private int $ttl)
    {
    }

    protected function hasCachedResult(string $cacheKey): bool
    {
        return Cache::has($cacheKey);
    }

    protected function cachedResult(string $cacheKey): mixed
    {
        return Cache::get($cacheKey);
    }

    protected function cacheResult(string $cacheKey, mixed $result): void
    {
        Cache::remember($cacheKey, $this->ttl, fn() => $result);
    }

    protected function repository(): Repository
    {
        return $this->repository;
    }
}
