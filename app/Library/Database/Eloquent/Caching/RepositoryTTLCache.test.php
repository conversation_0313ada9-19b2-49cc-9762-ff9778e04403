<?php

namespace AwardForce\Library\Database\Eloquent\Caching;

use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use Illuminate\Support\Facades\Cache;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Stubs\EloquentTabRepositoryWithCacheStub;

class RepositoryTTLCacheTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private EloquentTabRepositoryWithCacheStub $repository;
    private mixed $original;

    public function init(): void
    {
        $this->repository = new EloquentTabRepositoryWithCacheStub(new Tab);
        $this->original = app(TabRepository::class);
    }

    public function testItResolvesRepositoryMethodWithNoArguments()
    {
        $this->assertEquals($this->original->countAll(), $this->repository->ttlCache()->countAll());
    }

    public function testResolvesRepositoryMethodWithArguments()
    {
        $this->assertEquals(0, $this->repository->ttlCache()->methodWithArguments(1, true, 'test', ['test1', 'test2', ['asdas' => 'asdasdsad']], current_account()));
        $this->assertEquals(0, $this->repository->ttlCache()->methodWithArguments(resources: []));
    }

    public function testItCachesCalls()
    {
        $cache = Cache::spy();
        Cache::shouldReceive('has')->andReturnFalse();
        $this->repository->ttlCache()->countAll();
        $cache->shouldHaveReceived('remember')
            ->once()
            ->with(current_account_id().':'.get_class($this->repository).':countAll:', 60, m::any());
    }
}
