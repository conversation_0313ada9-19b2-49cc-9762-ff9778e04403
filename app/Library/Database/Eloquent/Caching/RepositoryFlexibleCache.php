<?php

namespace AwardForce\Library\Database\Eloquent\Caching;

use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use Illuminate\Support\Facades\Cache;
use Platform\Database\Repository;

/**
 * @mixin CategoryRepository $repository
 * @mixin ChapterRepository $repository
 * @mixin EntryRepository $repository
 * @mixin OrderRepository $repository
 */
class RepositoryFlexibleCache
{
    use RepositoryCacheManager;

    public function __construct(private Repository $repository, private int $fresh, private int $stale)
    {
    }

    protected function hasCachedResult(string $cacheKey): bool
    {
        return Cache::has($cacheKey);
    }

    protected function cachedResult(string $cacheKey): mixed
    {
        return Cache::get($cacheKey);
    }

    protected function cacheResult(string $cacheKey, mixed $result): void
    {
        Cache::flexible(
            $cacheKey,
            [$this->fresh, $this->stale],
            fn() => $result
        );
    }

    protected function repository(): Repository
    {
        return $this->repository;
    }
}
