<?php

namespace AwardForce\Library\Database\Eloquent\Caching;

use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use Illuminate\Support\Facades\Cache;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Stubs\EloquentTabRepositoryWithCacheStub;

class RepositoryFlexibleCacheTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private EloquentTabRepositoryWithCacheStub $repository;
    private TabRepository $original;

    public function init(): void
    {
        $this->repository = new EloquentTabRepositoryWithCacheStub(new Tab);
        $this->original = app(TabRepository::class);
    }

    public function testItResolvesRepositoryMethodWithNoArguments(): void
    {
        $this->assertEquals($this->original->countAll(), $this->repository->flexibleCache()->countAll());
    }

    public function testResolvesRepositoryMethodWithArguments(): void
    {
        $this->assertEquals(0, $this->repository->flexibleCache()->methodWithArguments(1, true, 'test', ['test1', 'test2', ['asdas' => 'asdasdsad']], current_account()));
        $this->assertEquals(0, $this->repository->flexibleCache()->methodWithArguments(resources: []));
    }

    public function testItCachesCalls(): void
    {
        $cachedCount = $this->repository->flexibleCache()->countAll();
        $this->assertEquals($cachedCount, $this->repository->flexibleCache()->countAll());

        $this->muffin(Tab::class);
        $this->assertEquals($cachedCount, $this->repository->flexibleCache()->countAll());
        $this->assertEquals($cachedCount + 1, $this->original->countAll());
    }

    public function testItSetsFreshAndStaleArguments(): void
    {
        Cache::spy();
        Cache::shouldReceive('has')->andReturnFalse();
        $this->repository->flexibleCache(1, 2)->countAll();
        Cache::shouldHaveReceived('flexible')->with(m::type('string'), [1, 2], m::type('callable'))->once();
    }
}
