<?php

namespace AwardForce\Library\Database\Eloquent;

use AwardForce\Library\Authorization\Consumer as ConsumerFacade;
use AwardForce\Library\Database\Builder\CacheQueries;
use Illuminate\Support\Facades\Cache;
use Platform\Authorisation\Consumer;

abstract class Model extends \Platform\Database\Eloquent\Model
{
    use CacheQueries;
    use HasToJson;
    use TranslationRetrieval;

    protected static bool $allowRawAttributes = false;

    /**
     * Flushes all cached values tagged with the cacheKey.
     */
    public function flushCacheTag()
    {
        Cache::tags($this->cacheKey())->flush();
    }

    protected function consumer(): Consumer
    {
        return ConsumerFacade::get();
    }

    /**
     * Save the model to the database, force the modification timestamp to be updated.
     *
     * @return bool
     */
    public function saveOrTouch(array $options = [])
    {
        if ($this->isDirty()) {
            return $this->save($options);
        }

        return $this->touch();
    }

    public static function allowRawAttributes(): void
    {
        self::$allowRawAttributes = true;
    }

    public static function disallowRawAttributes(): void
    {
        self::$allowRawAttributes = false;
    }

    public function changed(): array
    {
        return $this->changed;
    }
}
