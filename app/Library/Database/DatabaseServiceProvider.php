<?php

namespace AwardForce\Library\Database;

use AwardForce\Library\Database\ElasticSearch\Connection;
use AwardForce\Library\Database\EventSourcing\MySqlSnapshotRepository;
use EventSauce\EventSourcing\MessageRepository;
use EventSauce\EventSourcing\Serialization\ConstructingMessageSerializer;
use EventSauce\EventSourcing\Serialization\PayloadSerializerSupportingObjectMapperAndSerializablePayload;
use EventSauce\EventSourcing\Snapshotting\SnapshotRepository;
use EventSauce\MessageRepository\IlluminateMessageRepository\IlluminateMessageRepository;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\ServiceProvider;
use Platform\Database\Database;
use Platform\Database\Selector;

class DatabaseServiceProvider extends ServiceProvider
{
    use ParseConnections;

    public function register()
    {
        $this->registerConnections();
        $this->registerElasticSearchConnection();
        $this->registerSelector();
        $this->registerSnapshotRepository();
        $this->registerMessageRepository();
        $this->registerFactoryResolvers();
    }

    private function registerConnections(): void
    {
        $connections = [];

        foreach ($this->parse(config('database.available')) as $name => $connection) {
            $connections[] = $name;
            $this->app['config']->set("database.connections.{$name}", $this->baseOptions($connection));
        }

        Database::registerConnections(config('database.connections_override') ?: $connections);
    }

    private function baseOptions(string $url)
    {
        return [
            'url' => $url,
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => false,
        ];
    }

    private function registerSelector()
    {
        $this->app->scoped(Selector::class, DatabaseSelector::class);
    }

    private function registerElasticSearchConnection()
    {
        $this->app->extend('es', function () {
            return new Connection;
        });
    }

    private function registerMessageRepository(): void
    {
        $this->app->scoped(MessageRepository::class, function ($app) {
            return $app->make(IlluminateMessageRepository::class, ['tableName' => 'domain_events', 'serializer' => new ConstructingMessageSerializer(
                payloadSerializer: new PayloadSerializerSupportingObjectMapperAndSerializablePayload()
            )]);
        });
    }

    private function registerSnapshotRepository(): void
    {
        $this->app->scoped(SnapshotRepository::class, function ($app) {
            return new MySqlSnapshotRepository;
        });
    }

    private function registerFactoryResolvers(): void
    {
        Factory::guessFactoryNamesUsing(fn($class) => $class.'Factory');

        Factory::guessModelNamesUsing(fn(Factory $factory) => preg_replace('/Factory$/', '', $factory::class));
    }
}
