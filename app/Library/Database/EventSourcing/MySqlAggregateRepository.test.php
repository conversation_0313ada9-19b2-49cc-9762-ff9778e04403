<?php

namespace AwardForce\Library\Database\EventSourcing;

use AwardForce\Library\EventSourcing\AggregateRoot;
use AwardForce\Library\EventSourcing\HasSnapshots;
use AwardForce\Library\EventSourcing\SnapshottableAggregateRoot;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\EventSourcing\Snapshotting\AggregateRootRepositoryWithSnapshotting;
use EventSauce\EventSourcing\Snapshotting\AggregateRootWithSnapshotting;
use Mockery as m;
use Tests\BaseTestCase;

class MySqlAggregateRepositoryTest extends BaseTestCase
{
    use HasSnapshots;

    private AggregateRootRepositoryWithSnapshotting|m\MockInterface $messages;
    private MysqlAggregateRepository $repository;

    public function init(): void
    {
        $this->messages = m::mock(AggregateRootRepositoryWithSnapshotting::class);
        $this->repository = new class($this->messages) extends MysqlAggregateRepository
        {
            public function new(): AggregateRoot
            {
                // TODO: Implement new() method.
            }

            public function persist(object $aggregateRoot): void
            {
                // TODO: Implement persist() method.
            }

            public function persistEvents(
                AggregateRootId $aggregateRootId,
                int $aggregateRootVersion,
                object ...$events
            ): void {
                // TODO: Implement persistEvents() method.
            }

            public function storeSnapshot(
                AggregateRootWithSnapshotting $aggregateRoot
            ): void {
                // TODO: Implement storeSnapshot() method.
            }
        };
    }

    public function testItCanSaveAggregate(): void
    {
        $aggregate = m::mock(SnapshottableAggregateRoot::class);
        $aggregate->shouldReceive('totalEvents')->andReturn($this->threshold() - 1);
        $aggregate->shouldReceive('threshold')->andReturn($this->threshold());

        $this->messages->shouldReceive('persist')->with($aggregate);

        $this->repository->save($aggregate);
    }

    public function testItCanSaveAggregateWithSnapshot(): void
    {
        $aggregate = m::mock(SnapshottableAggregateRoot::class);
        $aggregate->shouldReceive('totalEvents')->andReturn($this->threshold());
        $aggregate->shouldReceive('threshold')->andReturn($this->threshold());

        $this->messages->shouldReceive('persist')->with($aggregate);
        $this->messages->shouldReceive('storeSnapshot')->with($aggregate);

        $this->repository->save($aggregate);
    }
}
