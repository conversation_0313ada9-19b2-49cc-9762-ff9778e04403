<?php

namespace AwardForce\Library\Database\EventSourcing;

use AwardForce\Library\EventSourcing\SnapshottableAggregateRoot;
use EventSauce\EventSourcing\Snapshotting\AggregateRootRepositoryWithSnapshotting;
use EventSauce\EventSourcing\Snapshotting\AggregateRootWithSnapshotting;

/**
 * @mixin AggregateRootRepositoryWithSnapshotting
 */
interface AggregateRepository
{
    public function save(AggregateRootWithSnapshotting&SnapshottableAggregateRoot $aggregate): void;

    public function transaction(callable $callback): mixed;
}
