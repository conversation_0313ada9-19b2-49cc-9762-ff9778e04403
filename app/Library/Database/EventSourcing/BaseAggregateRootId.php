<?php

namespace AwardForce\Library\Database\EventSourcing;

use Assert\Assertion;
use AwardForce\Library\Identifier\Snowflake\SnowflakeFactory;
use AwardForce\Library\Identifier\Snowflake\SnowflakeId;
use Illuminate\Contracts\Database\Eloquent\Castable;
use Illuminate\Contracts\Database\Eloquent\CastsAttributes;

abstract class BaseAggregateRootId extends SnowflakeId implements Castable, SnowflakeAggregateRootId
{
    public static function create(): static
    {
        return new static(app(SnowflakeFactory::class)->create()->toInteger());
    }

    public static function fromString(string $aggregateRootId): static
    {
        return new static($aggregateRootId);
    }

    public static function fromInteger(int $aggregateRootId): static
    {
        return new static($aggregateRootId);
    }

    public static function castUsing(array $attributes): CastsAttributes
    {
        return new class implements CastsAttributes
        {
            public function get($model, $key, $value, $attributes): SnowflakeAggregateRootId
            {
                $type = $model->getCasts()[$key];

                return new $type($value);
            }

            public function set($model, $key, $value, $attributes): array
            {
                Assertion::isInstanceOf($value, SnowflakeAggregateRootId::class);

                return [$key => $value->toInteger()];
            }
        };
    }
}
