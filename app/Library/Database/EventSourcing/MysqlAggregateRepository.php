<?php

namespace AwardForce\Library\Database\EventSourcing;

use AwardForce\Library\EventSourcing\SnapshottableAggregateRoot;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\EventSourcing\Snapshotting\AggregateRootRepositoryWithSnapshotting;
use EventSauce\EventSourcing\Snapshotting\AggregateRootWithSnapshotting;
use Illuminate\Support\Facades\DB;

abstract class MysqlAggregateRepository implements AggregateRepository
{
    public function __construct(private AggregateRootRepositoryWithSnapshotting $messages)
    {
    }

    public function save(AggregateRootWithSnapshotting&SnapshottableAggregateRoot $aggregate): void
    {
        $totalEvents = $aggregate->totalEvents();

        $this->messages->persist($aggregate);

        if ($totalEvents >= $aggregate->threshold()) {
            $this->messages->storeSnapshot($aggregate);
        }
    }

    public function transaction(callable $callback): mixed
    {
        return DB::transaction(fn() => $callback($this));
    }

    public function retrieve(AggregateRootId $aggregateRootId): AggregateRootWithSnapshotting
    {
        return $this->messages->retrieve($aggregateRootId);
    }

    public function retrieveFromSnapshot(AggregateRootId $aggregateRootId): AggregateRootWithSnapshotting
    {
        return $this->messages->retrieveFromSnapshot($aggregateRootId);
    }
}
