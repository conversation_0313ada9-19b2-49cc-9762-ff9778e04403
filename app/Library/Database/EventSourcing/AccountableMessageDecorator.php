<?php

namespace AwardForce\Library\Database\EventSourcing;

use EventSauce\EventSourcing\Message;
use EventSauce\EventSourcing\MessageDecorator;

class AccountableMessageDecorator implements MessageDecorator
{
    public function decorate(Message $message): Message
    {
        return $message->withHeader(
            EventsTableSchema::ACCOUNT_ID_HEADER,
            current_account_id()
        );
    }
}
