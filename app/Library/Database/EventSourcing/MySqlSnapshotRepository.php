<?php

namespace AwardForce\Library\Database\EventSourcing;

use DB;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\EventSourcing\Snapshotting\Snapshot;
use EventSauce\EventSourcing\Snapshotting\SnapshotRepository;

class MySqlSnapshotRepository implements SnapshotRepository
{
    public function persist(Snapshot $snapshot): void
    {
        DB::table('snapshots')->insert([
            'aggregate_root_id' => $snapshot->aggregateRootId()->toString(),
            'aggregate_version' => $snapshot->aggregateRootVersion(),
            'state' => json_encode($snapshot->state()),
        ]);
    }

    public function retrieve(AggregateRootId $id): ?Snapshot
    {
        $snapshot = DB::table('snapshots')
            ->where('aggregate_root_id', $id->toString())
            ->orderByDesc('id')
            ->first();

        if ($snapshot) {
            return new Snapshot($id, $snapshot->aggregate_version, $snapshot->state);
        }

        return null;
    }
}
