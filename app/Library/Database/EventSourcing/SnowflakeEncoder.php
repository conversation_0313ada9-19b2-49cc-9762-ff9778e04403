<?php

namespace AwardForce\Library\Database\EventSourcing;

use AwardForce\Library\Identifier\Snowflake\SnowflakeFactory;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\IdEncoding\IdEncoder;

class SnowflakeEncoder implements IdEncoder
{
    public function encodeId(AggregateRootId|string $id): mixed
    {
        return app(SnowflakeFactory::class)->createFromString((string) $id);
    }
}
