<?php

namespace AwardForce\Library\Database\EventSourcing;

use AwardForce\Library\Database\EventSourcing\Stubs\AggregateRootIdStub;
use EventSauce\EventSourcing\Snapshotting\Snapshot;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class MySqlSnapshotRepositoryTest extends BaseTestCase
{
    use Laravel;

    public function testSnapshotsCanBePersisted(): void
    {
        $repository = new MySqlSnapshotRepository;

        $snapshot = new Snapshot(
            AggregateRootIdStub::create(),
            100,
            ['foo' => 'bar']
        );

        $repository->persist($snapshot);

        $this->assertDatabaseHas('snapshots', [
            'aggregate_root_id' => $snapshot->aggregateRootId()->toString(),
            'aggregate_version' => $snapshot->aggregateRootVersion(),
            'state->foo' => 'bar',
        ]);
    }

    public function testSnapshotsCanBeRetrieved(): void
    {
        $repository = new MySqlSnapshotRepository;

        $aggregateRootId = AggregateRootIdStub::create();

        $this->assertNull($repository->retrieve($aggregateRootId));

        $snapshot = new Snapshot(
            $aggregateRootId,
            100,
            ['foo' => 'bar']
        );

        $repository->persist($snapshot);

        $retrievedSnapshot = $repository->retrieve($aggregateRootId);

        $this->assertEquals($snapshot->aggregateRootId()->toString(), $retrievedSnapshot->aggregateRootId()->toString());
        $this->assertEquals($snapshot->aggregateRootVersion(), $retrievedSnapshot->aggregateRootVersion());
        $this->assertEquals($snapshot->state(), json_decode($retrievedSnapshot->state(), true));
    }
}
