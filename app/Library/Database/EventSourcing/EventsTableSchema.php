<?php

namespace AwardForce\Library\Database\EventSourcing;

use EventSauce\MessageRepository\TableSchema\TableSchema;

class EventsTableSchema implements TableSchema
{
    const ACCOUNT_ID_HEADER = '__account_id';

    public function incrementalIdColumn(): string
    {
        return 'id';
    }

    public function eventIdColumn(): string
    {
        return 'event_id';
    }

    public function aggregateRootIdColumn(): string
    {
        return 'aggregate_root_id';
    }

    public function versionColumn(): string
    {
        return 'version';
    }

    public function payloadColumn(): string
    {
        return 'payload';
    }

    public function additionalColumns(): array
    {
        return [
            'account_id' => self::ACCOUNT_ID_HEADER,
        ];
    }
}
