<?php

namespace AwardForce\Library\Listeners;

use AwardForce\Modules\Files\Commands\DeleteFileCommand;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoUpdated;
use Illuminate\Foundation\Bus\DispatchesJobs;

class DeleteOrganisationLogo
{
    use DispatchesJobs;

    public function __construct(private FileRepository $files)
    {
    }

    public function whenOrganisationLogoUpdated(OrganisationLogoUpdated $event)
    {
        if ($event->oldLogoId) {
            $this->dispatch(new DeleteFileCommand($this->files->getById($event->oldLogoId)));
        }
    }

    public function whenOrganisationLogoDeleted(OrganisationLogoDeleted $event)
    {
        $this->dispatch(new DeleteFileCommand($this->files->getById($event->logoId)));
    }
}
