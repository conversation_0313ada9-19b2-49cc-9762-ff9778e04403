<?php

namespace AwardForce\Library\Listeners;

use AwardForce\Modules\Files\Commands\DeleteFileCommand;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoUpdated;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use Illuminate\Support\Facades\Bus;
use Mockery as m;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class DeleteOrganisationLogoTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private FileRepository|m $files;

    public function init()
    {
        $this->files = m::mock(FileRepository::class);
    }

    public function testDispatchDeleteFileWhenHasAnOldFile()
    {
        Bus::fake(DeleteFileCommand::class);
        $this->files->shouldReceive('getById')->once()->with(1)->andReturn($logo = $this->muffin(File::class));

        $listener = new DeleteOrganisationLogo($this->files);

        $listener->whenOrganisationLogoUpdated(new OrganisationLogoUpdated(OrganisationId::fromString('1212'), 2, 1));

        Bus::assertDispatched(DeleteFileCommand::class, function (DeleteFileCommand $command) use ($logo) {
            return $command->file->id === $logo->id;
        });
    }

    public function testShouldNotDispatchWhenOldFileIsNull()
    {
        Bus::fake(DeleteFileCommand::class);

        $listener = new DeleteOrganisationLogo($this->files);

        $listener->whenOrganisationLogoUpdated(new OrganisationLogoUpdated(OrganisationId::fromString('1212'), 2, null));

        Bus::assertNotDispatched(DeleteFileCommand::class);
    }

    public function testShouldDispatchDeleteFileCommandWhenOrganisationLogoDeleted()
    {
        Bus::fake(DeleteFileCommand::class);
        $this->files->shouldReceive('getById')->once()->with(321)->andReturn($logo = $this->muffin(File::class));
        $listener = new DeleteOrganisationLogo($this->files);

        $listener->whenOrganisationLogoDeleted(new OrganisationLogoDeleted(OrganisationId::fromString('1212'), 321));

        Bus::assertDispatched(DeleteFileCommand::class, function (DeleteFileCommand $command) use ($logo) {
            return $command->file->id === $logo->id;
        });

    }
}
