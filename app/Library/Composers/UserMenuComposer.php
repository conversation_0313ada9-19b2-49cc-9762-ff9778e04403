<?php

namespace AwardForce\Library\Composers;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Identity\Users\Services\Emulation\UserEmulator;
use Illuminate\Support\Facades\Request;
use Platform\Menu\Item;
use Platform\Menu\Menu;
use Platform\Menu\Menufy;

class UserMenuComposer
{
    /**
     * @var Manager
     */
    private $consumer;

    public function __construct(Manager $consumer)
    {
        $this->consumer = $consumer;
    }

    /**
     * Setup the required menu items, using the appropriate checks where necessary.
     */
    public function compose()
    {
        $userMenu = new Menu('user');

        $userMenu->addChild(new Item('header-profile', trans('header.profile'), route('profile.show'), 'user'));

        if (feature_enabled('organisations')) {
            $userMenu->addChild(new Item('header-organisations', trans('organisations.titles.main'), route('my-organisation.index'), 'organisation', null));
        }

        if ($this->consumer->isOwnerOrProgramManager()) {
            $userMenu->addChild(new Item('downloads', trans('downloads.titles.main'), route('download.list'), 'download'));
        }

        if (current_account()->billingPortalIsActive() && $this->consumer->isOwnerOrProgramManager()) {
            $userMenu->addChild(new Item('header-billing', trans('header.billing'), route('billing.index'), 'invoice'));
        }

        $userMenu->addChild(new Item('', '', '', attributes: ['class' => 'divider block']));

        $isManager = $this->consumer->isProgramManager() || $this->consumer->isChapterManager();
        $user = $this->consumer->user();

        if (! UserEmulator::active() && ($isManager || ($user && $user->hasMultipleMemberships()) || $this->consumer->isOwner())) {
            $brand = current_account_brand();
            $userMenu->addChild(new Item('header-my-af', trans('header.my_af'), route('app.switch', ["my-{$brand}"]), 'my', null, ['class' => 'ignore dropdown-menu-item', 'target' => '_blank', 'rel' => 'noopener noreferrer']));
        }

        if ($isManager) {
            $userMenu->addChild(new Item('header-help', trans('header.help'), app('config')->get('links.'.current_account_brand().'.support'), 'help', '_blank'));
        }

        $userMenu->addChild(new Item(
            'header-logout',
            trans('header.logout'),
            route('logout'),
            'logout',
            null,
            ['onclick' => "event.preventDefault(); document.getElementById('logout-form').submit();"]
        ));

        Menufy::register($userMenu);
        Menufy::activateByUrl(Request::url());
    }
}
