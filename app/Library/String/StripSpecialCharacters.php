<?php

namespace AwardForce\Library\String;

class StripSpecialCharacters
{
    const array SPECIAL_WHITESPACES = [
        "\u{200B}", // Zero-width space
        "\u{00A0}", // Non-breaking space
        "\u{2000}", // En quad
        "\u{2001}", // Em quad
        "\u{2002}", // En space
        "\u{2003}", // Em space
        "\u{2004}", // Three-per-em space
        "\u{2005}", // Four-per-em space
        "\u{2006}", // Six-per-em space
        "\u{2007}", // Figure space
        "\u{2008}", // Punctuation space
        "\u{2009}", // Thin space
        "\u{200A}", // Hair space
        "\u{202F}", // Narrow no-break space
        "\u{205F}", // Medium mathematical space
        "\u{3000}", // Ideographic space
        ' ',     // Non-breaking space (NBSP)
        '​',     // Zero-width space   (ZWSP)
    ];

    /**
     * Strips invisible/special whitespace characters, removes backslashes and trims the string.
     */
    public static function strip(mixed $input): mixed
    {
        if (empty($input)) {
            return $input;
        }

        if (is_array($input)) {
            return collect($input)->mapWithKeys(function ($value, $key) {
                return [self::strip($key) => self::strip($value)];
            })->toArray();
        }

        $input = str_replace(static::SPECIAL_WHITESPACES, ' ', $input);

        return trim(stripslashes($input));
    }
}
