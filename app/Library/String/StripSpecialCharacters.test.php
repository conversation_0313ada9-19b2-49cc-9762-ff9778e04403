<?php

namespace AwardForce\Library\String;

use PHPUnit\Framework\Attributes\DataProvider;
use Tests\BaseTestCase;

final class StripSpecialCharactersTest extends BaseTestCase
{
    #[DataProvider('whiteSpaces')]
    public function testItStripsInvisibleSpacesFromString(string $input): void
    {
        $this->assertEquals('Hello world', StripSpecialCharacters::strip($input.'Hello'.$input.'world'));
    }

    public static function whiteSpaces(): array
    {
        return array_map(fn($space) => [$space], StripSpecialCharacters::SPECIAL_WHITESPACES);
    }
}
