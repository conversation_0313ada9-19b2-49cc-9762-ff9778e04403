<?php

namespace AwardForce\Library\Exceptions;

use AwardForce\Library\Notifications\Slack;
use AwardForce\Modules\Accounts\Services\CurrentAccountService;
use AwardForce\Modules\Api\V2\Exceptions\ReadOnlyApiKeyCannotBeModified;
use AwardForce\Modules\Ecommerce\Cart\Costing\PriceNotAvailableException;
use AwardForce\Modules\Judging\Data\Vote;
use AwardForce\Modules\Judging\Exceptions\CannotSaveVotesException;
use AwardForce\Modules\Judging\Exceptions\JudgingViewingOnlyException;
use AwardForce\Modules\Judging\Exceptions\ScoringLockedException;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Contracts\Translation\Translator;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Http\Exceptions\ThrottleRequestsException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Session\TokenMismatchException;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Platform\Database\Selector;
use Platform\Http\FormInputOutdatedException;
use Platform\Http\Messaging;
use Platform\Http\Respondable;
use Platform\Language\UnsupportedLanguage;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Exception\HttpExceptionInterface;
use Symfony\Component\Process\Exception\ProcessTimedOutException;
use Throwable;

class Handler extends ExceptionHandler
{
    use Messaging;
    use Respondable;

    /**
     * A list of the exception types that should not be reported.
     *
     * @var array
     */
    protected $dontReport = [
        // Laravel exceptions
        \Illuminate\Auth\Access\AuthorizationException::class,
        \Illuminate\Auth\AuthenticationException::class,
        \Illuminate\Database\Eloquent\ModelNotFoundException::class,
        \Illuminate\Session\TokenMismatchException::class,
        \Illuminate\Validation\ValidationException::class,
        \Symfony\Component\HttpKernel\Exception\HttpExceptionInterface::class,

        // AF4 exceptions
        \AwardForce\Library\Exceptions\LanguageKeyErrorException::class,
        \AwardForce\Modules\Accounts\AccountNotFoundException::class,
        \AwardForce\Modules\Authentication\Exceptions\AccountRegistrationNotOpenException::class,
        \AwardForce\Modules\Authentication\Exceptions\EmailNotAllowedForRegistrationException::class,
        \AwardForce\Modules\Authentication\Exceptions\InvalidAuthenticationCredentialsException::class,
        \AwardForce\Modules\Ecommerce\Cart\Costing\PriceNotAvailableException::class,
        \AwardForce\Modules\Entries\Exceptions\MaxAttachmentsReachedException::class,
        \AwardForce\Modules\Judging\Exceptions\CannotSaveVotesException::class,
        \AwardForce\Modules\Judging\Exceptions\ScoringLockedException::class,
        \AwardForce\Modules\ReviewFlow\Exceptions\ActionAlreadyTakenException::class,
        \AwardForce\Modules\Authentication\Exceptions\UserAccountAssociationException::class,
        \AwardForce\Modules\Api\V2\Exceptions\ReadOnlyApiKeyCannotBeModified::class,
        \AwardForce\Modules\Api\V2\Exceptions\APIResourceDoesNotExistException::class,

        // Platform exceptions
        \Platform\Http\FormInputOutdatedException::class,
        \Platform\Http\UpdatedTimestampMissingException::class,
        \Platform\Language\UnsupportedLanguage::class,
    ];

    /**
     * A list of HTTP Exception status codes that should not be reported.
     *
     * @var array
     */
    private $ignoredHttpStatusCodes = [
        Response::HTTP_GONE,
    ];

    /**
     * Report or log an exception.
     *
     * This is a great spot to send exceptions to Sentry, Bugsnag, etc.
     *
     * @throws Throwable
     */
    public function report(Throwable $e): void
    {
        if ($e instanceof HttpExceptionInterface) {
            $this->reportHttpException($e);
        } elseif ($e instanceof ModelNotFoundException) {
            $this->reportModelNotFoundException($e);
        }

        parent::report($e);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  Throwable  $exception
     * @return Response|\Illuminate\Http\Response|RedirectResponse
     *
     * @throws \Illuminate\Contracts\Container\BindingResolutionException|Throwable
     */
    public function render($request, Throwable $e)
    {
        $trans = $this->container->make('translator');

        switch (true) {
            case $e instanceof AuthorizationException:
                return $this->renderUnauthorizedException($request, $e, $trans);
            case $e instanceof CannotSaveVotesException:
                return $this->renderCannotSaveVotesException($e, $trans);
            case $e instanceof FormInputOutdatedException:
                $message = $trans->get('miscellaneous.outdated');

                return $this->withMessage($request->wantsJson() ? [$message, 'error'] : $message, 'warning', null, 422);
            case $e instanceof JudgingViewingOnlyException:
                return $this->withMessage($trans->get('judging.messages.judging-viewing-only'), 'error');
            case $e instanceof LanguageKeyErrorException:
                return $this->withMessage($trans->get($e->getLanguageKey(), $e->getParameters()), 'error');
            case $e instanceof PriceNotAvailableException:
                return $this->withMessage($trans->get('miscellaneous.price_not_available'), 'error', null, 400);
            case $e instanceof ScoringLockedException:
                return $this->withMessage($trans->get('judging.messages.scoring-locked'), 'error', null, 403);
            case $e instanceof UnsupportedLanguage:
                return $this->withMessage($trans->get('languages.errors.unsupported'), 'error');
            case $e instanceof ReadOnlyApiKeyCannotBeModified:
                $message = $trans->get('api-keys.messages.read_only_api_key');

                return $this->withMessage($request->wantsJson() ? [$message, 'error'] : $message, 'warning', null, 422);
            case $e instanceof TokenMismatchException:
                $message = $request->ajax() ? 'miscellaneous.token_outdated_refresh' : 'miscellaneous.token_outdated';

                return $this->withMessage($trans->get($message), 'info', null, 419);
            case $e instanceof ThrottleRequestsException:
                $minutesRemaining = (int) ($e->getHeaders()['Retry-After'] / 60) ?: 1;

                return $this->withMessage($trans->get('errors.throttling.message', ['minutes' => $minutesRemaining]), 'warning', null, 429);
        }

        return parent::render($request, $e);
    }

    public function renderForConsole($output, Throwable $e)
    {
        if ($e instanceof ProcessTimedOutException) {
            app(Slack::class)->commandException($e->getProcess(), $e);
        }

        parent::renderForConsole($output, $e);
    }

    protected function renderHttpException(HttpExceptionInterface $e)
    {
        if ($response = $this->renderKesselHttpException($e)) {
            return $response;
        }

        if ($this->loginRedirectRequired($e)) {
            return $this->unauthenticated($this->request(), new AuthenticationException);
        }

        if ($response = $this->renderNonAccountHttpException($e)) {
            return $response;
        }

        return $this->renderAccountHttpException($e);
    }

    protected function unauthenticated($request, AuthenticationException $exception)
    {
        if ($request->expectsJson()) {
            return response()->json(['error' => 'Unauthenticated.'], 401);
        }

        return redirect()->guest(route('home'));
    }

    private function reportHttpException(HttpExceptionInterface $exception)
    {
        if (in_array($exception->getStatusCode(), $this->ignoredHttpStatusCodes)) {
            return;
        }

        $request = $this->request();

        // Only enter here if not an API call (i.e. Has a session)
        if ($request->hasSession() && $exception->getStatusCode() == Response::HTTP_UNAUTHORIZED) {
            $request->session()->put('intended', $request->fullUrl());
        }

        Log::notice("Caught HttpException for [{$exception->getStatusCode()}] {$request->fullUrl()}", [
            'fullUrl' => $request->fullUrl(),
            'status' => $exception->getStatusCode(),
            'message' => $exception->getMessage(),
            'headers' => $exception->getHeaders(),
        ]);
    }

    private function reportModelNotFoundException(ModelNotFoundException $exception)
    {
        $request = $this->request();

        Log::notice(
            "Caught ModelNotFoundException for {$request->fullUrl()}",
            ['fullUrl' => $request->fullUrl(), 'status' => 404]
        );
    }

    private function renderCannotSaveVotesException(CannotSaveVotesException $exception, Translator $trans): JsonResponse
    {
        return new JsonResponse(
            [$exception->getLevel() => $trans->get($exception->getLang(), ['minutes' => Vote::THROTTLE_VOTING_PER_MINUTES])],
            Response::HTTP_FORBIDDEN
        );
    }

    private function renderKesselHttpException(HttpExceptionInterface $exception)
    {
        if ($this->request()->getHttpHost() != config('kessel.domain')) {
            return false;
        }

        return $this->convertExceptionToResponse($exception);
    }

    /**
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    private function renderNonAccountHttpException(HttpExceptionInterface $exception)
    {
        if ($this->container->make(CurrentAccountService::class)->get()) {
            return false;
        }

        app(Selector::class)->selectFirst();

        $status = $exception->getStatusCode();

        if ($status == Response::HTTP_NOT_FOUND && ! $exception->getPrevious() instanceof ModelNotFoundException) {
            return response()->view('errors.account-not-found', [], $status, $exception->getHeaders());
        }

        if (! view()->exists($template = 'errors.'.$status)) {
            $template = 'errors.default';
        }

        $this->layout = view('layouts.external');
        $this->respond($template, ['exception' => $exception]);

        return response($this->layout, $status, $exception->getHeaders());
    }

    private function renderAccountHttpException(HttpExceptionInterface $exception)
    {
        $status = $exception->getStatusCode();

        if (! view()->exists($template = 'errors.'.$status)) {
            $template = 'errors.default';
        }

        $this->layout = view('layouts.error', ['title' => $status]);
        $this->respond($template, ['exception' => $exception]);

        return response($this->layout, $status, $exception->getHeaders());
    }

    private function loginRedirectRequired(HttpExceptionInterface $exception)
    {
        $status = $exception->getStatusCode();

        return $status === Response::HTTP_UNAUTHORIZED && ! auth()->check();
    }

    private function request(): \Illuminate\Http\Request
    {
        return $this->container->make('request');
    }

    /**
     * Convert a validation exception into a JSON response.
     *
     * @param  \Illuminate\Http\Request  $request
     */
    protected function invalidJson($request, ValidationException $exception): JsonResponse
    {
        return response()->json($exception->errors(), $exception->status);
    }

    /**
     * Return true if the exception given should not be reported.
     *
     * @param  Throwable|\Error  $e
     */
    public function shouldNotReport($e): bool
    {
        return in_array(get_class($e), $this->dontReport);
    }

    protected function renderUnauthorizedException($request, AuthorizationException $exception, $trans)
    {
        $error = ['error' => $trans->get('errors.not_allowed')];
        if ($request->expectsJson()) {
            return response()->json($error, 401);
        }

        return back()->withErrors($error);
    }
}
