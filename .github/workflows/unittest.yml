name: Unittest

on:
  workflow_dispatch:
  repository_dispatch:
  push:
    branches:
      - master

jobs:
  jsunittest:
    name: JS Unit Test
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/tectonic/jsunittest:the-force
      options: --user 1001 --privileged
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false
          ref: ${{ github.event.client_payload.ref }}
        if: github.event.client_payload.type == 'unittest'

      - name: Checkout
        uses: actions/checkout@v4
        with:
          persist-credentials: false
        if: github.event.client_payload.type != 'unittest'

      - name: Reconfigure git to use HTTPS authentication
        uses: GuillaumeFalourd/SSH-to-HTTPS@v1
        with:
          github_token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

      - name: JS test
        env:
          npm_config_cache: /home/<USER>/npm-cache
          PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
        shell: bash
        run: |
          user=$(id -u)
          echo $user
          sudo rm -rf ~/.npm
          sudo cp -r /root/.npm ~/
          sudo chown -R $user:$user ~/.npm
          export NODE_OPTIONS=--max_old_space_size=4096
          echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
          echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
          NODE_ENV=development npm install
          npm run test 2>&1 | tee -a js_test.txt
        timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
      - uses: actions/upload-artifact@v4
        with:
          path: js_test.txt
          name: js_test_output
        if: always() && github.event.client_payload.type != 'unittest'

  phpunittest:
    name: PHP Unit
    container:
      image: ghcr.io/tectonic/phpunittest:php-8-3
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    runs-on: ubuntu-20.04-core08-php
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: "tectonic123"
        ports:
          - 3306:3306
        options: >-
          --user 0
          --health-cmd="mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch-oss:6.8.22
        env:
          discovery.type: "single-node"
        ports:
          - 9200:9200
        options: >-
          --user 0
          --health-cmd "curl http://localhost:9200/_cluster/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.client_payload.ref }}
        if: github.event.client_payload.type == 'unittest'

      - name: Checkout
        uses: actions/checkout@v4
        if: github.event.client_payload.type != 'unittest'

      - name: Get Composer Cache Directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Composer cache Restore
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-

      - name: Run composer install
        run: |
          php -v
          jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
          mv composer.temp.json composer.json
          composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose

      - name: Setup MySQL
        run: |
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists build"
          mysql -hmysql -uroot -ptectonic123 -e "create database build"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_1"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_1"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_2"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_2"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_3"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_3"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_4"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_4"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_5"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_5"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_6"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_6"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_7"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_7"
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_8"
          mysql -hmysql -uroot -ptectonic123 -e "create database uild_8"

      - name: Run PHPUnit main test
        shell: bash
        run: |
          set -o pipefail
          mkdir -p /tmp/php-opcache
          ant clean
          ant prepare
          php artisan test --env=testing --parallel -p8 --colors -c phpunit.xml | tee -a ant_php_test.txt
          rm -rf /tmp/php-opcache
        timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
        env:
          TESTING_DB_HOST: mysql
          TESTING_DB_USERNAME: root
          TESTING_DB_PASSWORD: tectonic123
          TESTING_DB_DATABASE: build
          APP_KEY_NEW: ${{secrets.APP_KEY_NEW}}
          APP_ENV: testing
          DB_CONNECTIONS: mysql://root:tectonic123@mysql:3306/build
          DB_DEFAULT_CONNECTION: build
          TESTING_DB_CONNECTION: mysql://root:tectonic123@mysql:3306/build
          API_DOMAIN: api.awardforce.app
          ELASTIC_HOST: elasticsearch

      - name: Upload php main tests
        uses: actions/upload-artifact@v4
        with:
          path: ant_php_test.txt
          name: php_main_output
        if: always()

      - name: Upload the-force main logs
        uses: actions/upload-artifact@v4
        with:
          path: storage/logs
          name: theforce_storage_logs_php
        if: always()

  phpothertest:
    name: PHP Other
    container:
      image: ghcr.io/tectonic/phpunittest:php-8-3
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    runs-on: ubuntu-24.04
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: "tectonic123"
        ports:
          - 3306:3306
        options: >-
          --user 0
          --health-cmd="mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch-oss:6.8.22
        env:
          discovery.type: "single-node"
        ports:
          - 9200:9200
        options: >-
          --user 0
          --health-cmd "curl http://localhost:9200/_cluster/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.client_payload.ref }}
        if: github.event.client_payload.type == 'unittest'

      - name: Checkout
        uses: actions/checkout@v4
        if: github.event.client_payload.type != 'unittest'

      - name: Get Composer Cache Directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Composer cache Restore
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-

      - name: Run composer install
        run: |
          php -v
          jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
          mv composer.temp.json composer.json
          composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose

      - name: Setup MySQL
        run: |
          mysql -hmysql -uroot -ptectonic123 -e "drop database if exists build"
          mysql -hmysql -uroot -ptectonic123 -e "create database build"

      - name: Run PHPUnit Other test
        shell: bash
        run: |
          set -o pipefail
          mkdir -p /tmp/php-opcache
          ant unit-other 2>&1 | tee -a ant_test_php_other.txt
          rm -rf /tmp/php-opcache
        timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
        env:
          TESTING_DB_HOST: mysql
          TESTING_DB_USERNAME: root
          TESTING_DB_PASSWORD: tectonic123
          TESTING_DB_DATABASE: build
          APP_KEY_NEW: ${{secrets.APP_KEY_NEW}}
          APP_ENV: testing
          DB_CONNECTIONS: mysql://root:tectonic123@mysql:3306/build
          DB_DEFAULT_CONNECTION: build
          TESTING_DB_CONNECTION: mysql://root:tectonic123@mysql:3306/build
          API_DOMAIN: api.awardforce.app
          ELASTIC_HOST: elasticsearch

      - name: Upload php other tests
        uses: actions/upload-artifact@v4
        with:
          path: ant_test_php_other.txt
          name: php_other_output
        if: always()

      - name: Upload the-force other logs
        uses: actions/upload-artifact@v4
        with:
          path: storage/logs
          name: theforce_storage_logs_php_other
        if: always()

  behattest:
    name: Behat Tests
    runs-on: ubuntu-20.04-core08
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: "tectonic123"
        ports:
          - 3306:3306
        options: >-
          --user 0
          --health-cmd="mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch-oss:6.8.22
        env:
          discovery.type: "single-node"
        ports:
          - 9200:9200
        options: >-
          --user 0
          --health-cmd "curl http://localhost:9200/_cluster/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.client_payload.ref }}
        if: github.event.client_payload.type == 'unittest'

      - name: Checkout
        uses: actions/checkout@v4
        if: github.event.client_payload.type != 'unittest'

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
          extensions: bcmath, :php-psr, calendar, Core, ctype, curl, date, dom, exif, FFI, fileinfo, filter, ftp, gd, gettext, hash, iconv, intl, json, libxml, mbstring, mysqli, mysqlnd, openssl, pcntl, pcre, PDO, pdo_mysql, Phar, posix, readline, Reflection, session, shmop, SimpleXML, soap, sockets, sodium, SPL, standard, sysvmsg, sysvsem, sysvshm, tokenizer, xml, xmlreader, xmlwriter, xsl, zip, zlib, grpc, rdkafka
          coverage: none
          tools: composer:2.2.0

      - name: Get Composer Cache Directory
        id: composer-cache
        run: |
          echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT

      - name: Composer cache Restore
        uses: actions/cache@v4
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: |
            ${{ runner.os }}-composer-
      - name: Run composer install
        run: |
          php -v
          jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
          mv composer.temp.json composer.json
          composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose

      - name: Setup MySQL
        run: |
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_1"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_1"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_2"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_2"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_3"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_3"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_4"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_4"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_5"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_5"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_6"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_6"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_7"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_7"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_8"
          mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_8"

      - name: Run Behat test
        shell: bash
        run: |
          set -o pipefail
          mkdir -p /tmp/php-opcache
          ant behavioral 2>&1 | tee -a ant_behat_test.txt
          rm -rf /tmp/php-opcache
        timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
        env:
          TESTING_DB_HOST: 127.0.0.1
          TESTING_DB_USERNAME: root
          TESTING_DB_PASSWORD: tectonic123
          TESTING_DB_DATABASE: build
          APP_KEY_NEW: ${{secrets.APP_KEY_NEW_UAT}}
          DB_CONNECTIONS: mysql://root:tectonic123@127.0.0.1:3306/build
          DB_DEFAULT_CONNECTION: build
          TESTING_DB_CONNECTION: mysql://root:tectonic123@127.0.0.1:3306/build
          API_DOMAIN: api.awardforce.app
          ELASTIC_HOST: 127.0.0.1

      - name: Upload behat tests
        uses: actions/upload-artifact@v4
        with:
          path: ant_behat_test.txt
          name: test_output_behat
        if: always()

      - name: Upload the-force logs
        uses: actions/upload-artifact@v4
        with:
          path: storage/logs
          name: theforce_storage_logs_behat
        if: always()

#  psalm:
#    container:
#      image: ghcr.io/tectonic/github-upload-8:the-force
#      credentials:
#        username: techdeploy
#        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
#    name: PHP psalm Test
#    runs-on: ubuntu-24.04

#    steps:
#      - name: Checkout
#        uses: actions/checkout@v3
#        with:
#          ref: ${{ github.event.client_payload.ref }}
#        if: github.event.client_payload.type == 'unittest'
#
#      - name: Checkout
#        uses: actions/checkout@v3
#        if: github.event.client_payload.type != 'unittest'
#
#      - name: Remove local paths from composer.json
#        shell: bash
#        run: |
#          cat composer.json | jq 'del(.repositories[] | select(.type == "path"))' > composer-fixed.json
#          mv composer-fixed.json composer.json
#
#      - name: Install Psalm
#        shell: bash
#        env:
#          COMPOSER_CACHE_DIR: /home/<USER>/composer-cache
#        run: |
#          php -v
#          composer require --dev bamarni/composer-bin-plugin:1.6.0
#          composer bin bdd require vimeo/psalm:4.24
#          ./vendor/bin/psalm --init

 #     - name: Install and enable laravel plugin
#        shell: bash
#        env:
#          COMPOSER_CACHE_DIR: /home/<USER>/composer-cache
#        run: |
#          composer bin bdd require psalm/plugin-laravel:2.0.1
#         ./vendor/bin/psalm-plugin enable psalm/plugin-laravel
#
#      - name: Run Psalm to analyze
#        shell: bash
#        env:
#          COMPOSER_CACHE_DIR: /home/<USER>/composer-cache
#        run: |
#          ./vendor/bin/psalm --report=results.sarif || true
#
#      - name: Upload sarif file
#        uses: actions/upload-artifact@v3
#        with:
#          path: results.sarif
#          name: psalm_test_output

  slack-success:
    needs: [ jsunittest, phpunittest, phpothertest, behattest ]
    runs-on: ubuntu-24.04
    if: success()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set unittest status - failure
        uses: ./.github/set-status
        with:
          status: success
          token: ${{ github.token }}
          sha: ${{ github.sha }}

      - name: Run slack
        run: python /slack-notify/slack-test.py ${{ github.run_number }} ${{ github.sha }} good ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} ${{ github.ref }} unittest

  slack-cancelled:
    needs: [ jsunittest, phpunittest, phpothertest, behattest ]
    runs-on: ubuntu-24.04
    if: cancelled()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
      - name: Run slack
        run: python /slack-notify/slack-test.py ${{ github.run_number }} ${{ github.sha }} warning ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} ${{ github.ref }} unittest

  slack-failure:
    needs: [ jsunittest, phpunittest, phpothertest, behattest ]
    runs-on: ubuntu-24.04
    if: failure()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set unittest status - failure
        uses: ./.github/set-status
        with:
          status: failure
          token: ${{ github.token }}
          sha: ${{ github.sha }}

      - name: Run slack
        run: python /slack-notify/slack-test.py ${{ github.run_number }} ${{ github.sha }} danger ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} ${{ github.ref }} unittest
