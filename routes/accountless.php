<?php

use AwardForce\Http\Controllers\CookieConsentController;
use AwardForce\Http\Controllers\DomainSetupController;
use AwardForce\Http\Middleware\DefaultDatabase;
use AwardForce\Http\Middleware\SetEuropeRegion;
use Illuminate\Session\Middleware\StartSession;

$provisioningRoutes = function () {
    Route::get('/', [
        'uses' => 'ProvisioningController@prepare',
    ]);

    Route::post('setup', [
        'uses' => 'ProvisioningController@setup',
    ]);
};

$cookiesRoutes = function () {
    Route::post('cookies/accept', [
        'uses' => CookieConsentController::class.'@accept',
    ]);
};

foreach (explode(',', config('provisioning.root_domains')) as $domain) {
    $parts = explode('.', $domain);
    array_splice($parts, 1, 1, '{region}');

    Route::domain(implode('.', $parts))
        ->namespace('AwardForce\Http\Controllers\Provisioning')
        ->middleware(['provisioning', 'verify-provisioner'])
        ->group($provisioningRoutes);

    // This route should be added apart from the rest of provisioning routes since doesn't use verify-provisioner middleware
    Route::domain(implode('.', $parts))
        ->namespace('AwardForce\Http\Controllers\Provisioning')
        ->middleware('provisioning')
        ->group(function () {
            Route::post('domain-suggestion', [
                'uses' => 'ProvisioningController@suggestDomain',
            ]);
            Route::post('domain-availability', [
                'uses' => 'ProvisioningController@isDomainAvailable',
            ]);
        });

    Route::domain(implode('.', $parts))
        ->middleware('provisioning')
        ->group($cookiesRoutes);
}

Route::post('transcoding/update-status', ['uses' => 'AwardForce\Http\Controllers\TranscodingController@updateStatus']);
Route::post('transcriber/update-status', ['uses' => 'AwardForce\Http\Controllers\TranscriberController@updateStatus']);

foreach (array_filter(config('domains.setup')) as $setupDomain) {
    Route::domain($setupDomain)
        ->middleware([SetEuropeRegion::class, DefaultDatabase::class, StartSession::class])
        ->group(function () {
            Route::get('/', [
                'uses' => DomainSetupController::class.'@waitForDomain',
            ]);
        });
}
