<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_events', function (Blueprint $table) {
            $table->id();
            $table->binary('event_id', 16)->unique();
            $table->binary('aggregate_root_id', 36)->index();
            $table->unsignedInteger('version')->nullable();
            $table->longText('payload');
            $table->timestamps();
            $table->index(['aggregate_root_id', 'version']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_events');
    }
};
