<?php

namespace Tests\Modules\Payments\Gateways;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Payments\GatewayManager;
use AwardForce\Modules\Payments\RedirectResponse;
use AwardForce\Modules\Payments\Response;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Consumer;
use Illuminate\Support\Facades\Cache;
use Mockery as m;
use Stripe\ApiRequestor;
use Stripe\HttpClient\ClientInterface;
use Tests\IntegratedTestCase;

class StripeCheckoutTest extends IntegratedTestCase
{
    private string $clientId;
    private ClientInterface $clientMock;

    public static function gateways()
    {
        return [['Alipay'], ['Ideal']];
    }

    public function init()
    {
        \Config::set('services.stripe_connect.api_key_test', 'sk_test_Z6eA6d0tScvNuV9fuelZ9kBo');
        \Config::set('services.stripe_connect.api_key_test', 'sk_test_Z6eA6d0tScvNuV9fuelZ9kBo');
        app(SettingRepository::class)->saveSetting('payment-test-mode', true);
        app(SettingRepository::class)->saveSetting('stripe-connect-user-id', $this->clientId = 'connected-user-id');
        $membership = new Membership();
        $user = $this->consumer()->user();
        $user->setRelation('currentMembership', $membership);
        Consumer::set(new UserConsumer($user));
        $this->clientMock = m::spy(ClientInterface::class);
        ApiRequestor::setHttpClient($this->clientMock);
    }

    /**
     * @dataProvider gateways
     */
    public function testItRedirectToStripeAfterPurchase(string $gatewayName)
    {
        $gateway = GatewayManager::create($gatewayName);
        $amount = 13.95;
        $gateway->setCurrency($currency = 'CNY');
        $reponse = [
            'id' => 'FakeId',
            'url' => $stripeRedirectUrl = 'https://stripe.com/redirect',
        ];

        $this->clientMock->shouldReceive('request')->withArgs(function ($method, $url, $headers, $params) use ($amount, $currency, $gateway) {
            $this->assertStringContainsString(config('services.stripe_connect.api_key_test'), collect($headers)->filter(fn($value) => \Str::contains($value, 'Bearer'))->first());
            $this->assertStringContainsString($this->clientId, collect($headers)->filter(fn($value) => \Str::contains($value, 'Stripe-Account'))->first());
            $this->assertEquals($amount * 100, $params['line_items'][0]['price_data']['unit_amount']);
            $this->assertEquals($currency, $params['line_items'][0]['price_data']['currency']);
            $this->assertEquals($gateway->getReturnUrl(), $params['success_url']);
            $this->assertEquals($gateway->getCancelUrl(), $params['cancel_url']);

            return true;
        })->andReturn([json_encode($reponse), 200, []]);

        $response = $gateway->purchase($amount);

        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals($stripeRedirectUrl, $response->redirectUrl());
    }

    /**
     * @dataProvider gateways
     */
    public function testItReturnsASuccessResponseWhenSucceed(string $gatewayName)
    {
        $gateway = GatewayManager::create($gatewayName);
        Cache::put(payment_cache_key(Consumer::user()->id, Consumer::user()->currentMembership->id), ['sessionId' => 'FakeId']);

        $response = $gateway->complete();

        $this->assertInstanceOf(Response::class, $response);
        $this->assertTrue($response->success());
    }

    /**
     * @dataProvider gateways
     */
    public function testItUsesTheApiKeyBasedOnTheTestMode(string $gatewayName)
    {
        $settings = m::mock(SettingRepository::class);
        $settings
            ->shouldReceive('getAllAsKeyValue')
            ->andReturn(
                ['payment-test-mode' => true, 'stripe-connect-user-id' => $this->clientId],
                ['payment-test-mode' => false, 'stripe-connect-user-id' => $this->clientId]
            );
        app()->instance(SettingRepository::class, $settings);

        \Config::spy();
        \Config::shouldReceive('get')->once()->with('services.stripe_connect.api_key_test', null)->andReturn('test');
        \Config::shouldReceive('get')->once()->with('services.stripe_connect.api_key_live', null)->andReturn('live');

        GatewayManager::create($gatewayName);
        GatewayManager::create($gatewayName);
    }

    /**
     * @dataProvider gateways
     */
    public function testItStoresThePaymentParams(string $gatewayName)
    {
        $gateway = GatewayManager::create($gatewayName);
        $gateway->setCurrency('CNY');
        $reponse = ['id' => 'FakeId'];
        $this->clientMock->shouldReceive('request')->andReturn([json_encode($reponse), 200, []]);

        $gateway->purchase(10);

        $this->assertEquals(['sessionId' => 'FakeId'], Cache::get(payment_cache_key(Consumer::user()->id, Consumer::user()->currentMembership->id)));
    }

    /**
     * @dataProvider gateways
     */
    public function testItClearParamsAfterComplete(string $gatewayName)
    {
        $gateway = GatewayManager::create($gatewayName);
        Cache::put(payment_cache_key(Consumer::user()->id, Consumer::user()->currentMembership->id), ['sessionId' => 'FakeId']);
        $this->assertNotNull(Cache::get(payment_cache_key(Consumer::user()->id, Consumer::user()->currentMembership->id)));

        $gateway->complete();

        $this->assertNull(Cache::get(payment_cache_key(Consumer::user()->id, Consumer::user()->currentMembership->id)));
    }

    /**
     * @dataProvider gateways
     */
    public function testItReturnsErrorResponseWhenApiKeyIsNotValid(string $gatewayName)
    {
        \Config::set('services.stripe_connect.api_key_test', '');
        $gateway = GatewayManager::create($gatewayName);
        $gateway->setCurrency('CNY');
        $response = $gateway->purchase(10);

        $this->assertFalse($response->success());
    }
}
