<?php

namespace Tests\Modules\Settings\Repositories;

use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Repositories\EloquentSettingRepository;
use AwardForce\Modules\Settings\Values\OrganisationsSettings;
use Tests\IntegratedTestCase;

final class EloquentSettingRepositoryTest extends IntegratedTestCase
{
    /** @var EloquentSettingRepository */
    private $repository;

    protected function setUp(): void
    {
        parent::setUp();

        $this->repository = app(EloquentSettingRepository::class);
        $this->app->instance(SettingRepository::class, $this->repository);
    }

    public function testGetBySetting(): void
    {
        $this->repository->saveSetting('setting', 'value');

        $savedSetting = $this->repository->getValueByKey('setting');

        $this->assertEquals('value', $savedSetting);
    }

    public function testEntryPaymentOnStart(): void
    {
        $this->assertFalse($this->repository->entryPaymentOnStart());

        Feature::shouldReceive('enabled')->with('order_payments')->andReturn(true);
        $this->assertFalse($this->repository->entryPaymentOnStart());

        $this->repository->saveSetting('paid-entries', true);
        $this->assertFalse($this->repository->entryPaymentOnStart());

        $this->repository->saveSetting('entry-payment', 'submit');
        $this->assertFalse($this->repository->entryPaymentOnStart());

        $this->repository->saveSetting('entry-payment', 'start');
        $this->assertTrue($this->repository->entryPaymentOnStart());
    }

    public function testRespectsDefaultValueForEmptyString(): void
    {
        $this->assertEquals(null, $this->repository->getValueByKey('tricky-setting-key'));
        $this->assertEquals(2, $this->repository->getValueByKey('tricky-setting-key', 2));

        $this->repository->saveSetting('tricky-setting-key', '');

        $this->assertEquals(null, $this->repository->getValueByKey('tricky-setting-key'));

        $this->repository->saveSetting('tricky-setting-key', '');

        $this->assertEquals(50, $this->repository->getValueByKey('tricky-setting-key', 50));

        $this->repository->saveSetting('tricky-setting-key', '0');

        $this->assertEquals(0, $this->repository->getValueByKey('tricky-setting-key', 99));

        $this->repository->saveSetting('tricky-setting-key', '{"documents":["pdf"]}');

        $this->assertEquals('{"documents":["pdf"]}', $this->repository->getValueByKey('tricky-setting-key', 0));
    }

    public function testItLoadsOrganisationSettings(): void
    {
        $this->repository->saveSetting('organisations', json_encode(['enabled' => false, 'joinOnRegisteredEmail' => true]));

        $settings = $this->repository->organisationSettings();

        $this->assertInstanceOf(OrganisationsSettings::class, $settings);
        $this->assertFalse($settings->enabled);
        $this->assertTrue($settings->joinOnRegisteredEmail);
    }
}
