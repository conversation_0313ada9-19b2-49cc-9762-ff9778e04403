<?php

namespace Tests\Modules\Identity\Users\Search\Filters;

use AwardForce\Modules\Identity\Users\Search\Filters\UserKeywordFilter;
use Mockery as m;
use Tests\UnitTestCase;

final class UserKeywordFilterTest extends UnitTestCase
{
    protected $query;

    public function init()
    {
        $this->query = m::spy('query');
    }

    public function testMakeApostropheSearch(): void
    {
        $query = \DB::query();
        $keywords = 'chinedu m\'mike';
        $filter = new UserKeywordFilter($keywords, []);
        $filterResponse = $filter->applyToEloquent($query);
        $sqlQuery = 'select * where (`users`.`first_name` LIKE ? or `users`.`last_name` LIKE ? or `users`.`email` LIKE ?)';
        $this->assertSame($sqlQuery, $filterResponse->toSql());
    }

    public function testIsEmailSearch(): void
    {
        $filter = new UserKeywordFilter('<EMAIL>', []);
        $filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('where')->with('users.email', '=', '<EMAIL>');
    }

    public function testIsPartialEmailSearch(): void
    {
        $filter = new UserKeywordFilter('abc.com', []);
        $filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('where')->with('users.email', 'LIKE', '%abc.com%');

        $filter = new UserKeywordFilter('abc@abc', []);
        $filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('where')->with('users.email', 'LIKE', '%abc@abc%');
    }

    public function testIsFirstNameOrLastNameSearch(): void
    {
        $filter = new UserKeywordFilter('Luke', []);
        $filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('whereRaw')->withArgs(function ($query, $binding) {
            return $query == 'MATCH(users.first_name, users.last_name, users.email) AGAINST (? IN BOOLEAN MODE)' && $binding == 'Luke';
        });
    }

    public function testIsFullNameSearch(): void
    {
        $filter = new UserKeywordFilter('Luke Skywalker', []);
        $filter->applyToEloquent($this->query);

        $this->query->shouldHaveReceived('whereRaw')->withArgs(function ($query, $binding) {
            return $query == 'MATCH(users.first_name, users.last_name, users.email) AGAINST (? IN BOOLEAN MODE)' && $binding == 'Luke Skywalker';
        });
    }
}
