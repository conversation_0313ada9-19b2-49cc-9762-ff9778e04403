<?php

namespace Tests\Modules\Accounts;

use AwardForce\Library\Filesystem\Courier;
use AwardForce\Library\Filesystem\Storage;
use AwardForce\Modules\Accounts\Commands\CreateAccountCommand;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\GlobalAccount;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Models\UserFactory;
use AwardForce\Modules\Localisation\Repositories\TranslationRepository;
use AwardForce\Modules\NewDashboard\DataObjects\Dashboards;
use AwardForce\Modules\Theme\Events\ThemeConfigurationWasUpdated;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Mockery as m;
use Ramsey\Uuid\Uuid;
use Tests\IntegratedTestCase;

final class CreateAccountTest extends IntegratedTestCase
{
    use DispatchesJobs;

    protected $input;
    protected $newAccount;

    public function init()
    {
        $storage = m::mock(Storage::class);
        $storage->shouldReceive('courier')->andReturn(m::mock(Courier::class)->shouldReceive('copySeedToRemote')->getMock());
        $this->app->instance(Storage::class, $storage);

        $userFactory = m::mock(UserFactory::class);
        $userFactory->shouldReceive('requireUserByGlobalId')->andReturn($owner = $this->muffin(User::class))->byDefault();
        $this->app->instance(UserFactory::class, $userFactory);

        Event::forget(ThemeConfigurationWasUpdated::class);
        Config::set('domains.white_label', ['awardsplatform.com', 'grantplatform.com']);

        $this->input = [
            'name' => 'Test account name',
            'supportedLanguages' => ['ar_AR', 'en_GB', 'fr_FR'],
            'defaultLanguageCode' => 'en_GB',
            'domain' => 'test.awardsplatform.com',
            'owner' => $owner->globalId,
            'brand' => 'awardforce',
            'vertical' => 'awards',
            'product' => 'usd-m-professional-1',
            'dashboards' => [],
            'attributes' => [
                'region' => 'au',
                'startDate' => '2020-01-01',
                'dealId' => '123',
            ],
        ];

        $globalAccount = new GlobalAccount;
        $globalAccount->id = Uuid::uuid4();

        $this->hyperdrive->shouldReceive('create')->andReturn(['globalId' => $globalAccount->id]);
        $this->hyperdrive->shouldReceive('update');

        $this->newAccount = $this->dispatchSync(new CreateAccountCommand(
            $this->input['owner'],
            $this->input['name'],
            $this->input['domain'],
            $this->input['brand'],
            $this->input['vertical'],
            $this->input['product'],
            $this->input['supportedLanguages'],
            $this->input['defaultLanguageCode'],
            Dashboards::fromArray([]),
            $this->input['attributes']
        ));
    }

    public function testSupportedAndDefaultLanguages(): void
    {
        $this->assertInstanceOf(Account::class, $this->newAccount);
        $this->assertCount(3, $this->newAccount->languages);
        $this->assertSame($this->input['defaultLanguageCode'], $this->newAccount->defaultLanguage()->code);
    }

    public function testContentBlockSeedDataIsSeededForAllSupportedLanguagesWhenCreatingANewAccount(): void
    {
        $this->useNewAccount();

        $translations = app(TranslationRepository::class)->getByResource('ContentBlock');

        foreach ($this->newAccount->languages as $language) {
            $code = $language->code;

            $titles = $translations->where('language', $code)->where('field', 'title');
            $contents = $translations->where('language', $code)->where('field', 'content');

            // We currently have 10 seeded content block on account creation.
            // Test to see if we have the correct number of translations.
            $this->assertCount(10, $titles);
            $this->assertCount(10, $contents);
        }
    }

    public function testNotificationSeedDataIsSeededForAllSupportedLanguagesWhenCreatingANewAccount(): void
    {
        $this->useNewAccount();

        $translations = app(TranslationRepository::class)->getByResource('Notification');

        foreach ($this->newAccount->languages as $language) {
            $code = $language->code;

            $subjects = $translations->where('language', $code)->where('field', 'subject');
            $bodies = $translations->where('language', $code)->where('field', 'body');

            $this->assertCount($expectedCount = count(config('notifications.seed')), $subjects);
            $this->assertCount($expectedCount, $bodies);
        }
    }

    public function testRoleSeedDataIsSeededForAllSupportedLanguagesWhenCreatingANewAccount(): void
    {
        $this->useNewAccount();

        $translations = app(TranslationRepository::class)->getByResource('Role');

        foreach ($this->newAccount->languages as $language) {
            $names = $translations->where('language', $language->code)->where('field', 'name');

            $this->assertCount(9, $names);
        }
    }

    public function testRoundSeedDataIsSeededForAllSupportedLanguagesWhenCreatingANewAccount(): void
    {
        $this->useNewAccount();

        $translations = app(TranslationRepository::class)->getByResource('Round');

        foreach ($this->newAccount->languages as $language) {
            $names = $translations->where('language', $language->code)->where('field', 'name');

            $this->assertCount(2, $names);
        }
    }

    public function testTabSeedDataIsSeededForAllSupportedLanguagesWhenCreatingANewAccount(): void
    {
        $this->useNewAccount();

        $fieldsTab = app(TabRepository::class)->getOneBy('type', Tab::TYPE_FIELDS);
        $attachmentsTab = app(TabRepository::class)->getOneBy('type', Tab::TYPE_ATTACHMENTS);

        $fieldsTabTranslations = app(TranslationRepository::class)->getByResource('Tab')
            ->where('foreign_id', $fieldsTab->id)
            ->where('field', 'name');

        $attachmentsTabTranslations = app(TranslationRepository::class)->getByResource('Tab')
            ->where('foreign_id', $attachmentsTab->id)
            ->where('field', 'name');

        $this->assertCount(3, $fieldsTabTranslations);
        $this->assertCount(3, $attachmentsTabTranslations);
    }

    public function testFieldSeedDataIsSeededForAllSupportedLanguagesWhenCreatingANewAccount(): void
    {
        $this->useNewAccount();

        $fields = app(FieldRepository::class)->getAll();

        foreach ($fields as $field) {
            $translations = app(TranslationRepository::class)->getByResource('Field')
                ->where('foreign_id', $field->id)
                ->where('field', 'title');
            // This has dependency on translations being available for the supported languages ('ar_AR', 'en_GB', 'fr_FR')
            $this->assertGreaterThanOrEqual(1, $translations->count());
        }
    }

    public function testCategorySeedDataIsSeededForAllSupportedLanguagesWhenCreatingANewAccount(): void
    {
        $this->useNewAccount();

        $categories = app(CategoryRepository::class)->getAll();

        foreach ($categories as $category) {
            $translations = app(TranslationRepository::class)->getByResource('Category')
                ->where('foreign_id', $category->id)
                ->where('field', 'name');

            $this->assertCount(3, $translations);
        }
    }

    private function useNewAccount()
    {
        $this->account = $this->newAccount;
        CurrentAccount::set($this->newAccount);
    }
}
