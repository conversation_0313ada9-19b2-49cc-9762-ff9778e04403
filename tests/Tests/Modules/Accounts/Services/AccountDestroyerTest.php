<?php

namespace Tests\Modules\Accounts\Services;

use AwardForce\Library\Filesystem\Storage;
use AwardForce\Library\Imports\Import;
use AwardForce\Modules\Accounts\Commands\DestroyAccountCommand;
use AwardForce\Modules\Accounts\Commands\DestroyAccountCommandHandler;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\Domain;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Accounts\Services\AccountDestroyer;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Features\Data\Feature;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Funding\Data\Fund;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Payments\Models\Tax;
use AwardForce\Modules\Reports\Report;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Settings\Models\Setting;
use GuzzleHttp\Client;
use Mockery as m;
use Platform\Events\EventDispatcher;
use Platform\Kessel\Hyperdrive;
use Platform\Localisation\Translation;
use Tests\IntegratedTestCase;

final class AccountDestroyerTest extends IntegratedTestCase
{
    use EventDispatcher;

    private Account $keepAccount;
    private Account $destroyAccount;

    public function init()
    {
        $this->keepAccount = $this->account;
        $this->destroyAccount = $this->muffin(Account::class);
        $this->user = $this->muffin(User::class);
        CurrentAccount::set($this->destroyAccount);
    }

    public function testDeletesAccount(): void
    {
        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $report = Report::latest()->first();

        $this->assertContains(['key' => 'accounts.deleted', 'payload' => ['slug' => (string) $this->destroyAccount->slug]], $report->details);

        $this->assertNull(Account::find($this->destroyAccount->id));
        $this->assertEmpty(Translation::where('resource', 'Account')->where('foreign_id', $this->destroyAccount->id)->get());

        $this->assertNotNull(Account::find($this->keepAccount->id));
        $this->assertNotEmpty(Translation::where('resource', 'Account')->where('foreign_id', $this->keepAccount->id)->get());
    }

    public function testDeletesSeasons(): void
    {
        $destroyA = $this->muffin(Season::class, ['account_id' => $this->destroyAccount->id]);
        $destroyB = $this->muffin(Season::class, ['account_id' => $this->destroyAccount->id]);
        $keep = $this->muffin(Season::class, ['account_id' => $this->keepAccount->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $report = Report::latest()->first();
        $this->assertContains(['key' => 'seasons.deleted', 'payload' => ['slug' => (string) $destroyA->slug]], $report->details);
        $this->assertContains(['key' => 'seasons.deleted', 'payload' => ['slug' => (string) $destroyB->slug]], $report->details);

        $this->assertNull(Season::find($destroyA->id));
        $this->assertNull(Season::find($destroyB->id));
        $this->assertEmpty(Translation::where('resource', 'Season')->whereIn('foreign_id', [$destroyA->id, $destroyB->id])->get());

        $this->assertNotNull(Season::find($keep->id));
        $this->assertNotEmpty(Translation::where('resource', 'Season')->where('foreign_id', $keep->id)->get());
    }

    public function testDeletesContentBlocks(): void
    {
        $keep = $this->muffin(ContentBlock::class, ['account_id' => $this->keepAccount->id]);
        $destroy = $this->muffin(ContentBlock::class, ['account_id' => $this->destroyAccount->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $report = Report::latest()->first();
        $this->assertContains(['key' => 'content-block.deleted', 'payload' => ['count' => 1]], $report->details);

        $this->assertNull(ContentBlock::find($destroy->id));
        $this->assertEmpty(Translation::where('resource', 'ContentBlock')->where('foreign_id', $destroy->id)->get());

        $this->assertNotNull(ContentBlock::find($keep->id));
        $this->assertNotEmpty(Translation::where('resource', 'ContentBlock')->where('foreign_id', $keep->id)->get());
    }

    public function testDeletesDomains(): void
    {
        $this->destroyAccount->setVanityDomain('destroy.awardforce.local');
        $this->keepAccount->setVanityDomain('keep.awardforce.local');

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $this->assertEmpty(Domain::whereAccountId($this->destroyAccount->id)->get());
        $this->assertNotEmpty(Domain::whereAccountId($this->keepAccount->id)->get());
    }

    public function testDeletesFeatures(): void
    {
        // keepAccount already has features set up
        Feature::add($this->destroyAccount->id, new \Platform\Features\Feature('audit', 'enabled'));

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $this->assertEmpty(Feature::whereAccountId($this->destroyAccount->id)->get());
        $this->assertNotEmpty(Feature::whereAccountId($this->keepAccount->id)->get());
    }

    public function testDeletesImports(): void
    {
        $destroy = new Import;
        $destroy->accountId = $this->destroyAccount->id;
        $destroy->save();

        $keep = new Import;
        $keep->accountId = $this->keepAccount->id;
        $keep->save();

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $this->assertEmpty(Import::whereAccountId($this->destroyAccount->id)->get());
        $this->assertNotEmpty(Import::whereAccountId($this->keepAccount->id)->get());
    }

    public function testDeletesMemberships(): void
    {
        $this->muffin(Membership::class, ['account_id' => $this->destroyAccount->id]);
        $this->muffin(Membership::class, ['account_id' => $this->keepAccount->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $this->assertEmpty(Membership::whereAccountId($this->destroyAccount->id)->get());
        $this->assertNotEmpty(Membership::whereAccountId($this->keepAccount->id)->get());
    }

    public function testDeletesFunds(): void
    {
        $keep = $this->muffin(Fund::class, ['account_id' => $this->keepAccount->id]);
        $destroy = $this->muffin(Fund::class, ['account_id' => $this->destroyAccount->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $report = Report::latest()->first();
        $this->assertContains(['key' => 'funding.deleted', 'payload' => ['count' => 1]], $report->details);

        $this->assertNull(Fund::find($destroy->id));
        $this->assertEmpty(Translation::where('resource', 'Fund')->where('foreign_id', $destroy->id)->get());

        $this->assertNotNull(Fund::find($keep->id));
        $this->assertNotEmpty(Translation::where('resource', 'Fund')->where('foreign_id', $keep->id)->get());
    }

    public function testDeletesTaxes(): void
    {
        $keep = $this->muffin(Tax::class, ['account_id' => $this->keepAccount->id]);
        $destroy = $this->muffin(Tax::class, ['account_id' => $this->destroyAccount->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $report = Report::latest()->first();
        $this->assertContains(['key' => 'payments.taxes_deleted', 'payload' => ['count' => 1]], $report->details);

        $this->assertNull(Tax::find($destroy->id));
        $this->assertNotNull(Tax::find($keep->id));
    }

    public function testDeletesRoles(): void
    {
        $keepRole = $this->muffin(Role::class, ['account_id' => $this->keepAccount->id]);
        $this->muffin(Permission::class, ['role_id' => $keepRole->id]);

        $destroyRole = $this->muffin(Role::class, ['account_id' => $this->destroyAccount->id]);
        $this->muffin(Permission::class, ['role_id' => $destroyRole->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $report = Report::latest()->first();
        $this->assertContains(['key' => 'roles.deleted', 'payload' => ['count' => 1]], $report->details);

        $this->assertNull(Role::find($destroyRole->id));
        $this->assertEmpty(Permission::whereRoleId($destroyRole->id)->get());

        $this->assertNotNull(Role::find($keepRole->id));
        $this->assertNotEmpty(Permission::whereRoleId($keepRole->id)->get());
    }

    public function testDeletesSettings(): void
    {
        $keep = $this->muffin(Setting::class, ['account_id' => $this->keepAccount->id]);
        $destroy = $this->muffin(Setting::class, ['account_id' => $this->destroyAccount->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $this->assertNull(Setting::find($destroy->id));
        $this->assertNotNull(Setting::find($keep->id));
    }

    public function testDeletesFiles(): void
    {
        $keep = $this->muffin(File::class, ['account_id' => $this->keepAccount->id, 'resource' => File::RESOURCE_THEME]);
        $destroy = $this->muffin(File::class, ['account_id' => $this->destroyAccount->id, 'resource' => File::RESOURCE_THEME]);
        $destroyDir = dirname($destroy->file);

        $filesystem = m::spy(Storage::class);
        $filesystem->shouldReceive('exists')->once()->with($destroyDir)->andReturn(true);
        app()->instance(Storage::class, $filesystem);

        $hyperDrive = m::mock(Hyperdrive::class)->makePartial();
        $hyperDrive->shouldReceive('delete')->with('accounts/'.(string) $this->destroyAccount->globalId)->andReturn(true);
        app()->instance(Hyperdrive::class, $hyperDrive);

        config(['webhooks.account_destroyed' => 'http://delete.webhook.local']);
        $client = m::mock(Client::class);
        $client->shouldReceive('post')->once()->with(config('webhooks.account_destroyed'), m::type('array'));
        app()->instance(Client::class, $client);

        $destroyer = app(AccountDestroyer::class);
        $destroyer->destroy($this->destroyAccount, $this->user);
        $this->dispatch($destroyer->releaseEvents());

        $this->assertNull(File::find($destroy->id));
        $this->assertNotNull(File::find($keep->id));

        $filesystem->shouldHaveReceived('deleteDirectory')->once()->with($destroyDir);
        $filesystem->shouldNotHaveReceived('exists', [dirname($keep->file)]);
    }

    public function testDispatchesWebhook(): void
    {
        $client = m::spy(Client::class);
        app()->instance(Client::class, $client);

        $hyperDrive = m::spy(Hyperdrive::class);
        $hyperDrive->shouldReceive('delete')->with('accounts/'.(string) $this->destroyAccount->globalId)->andReturn(true);
        app()->instance(Hyperdrive::class, $hyperDrive);

        config(['webhooks.account_destroyed' => 'http://delete.wbhook.local']);

        $accounts = m::mock(AccountRepository::class);
        $accounts->shouldReceive('getActiveSeason')->once()->andReturn($this->destroyAccount->activeSeason());
        app()->instance(AccountRepository::class, $accounts);

        $handler = app(DestroyAccountCommandHandler::class);
        $handler->handle(new DestroyAccountCommand($this->user->email));

        $client->shouldHaveReceived('post')->once()->with(config('webhooks.account_destroyed'), \Mockery::on(function (array $payload) {
            $this->assertArrayHasKey('form_params', $payload);
            $formParams = $payload['form_params'];
            $this->assertArrayHasKey('account', $formParams);
            $this->assertEquals($this->destroyAccount->id, $formParams['account']['id']);
            $this->assertEquals($this->destroyAccount->falconId, $formParams['account']['falconId']);
            $this->assertEquals((string) $this->destroyAccount->globalId, $formParams['account']['globalId']);
            $this->assertEquals((string) $this->destroyAccount->slug, $formParams['account']['slug']);
            $this->assertEquals($this->destroyAccount->region, $formParams['account']['region']);
            $this->assertNotEmpty($formParams['account']['deletedAt']);

            return true;
        }));
    }

    public function testDeleteGrantStatuses(): void
    {
        $grantStatus = $this->muffin(GrantStatus::class, ['account_id' => $this->destroyAccount->id]);
        $this->muffin(GrantStatus::class, ['account_id' => $this->keepAccount->id]);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $this->assertNull(GrantStatus::find($grantStatus->id));
        $this->assertNotNull(GrantStatus::whereAccountId($this->keepAccount->id)->first());
    }

    public function testDeleteOrganisations()
    {
        $keep = Organisation::factory()->create(['account_id' => $this->keepAccount->id, 'season_id' => $this->keepAccount->activeSeason()->id,  'name' => 'ShouldKeep']);
        $destroy = Organisation::factory()->create(['account_id' => $this->destroyAccount->id, 'season_id' => $this->destroyAccount->activeSeason()->id, 'name' => 'ShouldDestroy']);

        app(AccountDestroyer::class)->destroy($this->destroyAccount, $this->user);

        $this->assertNull(Organisation::find($destroy->id));
        $this->assertNotNull(Organisation::find($keep->id));
    }
}
