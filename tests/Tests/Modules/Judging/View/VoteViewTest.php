<?php

namespace Tests\Modules\Judging\View;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\VisibleAttachments;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\AttachmentTypes;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\Factory;
use AwardForce\Modules\Forms\Fields\Services\VisibleFields\ScoreSetBased;
use AwardForce\Modules\Judging\Services\TagEntryPermissions;
use AwardForce\Modules\Judging\Services\Voting\CachedCounter;
use AwardForce\Modules\Judging\View\VoteView;
use AwardForce\Modules\Rounds\Services\RoundStatus;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Illuminate\Http\Request;
use Mockery as m;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tests\UnitTestCase;

final class VoteViewTest extends UnitTestCase
{
    private Request $request;
    private Manager $manager;
    private Factory $visibleFields;
    private VisibleAttachments $visibleAttachments;
    private AttachmentTypes $attachmentTypes;
    private TagEntryPermissions $tagEntryPermissions;
    private int $entryId;
    private $consumer;
    private $rounds;

    public function init()
    {
        $this->translator = m::mock(Engine::class);
        $this->translator->shouldReceive('translate')
            ->andReturnUsing(function ($value) {
                return $value;
            });

        $this->consumer = $this->mockConsumer();
        $this->manager = m::mock(Manager::class);
        $this->manager->shouldReceive('get')->andReturn($this->consumer);

        $this->visibleFields = m::mock(Factory::class);
        $this->visibleFields->shouldReceive('scoreSetBased')
            ->andReturn(m::mock(ScoreSetBased::class));
        $this->visibleFields->shouldReceive('get')
            ->andReturn($this->manager);

        $this->visibleAttachments = m::mock(VisibleAttachments::class);

        $this->attachmentTypes = m::mock(AttachmentTypes::class);

        $this->request = m::mock(Request::class)->makePartial();
        $this->request->shouldReceive('all')->andReturn([]);
        $this->request->shouldReceive('route')->andReturn(m::mock(['getName' => 'voting']));

        $scoreSet = new ScoreSet;
        $scoreSet->mode = ScoreSet::MODE_VOTING;
        $this->request->scoreSet = $scoreSet;

        $this->entryId = 1;
        $entry = m::mock(Entry::class);
        $entry->shouldReceive('formableResourceId')->andReturn($this->entryId);
        $entry->shouldReceive('getTranslatableFields')->andReturn([]);
        $entry->shouldReceive('getAttribute')->andReturn($this->entryId);
        $this->request->entry = $entry;

        $this->tagEntryPermissions = app(TagEntryPermissions::class);

        $this->rounds = m::mock(RoundStatus::class);
    }

    public function testUsedVotes(): void
    {
        $consumerId = $this->consumer->id();
        $voteCount = 5;

        $votes = m::mock(CachedCounter::class);

        $votes->shouldReceive('countJudge')->with($this->request->scoreSet, $consumerId)->andReturn($voteCount);

        $view = new VoteView($this->request, $this->translator, $this->manager, $votes, $this->visibleFields, $this->visibleAttachments, $this->attachmentTypes, $this->rounds, $this->tagEntryPermissions);
        $result = $view->usedVotes();

        $this->assertEquals($voteCount, $result);
    }

    public function testConsumerVotes(): void
    {
        $consumerId = $this->consumer->id();
        $voteCount = 3;

        $consumer = m::mock(Consumer::class);
        $consumer->shouldReceive('id')->andReturn($consumerId);

        $votes = m::mock(CachedCounter::class);
        $votes->shouldReceive('countJudgeEntryAll')->with($this->request->scoreSet, $consumerId, $this->entryId)->andReturn($voteCount);

        $view = $this->voteView($votes);

        $result = $view->consumerVotes();

        $this->assertEquals($voteCount, $result);
    }

    public function testTotalVotes(): void
    {
        $consumerId = $this->consumer->id();
        $entryId = $this->entryId;
        $voteCount = 3;

        $consumer = m::mock(Consumer::class);
        $consumer->shouldReceive('id')->andReturn($consumerId);

        $votes = m::mock(CachedCounter::class);
        $votes->shouldReceive('countEntryAll')->with($this->request->scoreSet, $entryId)->andReturn($voteCount);

        $view = $this->voteView($votes);

        $result = $view->totalVotes();

        $this->assertEquals($voteCount, $result);
    }

    public function testCurrentUserVotesWithVoteButtonToUnvote(): void
    {
        $consumerId = $this->consumer->id();
        $entryId = $this->entryId;
        $voteCount = 3;

        $consumer = m::mock(Consumer::class);
        $consumer->shouldReceive('id')
            ->andReturn($consumerId);

        $votes = m::mock(CachedCounter::class);
        $votes->shouldReceive('countJudgeEntry')
            ->with($this->request->scoreSet, $consumerId, $entryId)
            ->andReturn($voteCount);
        $votes->shouldReceive('countEntryAll')
            ->with($this->request->scoreSet, $entryId)
            ->andReturn($voteCount);

        $scoreSet = m::mock(ScoreSetBased::class);
        $scoreSet->shouldReceive('votesRevokable')
            ->andReturn(true);
        $scoreSet->shouldReceive('maxVotesPerEntry')
            ->andReturn(1);

        $view = $this->voteView($votes);

        $result = $view->currentUserVotes();

        $this->assertEquals($voteCount, $result);
    }

    public function testCurrentUserVotesWithoutVoteButtonToUnvote(): void
    {
        $entryId = $this->request->entry->id;
        $voteCount = 3;

        $votes = m::mock(CachedCounter::class);
        $votes->shouldReceive('countEntryAll')
            ->with($this->request->scoreSet, $entryId)
            ->andReturn($voteCount);

        $scoreSet = m::mock(ScoreSetBased::class);
        $scoreSet->shouldReceive('votesRevokable')
            ->andReturn(false);

        $view = $this->voteView($votes);

        $result = $view->currentUserVotes();

        $this->assertEquals($voteCount, $result);
    }

    public function testCantVoteIfRoundsAreViewOnly(): void
    {
        $votes = m::mock(CachedCounter::class);

        $this->rounds->shouldReceive('isViewOnlyJudging')->andReturn(true)->once();

        $view = new VoteView($this->request, $this->translator, $this->manager, $votes, $this->visibleFields, $this->visibleAttachments, $this->attachmentTypes, $this->rounds, $this->tagEntryPermissions);

        $this->assertFalse($view->canVote());
    }

    public function testCanVoteIfRoundsAreNotForViewOnly(): void
    {
        $votes = m::mock(CachedCounter::class);

        $this->rounds->shouldReceive('isViewOnlyJudging')->andReturn(false)->once();

        app()->instance(CurrentAssignments::class, $currentAssignments = m::mock(CurrentAssignments::class));
        $currentAssignments->shouldReceive('judgingAllowed')->andReturn(true)->once();

        $view = new VoteView($this->request, $this->translator, $this->manager, $votes, $this->visibleFields, $this->visibleAttachments, $this->attachmentTypes, $this->rounds, $this->tagEntryPermissions);

        $this->assertTrue($view->canVote());
    }

    private function voteView($votes)
    {
        return app(VoteView::class, [
            'request' => $this->request,
            'translator' => $this->translator,
            'manager' => $this->manager,
            'votes' => $votes,
            'visibleFields' => $this->visibleFields,
            'visibleAttachments' => $this->visibleAttachments,
            'attachmentTypes' => $this->attachmentTypes,
            'rounds' => $this->rounds,
        ]);
    }
}
