<?php

namespace Tests\Modules\Judging\Services;

use AwardForce\Library\PDF\Browsershot;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Judging\Composers\ScoringPDFComposer;
use AwardForce\Modules\Judging\Services\ScoringPDFGenerator;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Mockery as m;
use Tests\IntegratedTestCase;

final class ScoringPDFGeneratorTest extends IntegratedTestCase
{
    protected ScoringPDFGenerator $generator;
    protected Browsershot $pdf;

    public function init()
    {
        $this->app->instance(Browsershot::class, $this->pdf = m::spy(Browsershot::class));
    }

    public function testGenerateScoringPDF(): void
    {
        $this->pdf->shouldReceive('html')->andReturnSelf()->once();
        $this->pdf->shouldIgnoreMissing($this->pdf);
        $entry = $this->muffin(Entry::class);
        $judge = $this->setupUserWithRole('Judge');

        ScoringPDFGenerator::instance($entry, new AssignmentUser($judge))->generateScoringPDF();
    }

    /**
     * ITS J.2
     */
    public function testEntrantNameShouldNoBeShownOnJudgePdf(): void
    {
        $entry = $this->muffin(Entry::class);
        $judge = $this->setupUserWithRole('Judge');

        $this->pdf->shouldIgnoreMissing($this->pdf);
        $this->pdf->shouldReceive('html')->withArgs(function ($arg) use ($entry) {
            return ! Str::contains($arg, $entry->user->name);
        })->andReturnSelf()->once();

        ScoringPDFGenerator::instance($entry, new AssignmentUser($judge))->generateScoringPDF();
    }

    /**
     * ITS J.2
     */
    public function testEntrantNameShouldBeShownOnJudgePdf(): void
    {
        app()->instance(ScoringPDFComposer::class, $scoring = m::spy(ScoringPDFComposer::class));
        $scoring->shouldReceive('compose')->withArgs(function (View $view) {
            $view->with([
                'mode' => 'mode',
                'tabbedFields' => [],
                'referees' => collect([]),
                'hideRefereeEmail' => false,
                'hideRefereeName' => false,
                'additionalCriteria' => [],
                'contributors' => collect([]),
                'attachments' => collect([]),
                'links' => collect([]),
                'scoringBoxes' => false,
                'displayEntryName' => false,
                'displayEntrantName' => true,
                'attachmentDownload' => false,
            ]);

            return true;
        });
        $entry = $this->muffin(Entry::class);
        $judge = $this->setupUserWithRole('Judge');

        $this->pdf->shouldIgnoreMissing($this->pdf);
        $this->pdf->shouldReceive('html')->withArgs(function ($arg) use ($entry) {
            return Str::contains($arg, $entry->user->name);
        })->andReturnSelf()->once();

        ScoringPDFGenerator::instance($entry, new AssignmentUser($judge))->generateScoringPDF();
    }
}
