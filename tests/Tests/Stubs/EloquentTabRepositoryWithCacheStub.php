<?php

namespace Tests\Stubs;

use AwardForce\Library\Database\Eloquent\Caching\HasFlexibleCache;
use AwardForce\Library\Database\Eloquent\Caching\HasTTLCache;
use AwardForce\Library\Database\Eloquent\Caching\RepositoryRequestCache;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\EloquentTabRepository;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentTabRepositoryWithCacheStub extends EloquentTabRepository implements BuilderRepository
{
    use HasFlexibleCache;
    use HasQueryBuilder;
    use HasRequestCacheStub;
    use HasTTLCache;

    public function methodWithArguments(?int $id = null, bool $locked = false, string $slug = '', array $resources = [], ?Account $account = null): int
    {
        return $this->getQuery()
            ->where('id', $id)
            ->where('locked', $locked)
            ->where('slug', $slug)
            ->where('account_id', $account?->id ?? current_account_id())
            ->whereIn('resource', $resources)
            ->count();
    }

    public function resource(string $resource): self
    {
        $this->query()->where('resource', $resource);

        return $this;
    }
}

trait HasRequestCacheStub
{
    public RepositoryRequestCacheStub $requestCache;

    public function requestCache(): RepositoryRequestCache
    {
        return $this->requestCache ??= new RepositoryRequestCacheStub($this);
    }
}

class RepositoryRequestCacheStub extends RepositoryRequestCache
{
    public function getCacheKey(string $name, array $arguments): string
    {
        return $this->generateCacheKey($name, $arguments);
    }

    public function getParameterKey(mixed $arguments): string
    {
        return $this->parameterKey($arguments);
    }

    public function getCacheKeys(): array
    {
        return array_keys($this->requestCaches);
    }
}
