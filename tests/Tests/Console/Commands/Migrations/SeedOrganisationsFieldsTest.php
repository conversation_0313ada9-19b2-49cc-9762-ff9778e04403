<?php

namespace Tests\Console\Commands\Migrations;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use Illuminate\Support\Facades\Artisan;
use Tests\IntegratedTestCase;
use Tests\OrganisationFieldValidation;

class SeedOrganisationsFieldsTest extends IntegratedTestCase
{
    use OrganisationFieldValidation;

    public function init()
    {
        $this->fields = app(FieldRepository::class);
        $this->account->vertical = 'awards';
    }

    public function testOrganisationFieldsSeed()
    {
        $this->markTestSkipped('Until we re-implement organisation fields');
        Artisan::call('migrate:seed-organisations-fields');

        $orgFields = $this->fields->getByResource(Field::RESOURCE_ORGANISATIONS);
        $this->assertCount(4, $orgFields);
        $orgFields->each(fn($orgField) => $this->validateOrgField($orgField));
    }

    public function testOrganisationFieldsSeededTwice()
    {
        $this->markTestSkipped('Until we re-implement organisation fields');
        Artisan::call('migrate:seed-organisations-fields');
        Artisan::call('migrate:seed-organisations-fields');

        $orgFields = $this->fields->getByResource(Field::RESOURCE_ORGANISATIONS);
        $this->assertCount(4, $orgFields);
        $orgFields->each(fn($orgField) => $this->validateOrgField($orgField));
    }
}
