Feature: View/Create/Update/Delete fields

  Background:
    Given I am currently logged in as an "Program Manager"

  Scenario: View existing fields
    Given a number of fields exist
    Then I should see the fields list view page

  Scenario: Create field
    Given I want to create a field
    And I should be able to preview a new field
    Then I should be able to save that field

  Scenario: I should not be able to create a field without permissions
    Given I am currently logged in as an "Chapter Manager"
    Then I should get an error when creating the field

  Scenario: Update field
    Given a number of fields exist
    And I can edit a field
    Then I should be able to update that field

  Scenario: I should not be able to update a field without permissions
    Given I am currently logged in as an "Chapter Manager"
    And a number of fields exist
    Then I should get an error when updating a field

  Scenario: Delete field
    Given a number of fields exist
    Then I should be able to delete a specific field

  Scenario: Create Field validation
    Given I want to create a field
    And I try to save that field as checkbox list
    And validation fails
    Then I should be able to see the previous option inputs

  Scenario: Create table field
    Given I want to create a field
    And I should be able to preview a new field
    Then I should be able to save a table field

  Scenario: Create formula field
    Given I want to create a field
    And I should be able to preview a new formula field
    Then I should be able to save that formula field

  Scenario: Create and update a single file upload field with min and max video length
    Given I want to create a field
    And I should be able to see the min video length attribute
    And I should be able to see the max video length attribute
    Then I should be able to save that single file upload field
    And I should be able to update that single file upload field

  Scenario: Cannot view user field form when feature is disabled
    Given the user_fields feature is disabled
    And I want to access the user field creation form
    Then I should be redirected to user_fields disabled

  Scenario: Can view user field form when feature is enabled
    Given the user_fields feature is enabled
    And I want to access the user field creation form
    Then The response should be successfully

  Scenario: User field radio button disabled when feature is disabled
    Given the user_fields feature is disabled
    And I want to create a resource field
    Then I should see the user field radio button disabled
    And I should see the learn more button for user fields feature
