<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Forms\Fields\Configurations\Simple;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Carbon\Carbon;
use PHPUnit\Framework\Assert;

trait FieldFeatures
{
    /** @var array */
    private $fields;

    /**
     * @Given a number of fields exist
     */
    public function numberOfFieldsExist()
    {
        $numOfRecords = mt_rand(1, 5);
        for ($i = 0; $i < $numOfRecords; $i++) {
            $this->fields[] = $this->muffin(Field::class);
        }
    }

    /**
     * @Then I should see the fields list view page
     */
    public function iShouldSeeTheFieldsListViewPage()
    {
        $this->route('GET', 'field.index');
        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to view a specific field
     */
    public function iShouldBeAbleToViewASpecificField()
    {
        $this->route('GET', 'field.edit', [$this->fields[0]->slug]);
        $this->assertResponseOk();
    }

    /**
     * @Given I want to create a field
     */
    public function iWantToCreateAField()
    {
        $this->route('GET', 'field.new');

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to preview a new field
     */
    public function iShouldBeAbleToPreviewANewField()
    {
        $this->route('POST', 'field.preview', [], array_merge($this->formData(), ['type' => 'table']));

        $this->assertResponseOk();
        $this->seeJsonContains(['success' => true]);
    }

    /**
     * @Then I should be able to preview a new formula field
     */
    public function iShouldBeAbleToPreviewANewFormulaField(): void
    {
        $this->route('POST', 'field.preview', [], $this->formulaData());

        $this->assertResponseOk();
        $this->seeJsonContains(['success' => true]);
    }

    /**
     * @Then I should be able to save that field
     */
    public function iShouldBeAbleToSaveThatField()
    {
        $this->route('POST', 'field.add', [], $this->withInput($this->formData()));
        $this->assertNoErrors();
        $this->assertRedirectedToRoute('field.index');
    }

    /**
     * @Then I should get an error when creating the field
     */
    public function iShouldGetAnErrorWhenCreatingTheField(): void
    {
        $this->route('POST', 'field.add', [], $this->withInput($this->formData()));
        $this->assertSessionHasErrors();
    }

    /**
     * @Then I should be able to save that formula field
     */
    public function iShouldBeAbleToSaveThatFormulaField()
    {
        $this->route('POST', 'field.add', [], $this->withInput($this->formulaData()));
        $this->assertNoErrors();
        $this->assertRedirectedToRoute('field.index');
    }

    /**
     * @Then I should be able to see the min video length attribute
     */
    public function iShouldBeAbleToSeeTheMinVideoLengthAttribute(): void
    {
        $this->assertResponseContains('id="minVideoLength"');
        $this->assertResponseContains(trans('fields.form.min_video_length.label'));
    }

    /**
     * @Then I should be able to see the max video length attribute
     */
    public function iShouldBeAbleToSeeTheMaxVideoLengthAttribute(): void
    {
        $this->assertResponseContains('id="maxVideoLength"');
        $this->assertResponseContains(trans('fields.form.max_video_length.label'));
    }

    /**
     * @Then I should be able to save that single file upload field
     */
    public function iShouldBeAbleToSaveThatSingleFileUploadField(): void
    {
        $this->route('POST', 'field.add', [], $this->withInput($this->singleFileUploadData()));
        $this->assertNoErrors();
        $this->assertRedirectedToRoute('field.index');

        $field = Field::where('type', 'file')->first();

        $this->assertSame(10, $field->minVideoLength);
        $this->assertSame(100, $field->maxVideoLength);
    }

    /**
     * @Then I should be able to update that single file upload field
     */
    public function iShouldBeAbleToUpdateThatSingleFileUploadField(): void
    {
        $field = Field::where('type', 'file')->first();

        $this->route('PUT', 'field.update', ['field' => $field->slug], [
            'type' => 'file',
            'formId' => Form::first()->id,
            'tabId' => 1,
            'minVideoLength' => 20,
            'maxVideoLength' => 200,
            'updatedAt' => Carbon::now()->addWeek(),
            'resource' => 'Entries',
            'protection' => Field::PROTECTION_STANDARD,
        ]);

        $this->assertNoErrors();
        $this->assertRedirectedToRoute('field.index');

        $this->assertSame(20, $field->fresh()->minVideoLength);
        $this->assertSame(200, $field->fresh()->maxVideoLength);
    }

    /**
     * @Then I should be able to save a table field
     */
    public function iShouldBeAbleToSaveATableField()
    {
        $formData = $this->formData('table');
        $formData['configuration'] = '{"columns":["column-mokTU","column-cDiNm","column-swgix"],"rows":["row-mYQTo","row-CGLqz","row-MZntP"],"filters":[{"column":"column-mokTU","row":null,"rules":{"type":"label"}}],"dynamicRowsEnabled":false,"calculations":[]}';

        $this->route('POST', 'field.add', [], $this->withInput($formData));

        $this->assertRedirectedToRoute('field.index');
    }

    private function formData($type = 'text'): array
    {
        return [
            'account_id' => current_account()->id,
            'seasonId' => current_account()->activeSeason()->id,
            'formId' => FormSelector::getId(),
            'resource' => Field::RESOURCE_FORMS,
            'type' => $type,
            'categoryOption' => 'all',
            'tabId' => '1',
            'translated' => [
                'title' => ['en_GB' => 'Title - '.str_random()],
            ],
            'configuration' => json_encode(new Simple()),
            'protection' => Field::PROTECTION_STANDARD,
            'order' => 1,
        ];
    }

    private function formulaData(): array
    {
        return array_merge($this->formData(), [
            'type' => 'formula',
            'formulaConfiguration' => json_encode(['formula' => '=SUM(1,2,3)']),
        ]);
    }

    private function singleFileUploadData(): array
    {
        return array_merge($this->formData(), [
            'type' => 'file',
            'minVideoLength' => 10,
            'maxVideoLength' => 100,
        ]);
    }

    /**
     * @Given I can edit a field
     */
    public function iCanEditAField()
    {
        $this->route('GET', 'field.edit', [$this->fields[0]->slug]);
        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to update that field
     */
    public function iShouldBeAbleToUpdateThatField()
    {
        $field = $this->fields[0];
        $data = [
            'account_id' => $field->accountId,
            'seasonId' => $field->seasonId,
            'resource' => Field::RESOURCE_FORMS,
            'formId' => $field->formId,
            'type' => 'text',
            'categoryOption' => 'all',
            'tabId' => '1',
            'translated' => [
                'title' => ['en_GB' => 'Updated Title - '.str_random()],
            ],
            'updatedAt' => Carbon::now()->addWeek(),
            'configuration' => json_encode(new Simple()),
            'protection' => $field->protection,
        ];

        $this->route('PUT', 'field.update', ['field' => $field->slug], $data);
        $this->assertRedirectedToRoute('field.index');
    }

    /**
     * @Then I should get an error when updating a field
     */
    public function iShouldGetAnErrorWhenUpdatingAField(): void
    {
        $field = $this->fields[0];

        $data = [
            'updatedAt' => Carbon::now()->addWeek(),
        ];

        $this->route('PUT', 'field.update', ['field' => $field->slug], $data);
        $this->assertSessionHasErrors();
    }

    /**
     * @Then I should be able to delete a specific field
     */
    public function iShouldBeAbleToDeleteASpecificField()
    {
        $field = $this->fields[0];
        $this->route('DELETE', 'field.delete', [], $this->withInput(['selected' => [$field->id]]));
        $this->assertRedirectedToRoute('field.index');
        $this->route('GET', 'field.index');
        $this->assertResponseOk();
        $this->assertResponseNotContains((string) $field->slug);
    }

    /**
     * @Given /^I try to save that field as checkbox list$/
     */
    public function iTryToSaveThatField()
    {
        $data = $this->formData();
        $data['type'] = 'checkboxlist';
        $data['options'] = "Option A\r\nOption B";
        $data['translated'] = [
            'optionText' => [
                'en_GB' => json_encode(['Option A' => 'Option Text A', 'Option B' => 'Option Text B']),
            ],
        ];

        $this->route('POST', 'field.add', [], $this->withInput($data));
    }

    /**
     * @Given /^validation fails$/
     */
    public function validationFails()
    {
        $this->assertSessionHasErrors();
    }

    /**
     * @Then /^I should be able to see the previous option inputs$/
     */
    public function iShouldBeAbleToSeeThePreviousOptionInputs()
    {
        $this->get($this->response->getTargetUrl().'?resource=Entries');
        $data = $this->response->original->getData()['content']->getData();
        Assert::assertArrayHasKey('translatedField', $data);
        Assert::assertArrayHasKey('options', $data['translatedField']);

        Assert::assertIsArray($data['translatedField']['options']);
        Assert::assertEquals('Option A', $data['translatedField']['options'][0]['id']);
        Assert::assertEquals('Option Text A', $data['translatedField']['options'][0]['text']);

        Assert::assertArrayHasKey('translated', $data['translatedField']);
        Assert::assertArrayHasKey('en_GB', $data['translatedField']['translated']);
        Assert::assertArrayHasKey('optionText', $data['translatedField']['translated']['en_GB']);

        Assert::assertIsArray($data['translatedField']['translated']['en_GB']['optionText']);
        Assert::assertArrayHasKey('Option A', $data['translatedField']['translated']['en_GB']['optionText']);
        Assert::assertArrayHasKey('Option B', $data['translatedField']['translated']['en_GB']['optionText']);

        Assert::assertEquals('Option Text A', $data['translatedField']['translated']['en_GB']['optionText']['Option A']);
        Assert::assertEquals('Option Text B', $data['translatedField']['translated']['en_GB']['optionText']['Option B']);
    }

    /**
     * @Given I want to access the user field creation form
     */
    public function iWantToAccessTheuserFieldCreationForm()
    {
        $this->get(route('field.new').'?resource=Users');
    }

    /**
     * @Given I want to create a resource field
     */
    public function iWantToCreateAResourceField()
    {
        $this->route('GET', 'field.resource');
    }

    /**
     * @Then I should see the user field radio button disabled
     */
    public function iShouldSeeTheUserFieldRadioButtonDisabled()
    {
        $this->assertResponseContains('<input type="radio" name="resource" id="add-field-1" value="Users" disabled>');
    }

    /**
     * @Then I should see the learn more button for user fields feature
     */
    public function iShouldSeeTheLearMoreButtonForUserFieldsfeature()
    {
        $this->assertResponseContains('href="/feature-disabled/user_fields"');
    }

    /**
     * @Then I should see the screen reader text for the field
     */
    public function iShouldSeeTheScreeReaderTextForTheField()
    {
        $this->assertResponseContains(trans('buttons.action_overflow', ['resource' => $this->fields[0]->resourceLabel()]));
        $this->assertResponseContains(trans('buttons.checkbox_for_resource', ['resource' => $this->fields[0]->resourceLabel()]));
    }

    /**
     * @Then I should see the organisation resource filter
     */
    public function iShouldSeeTheOrganisationResourceFilter()
    {
        $organisationResourceOption = '<option value="'.Field::RESOURCE_ORGANISATIONS.'">'.trans('tabs.resources.organisations').'</option>';
        $this->assertResponseContains($organisationResourceOption);
    }
}
