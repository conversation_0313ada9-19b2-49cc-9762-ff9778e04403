<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Webhooks\Models\Webhook;

trait WebhooksFeatures
{
    /**
     * @Given /^a number of webhooks exist$/
     */
    public function aNumberOfWebhooksExist()
    {
        $this->webhooks = $this->muffins(3, Webhook::class);
    }

    /**
     * @Then /^I should see a list of webhooks$/
     */
    public function iShouldSeeAListOfWebhooks()
    {
        $this->route('GET', 'webhook.index');

        $this->assertResponseOk();
    }

    /**
     * @Then /^I should not see a list of webhooks$/
     */
    public function iShouldNotSeeAListOfWebhooks()
    {
        $this->route('GET', 'webhook.index');

        $this->assertResponseStatus(403);
    }

    /**
     * @Then /^I should be able to view a specific webhook$/
     */
    public function iShouldBeAbleToViewASpecificWebhook()
    {
        $this->route('GET', 'webhook.edit', [$this->webhooks[0]->slug]);

        $this->assertResponseOk();
    }

    /**
     * @Then /^I should not be able to view a specific webhook$/
     */
    public function iShouldNotBeAbleToViewASpecificWebhook()
    {
        $this->route('GET', 'webhook.edit', [$this->webhooks[0]->slug]);

        $this->assertResponseStatus(403);
    }

    /**
     * @Given /^I want to create a webhook$/
     */
    public function iWantToCreateAWebhook()
    {
        $this->route('GET', 'webhook.new');

        $this->assertResponseOk();
    }

    /**
     * @Then /^I should be able to save that webhook$/
     */
    public function iShouldBeAbleToSaveThatWebhook()
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);
        $fields = collect($this->muffins(random_int(2, 3), Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS]));
        app(FormRepository::class)->nukeRequestCache();

        $data = [
            'name' => 'Test webhook',
            'httpMethod' => 'post',
            'url' => 'http://notapplicable.app',
            'signingKey' => '1234567',
            'subscriptionEvents' => config('webhooks.subscription_events'),
            'notificationEmails' => '<EMAIL>,<EMAIL>',
            'form' => (string) $form->slug,
            'fields' => $fields->map(fn(Field $field) => (string) $field->slug)->all(),
        ];

        $this->route('POST', 'webhook.create', [], $this->withInput($data))->assertSessionHasNoErrors();
        $this->assertRedirectedToRoute('webhook.index');
        $this->assertSame(config('webhooks.subscription_events'), Webhook::first()->subscriptionEvents);
        $this->assertSame($form->id, Webhook::first()->formId);
        $this->assertTrue(Webhook::first()->fields()->pluck('id')->contains($fields->first()->id), 'Field not attached to webhook');
    }

    /**
     * @Then /^I should not be able to save that webhook$/
     */
    public function iShouldNotBeAbleToSaveThatWebhook()
    {
        $this->route('POST', 'webhook.create', [], $this->withInput([]));

        $this->assertResponseStatus(302);
        $this->assertSessionHasErrors(['error' => 'You do not have the right permissions to perform this action.']);
    }

    /**
     * @Given /^I can edit a webhook$/
     */
    public function iCanEditAWebhook()
    {
        $this->route('GET', 'webhook.edit', [$this->webhooks[0]->slug]);

        $this->assertResponseOk();
    }

    /**
     * @Then /^I should be able to update that webhook$/
     */
    public function iShouldBeAbleToUpdateThatWebhook()
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['allowApiUpdates' => true])]);
        $fields = collect($this->muffins(random_int(2, 3), Field::class, ['form_id' => $form->id, 'resource' => Field::RESOURCE_FORMS]));
        app(FormRepository::class)->nukeRequestCache();

        $data = [
            'name' => 'Test webhook updated',
            'httpMethod' => 'post',
            'url' => 'http://notapplicable.app',
            'signingKey' => '1234567',
            'subscriptionEvents' => config('webhooks.subscription_events'),
            'notificationEmails' => '<EMAIL>,<EMAIL>',
            'form' => (string) $form->slug,
            'fields' => $fields->map(fn(Field $field) => (string) $field->slug)->all(),
        ];

        $this->route('PUT', 'webhook.update', ['webhook' => $this->webhooks[0]->slug], $this->withInput($data))->assertSessionHasNoErrors();
        $this->assertRedirectedToRoute('webhook.index');
        $this->assertSame($form->id, Webhook::first()->formId);
        $this->assertTrue(Webhook::first()->fields()->pluck('id')->contains($fields->first()->id), 'Field not attached to webhook');
    }

    /**
     * @Then /^I should not be able to update that webhook$/
     */
    public function iShouldNotBeAbleToUpdateThatWebhook()
    {
        $this->route('PUT', 'webhook.update', ['webhook' => $this->webhooks[0]->slug], $this->withInput([]));

        $this->assertResponseStatus(302);
        $this->assertSessionHasErrors(['error' => 'You do not have the right permissions to perform this action.']);
    }

    /**
     * @Then /^I should be able to delete a webhook$/
     */
    public function iShouldBeAbleToDeleteAWebhook()
    {
        $this->route('DELETE', 'webhook.delete', [], $this->withInput(['selected' => [$this->webhooks[0]->id]]));

        $this->assertRedirectedToRoute('webhook.index');
    }

    /**
     * @Then /^I should not be able to delete a webhook$/
     */
    public function iShouldNotBeAbleToDeleteAWebhook()
    {
        $this->route('DELETE', 'webhook.delete', [], $this->withInput(['selected' => [$this->webhooks[0]->id]]));

        $this->assertResponseStatus(302);
        $this->assertSessionHasErrors(['error' => 'You do not have the right permissions to perform this action.']);
    }
}
