<?php

return [

    'images' => [

        'max_file_size' => 512 * 1024 * 1024,

        'thumbnail' => [
            'w' => 50,
            'h' => 50,
            'fit' => 'crop',
            'crop' => 'faces',
        ],

        'header' => [
            'w' => 580,
            'h' => 400,
            'fit' => 'max',
        ],

        'footer' => [
            'w' => 580,
            'h' => 400,
            'fit' => 'max',
        ],

        'card' => [
            'w' => 240,
            'max-h' => 360,
            'fit' => 'crop',
            'crop' => 'top',
        ],

        'gallery' => [
            'small' => [
                'w' => 800,
                'fit' => 'max',
            ],
            'large' => [
                'w' => 1920,
                'h' => 1080,
                'fit' => 'max',
            ],
        ],

        'email' => [
            'header' => [
                'w' => 580,
                'h' => 400,
                'fit' => 'max',
            ],
            'footer' => [
                'w' => 580,
                'h' => 400,
                'fit' => 'max',
            ],
        ],

        'pdf' => [
            'logo' => [
                'w' => 100,
                'h' => 100,
            ],
            'attachement' => [
                'w' => 215,
                'q' => 45,
                'fit' => 'clip',
                'auto' => 'compress,enhance',
            ],
            'header' => [
                'w' => 1920,
                'fit' => 'clip',
                'auto' => 'compress,enhance',
            ],
            'footer' => [
                'w' => 1920,
                'fit' => 'clip',
                'auto' => 'compress,enhance',
            ],
        ],

        'awards' => [
            'badge' => [
                'w' => 220,
                'h' => 100,
                'fit' => 'max',
            ],
        ],

        'contracts' => [
            'signature' => [
                'w' => 240,
                'h' => 100,
                'fit' => 'max',
            ],
        ],

        'holocron' => [
            'feature-upgrade' => [
                'standard' => [
                    'fit' => 'clip',
                    'w' => 400,
                    'h' => 400,
                ],
                'big' => [
                    'fit' => 'clip',
                    'w' => 1920,
                    'h' => 1080,
                ],
            ],
        ],

        'themes' => [
            'home_background_image' => [
                'w' => 1920,
                'fit' => 'max',
                'q' => 78,
            ],
            'home_logo' => [
                'h' => 'home-logo-height',
                'fit' => 'max',
            ],
            'home_logo_mobile' => [
                'h' => 'home-logo-height-mobile',
                'fit' => 'max',
            ],
            'favicon' => [
                'w' => 180,
                'q' => 90,
            ],
            'header_logo' => [
                'h' => 'header-logo-height',
                'fit' => 'max',
            ],
            'header_logo_mobile' => [
                'h' => 'header-logo-height-mobile',
                'fit' => 'max',
            ],
            'footer_logo' => [
                'h' => 'footer-logo-height',
                'fit' => 'max',
            ],
            'footer_logo_mobile' => [
                'h' => 'footer-logo-height-mobile',
                'fit' => 'max',
            ],
            'expiry' => (10 * 365 * 24 * 60), // 10 year in minutes
        ],

        'voting_card' => [
            'masonry' => [
                'w' => 240,
                'max-h' => 360,
                'fit' => 'crop',
                'crop' => 'top',
            ],
            'square' => [
                'w' => 240,
                'h' => 240,
                'max-h' => 360,
                'fit' => 'crop',
                'crop' => 'faces,edges',
            ],
        ],

        'forms' => [
            'cover_image' => [
                'w' => 800,
                'max-h' => 800,
                'fit' => 'clip',
            ],
        ],

        'home' => [
            'forms' => [
                'card' => [
                    'w' => 240,
                    'max-h' => 240,
                    'fit' => 'clip',
                ],
            ],
            'categories' => [
                'card' => [
                    'w' => 150,
                    'max-h' => 75,
                    'fit' => 'clip',
                ],
            ],
        ],

        'organisations' => [
            'small' => ['w' => 80, 'h' => 80],
            'large' => ['w' => 240, 'h' => 240],
        ],
    ],
];
