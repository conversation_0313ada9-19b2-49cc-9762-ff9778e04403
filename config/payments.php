<?php

/**
 * =======================================
 * ===== IMPORTANT - PLATFORM CONFIG =====
 * =======================================
 *
 * Please note, this config file is provided by platform!
 * Ensure all changes that apply to platform are made there.
 * Be careful publishing and merging updates from platform.
 */

return [

    // TODO: move this entry to a new config file OR into platform
    'cart' => [
        // Cart is forgotten after X minutes
        'expiry' => 60 * 24,

        // Seconds to consider an user with a recent payment
        'recent_payment_limit' => 30.,
    ],

    /**
     * Official list of supported payment gateways.
     */
    'payment_gateways' => [
        'authorize_net',
        'bluepay',
        'bpoint',
        'ccavenue',
        'eway',
        'eway_redirect',
        'nab_transact',
        'payflow',
        'paystack',
        'sagepay',
        'securepay',
        'stripe_connect',
        'westpac_payway',
    ],

    /**
     * List of deprecated (no longer supported) payment gateways.
     */
    'deprecated_payment_gateways' => [
        'cybersource',
    ],

    /**
     * Card types
     */
    'available_cards' => [
        'american_express',
        'cartes_bancaires',
        'diners_club',
        'discover',
        'jcb',
        'maestro',
        'mastercard',
        'visa',
    ],
    /**
     * Payment types that can optionally incur a processing fee
     */
    'optional_processing_fees' => [
        'alipay',
        'american_express',
        'diners_club',
        'discover',
        'ideal',
        'invoice',
        'jcb',
        'maestro',
        'mastercard',
        'paypal_express',
        'visa',
    ],

    'onsite_gateways' => [
        'authorize_net',
        'bluepay',
        'bpoint',
        'cybersource',
        'eway',
        'nab_transact',
        'payflow',
        'realex',
        'sagepay',
        'securepay',
        'stripe',
        'stripe_connect',
        'westpac_payway',
    ],

    'offsite_payment_methods' => [
        'alipay',
        'ideal',
        'invoice',
        'paypal_express',
    ],

    'custom_checkout' => [
        'cybersource',
        'eway',
        'securepay',
    ],
];
