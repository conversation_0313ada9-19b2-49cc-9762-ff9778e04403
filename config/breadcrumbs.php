<?php
/*
    |--------------------------------------------------------------------------
    | BreadcrumbResolver configuration
    |--------------------------------------------------------------------------
    |
    | The array keys represent the names of the routes that can be used as breadcrumbs overrides
    | and the values represent the translation keys that will be used for the breadcrumb override text
    |
    */
return [
    'entry.manager.index' => 'entries.titles.manager',
    'entry.entrant.index' => 'entries.titles.entrant',
    'leaderboard.index' => 'judging.titles.leaderboard',
    'contract.index' => 'contract.titles.main',
    'grant.manager.index' => 'grants.titles.manage',
    'funding.allocation.index' => 'funding.titles.allocation.list',
    'assignment.index' => 'assignments.titles.main',
    'review-flow.task.manage' => 'review-flow.titles.manage',
    //    'organisation.index' => 'organisations.titles.main',
    //    'my-organisation.index' => 'organisations.titles.main',
];
