<?php

return [

    'brand' => 'Award Force',

    // Domains of internal apps used by Award Force
    'domains' => [
        'auth' => env('AUTH_DOMAIN'),
        'my-awardforce' => env('MY_AWARDFORCE_DOMAIN'),
        'my-goodgrants' => env('MY_GOODGRANTS_DOMAIN'),
    ],

    'regions' => explode(',', env('AF_REGIONS')),

    'region' => env('AF_REGION', null),

    // Emails required by the application
    'emails' => [
        'support' => '',
    ],

    // Milestone dates for handling specific scenarios
    'milestones' => [
        // Date we started logging user activity, used to alert on user search for incomplete data.
        // If this date changes, update lang: users.messages.incomplete-results
        'activity-logging' => '2016-10-20',
    ],

    'datadome' => [
        'keys' => [
            'server' => env('DATADOME_SERVER_KEY'),
            'client' => env('DATADOME_CLIENT_KEY'),
        ],
    ],

    // PDF - wkhtmltopdf
    'pdf' => [
        'binary' => env('WKHTMLTOPDF', 'wkhtmltopdf'),
    ],

    // Path for bulk downloads to be built (relative to the storage dir)
    'bulk-downloads-path' => 'bulk',

    // Url settings. If you want Shift located at something like /admin/, for example, put 'admin'
    'url' => '',

    // Image types
    'extensions' => [
        'audio' => [
            'mp3',
            'm4a',
            'wav',
        ],
        'images' => [
            'gif',
            'jpeg',
            'jpg',
            'png',
            'tif',
            'tiff',
            'ico',
            'webp',
            'avif',
        ],
        'videos' => [
            'avi',
            'flv',
            'm4v',
            'mov',
            'mp4',
            'mpeg',
            'mpeg4',
            'mpg',
            'ogm',
            'ogx',
            'swf',
            'wmv',
        ],
        'thumbnails' => [
            'gif',
            'jpeg',
            'jpg',
            'png',
            'pdf',
            'tif',
            'tiff',
            'webp',
            'avif',
            'avi',
            'flv',
            'm4v',
            'mov',
            'mp4',
            'mpeg',
            'mpeg4',
            'mpg',
            'ogm',
            'ogx',
            'swf',
            'wmv',
        ],
    ],

    // Content blocks
    'content-blocks' => [
        //'key'                         => 'type'
        'home-program-description' => ['type' => 'standard', 'enable-roles-based' => false],
        'entrant-home' => ['type' => 'info-box', 'enable-roles-based' => true],
        'judges-home' => ['type' => 'info-box', 'enable-roles-based' => true],
        'voting-home' => ['type' => 'info-box', 'enable-roles-based' => true],
        'qualifying-home' => ['type' => 'info-box', 'enable-roles-based' => true],
        'top-pick-home' => ['type' => 'info-box', 'enable-roles-based' => true],
        'entry-view' => ['type' => 'info-box', 'enable-roles-based' => true],
        'cart-info' => ['type' => 'info-box', 'enable-roles-based' => true],
        'cart-footer' => ['type' => 'info-box', 'enable-roles-based' => true],
        'judges-confidentiality-agreement' => ['type' => 'standard', 'enable-roles-based' => false],
        'tab-info' => ['type' => 'info-box', 'enable-roles-based' => true],
        'submission-completed' => ['type' => 'standard', 'enable-roles-based' => true],
        'judging-comments' => ['type' => 'standard', 'enable-roles-based' => true],
        'role-registration-form' => ['type' => 'standard', 'enable-roles-based' => false],
        'home-header-info-box' => ['type' => 'standard', 'enable-roles-based' => false],
        'gallery-info' => ['type' => 'info-box', 'enable-roles-based' => true],
        'role-registration-completed' => ['type' => 'standard', 'enable-roles-based' => false],
        'review-flow-page' => ['type' => 'standard', 'enable-roles-based' => true],
        'email-footer' => ['type' => 'standard', 'enable-roles-based' => true],
        'review-stage-completed' => ['type' => 'standard', 'enable-roles-based' => true],
        'about-page' => ['type' => 'standard', 'enable-roles-based' => true],
        'cookie-notice' => ['type' => 'standard', 'enable-roles-based' => false],
        'profile-preferences-info-box' => ['type' => 'info-box', 'enable-roles-based' => true],
        'unsubscribe-confirmation' => ['type' => 'standard', 'enable-roles-based' => true],
        'contract' => ['type' => 'standard', 'enable-roles-based' => false],
        'entry-eligible' => ['type' => 'standard', 'enable-roles-based' => true],
        'entry-ineligible' => ['type' => 'standard', 'enable-roles-based' => true],
        'judge-dashboard' => ['type' => 'info-box', 'enable-roles-based' => true],
        'gallery-dashboard' => ['type' => 'info-box', 'enable-roles-based' => true],
        'blank-entry-pdf-info' => ['type' => 'info-box', 'enable-roles-based' => true],
        'home-category-promotion' => ['type' => 'standard', 'enable-roles-based' => true],
        'entry-page-info-box' => ['type' => 'info-box', 'enable-roles-based' => false],
    ],

    'seedable-content-blocks' => [
        'home-program-description',
        'entrant-home',
        'judges-confidentiality-agreement',
        'judges-home',
        'submission-completed',
        'tab-info',
        'cookie-notice',
        'entry-eligible',
        'entry-ineligible',
    ],

    'feature-based-content-blocks' => [
        // content-block => feature
        'contract' => 'contracts',
        'entry-eligible' => 'eligibility',
        'entry-ineligible' => 'eligibility',
    ],

    // Judging types
    'judging' => [
        'modes' => [
            'qualifying',
            'top_pick',
            'vip_judging',
            'voting',
            'gallery',
        ],
    ],

    'assignments' => [
        'status' => [
            'none',
            'in_progress',
            'complete',
            'abstained',
        ],
    ],

    // The following array represents a collection of replaceable translatable keys
    'lang' => [

    ],

    // Settings - and their default values
    'settings' => [
        'general' => [
            // General
            'app-site-name' => 'AwardForce',
            'app-site-description' => '',
            'app-site-registration' => true,
            'app-site-custom_theming' => false,
            'app-site-google_analytics_code' => '',
            'app-site-registration-open' => true,

            // File
            'app-file-types_permitted' => 'png,jpg,jpeg,gif,ico,pdf,docx,doc,ppt,pptx,xls,xlsx,mp3,m4a,mov',
            'app-file-max_size' => '10MB',
            'accept-attachment-links' => true,
            'max-filesize' => 5,

            // Entry Specific
            'app-awardforce-date_details' => '',
            'app-awardforce-support_details' => '',
            'app-awardforce-entry_teams' => false,
            'app-awardforce-category_selector' => false,
            'app-awardforce-lock_entries_on_submission' => false,

            // Attachments
            'max-attachments' => 10,
            'attachment-allowed-file-types' => '{"documents":["pdf"],"images":["gif","jpeg","jpg","png"],"audio":["mp3"]}',

            // Registration
            'enable-mobile-registrations' => false,

            // Social sharing
            'sharing-active' => true,

            // Social authentication
            'social-authentication' => 'google,facebook,twitter',

            // Payments
            'payment-identifier' => 'AWARD FORCE',
            'display-state-in-cart' => true,
            'payment-gateway-description' => '',

            // Consent
            'require-agreement-to-terms' => true,
            'require-consent-to-notifications-and-broadcasts' => true,
            'custom-agreement-to-terms' => false,
            'custom-consent-to-notifications-and-broadcasts' => false,
            'request-consent-to-cookies' => true,
        ],
    ],

    // Encrypted Settings
    'encrypted-settings' => [
        'authorize-net-api-login-id',
        'authorize-net-transaction-key',
        'bluepay-account-id',
        'bluepay-secret-key',
        'bpoint-api-password',
        'bpoint-api-username',
        'bpoint-merchant-number',
        'ccavenue-access-code',
        'ccavenue-key',
        'ccavenue-merchant-id',
        'cybersource-api-identifier',
        'cybersource-api-key',
        'cybersource-merchant-id',
        'cybersource-org-unit-id',
        'cybersource-transaction-key',
        'eway-api-key',
        'eway-password',
        'eway-redirect-api-key',
        'eway-redirect-password',
        'first-data-shared-secret',
        'first-data-store-id',
        'mercanet-merchant-id',
        'mercanet-secret-key',
        'nab-transact-merchant-id',
        'nab-transact-password',
        'netbanx-account-number',
        'netbanx-store-id',
        'netbanx-store-password',
        'payflow-partner',
        'payflow-password',
        'payflow-username',
        'payflow-vendor',
        'paystack-merchant-email',
        'paystack-public-key',
        'paystack-secret-key',
        'paypal-api-password',
        'paypal-api-signature',
        'paypal-api-username',
        'paypal-express-password',
        'paypal-express-signature',
        'paypal-express-username',
        'realex-account',
        'realex-merchant-id',
        'realex-shared-secret',
        'sagepay-referrer-id',
        'sagepay-vendor',
        'securepay-merchant-id',
        'securepay-transaction-password',
        'sharing-shareaholic-app-id',
        'sharing-shareaholic-key',
        'stripe-api-key',
        'stripe-connect-client-id',
        'stripe-connect-user-id',
        'twilio-api-key',
        'twilio-api-secret',
        'westpac-payway-merchant-id',
        'westpac-payway-password',
        'westpac-payway-username',
        'worldpay-installation-id',
        'saml-issuer',
        'saml-sso-service',
        'saml-certificate',
        'saml-certificate-private-key',
    ],

    'masked-settings' => [
        'bpoint-api-password',
        'eway-password',
        'eway-redirect-password',
        'nab-transact-password',
        'netbanx-store-password',
        'payflow-password',
        'paypal-api-password',
        'paypal-express-password',
        'securepay-transaction-password',
        'westpac-payway-password',
    ],

    'partially-masked-settings' => [
        'authorize-net-transaction-key',
        'bluepay-secret-key',
        'bpoint-merchant-number',
        'ccavenue-access-code',
        'ccavenue-key',
        'cybersource-api-key',
        'cybersource-transaction-key',
        'eway-api-key',
        'eway-redirect-api-key',
        'first-data-shared-secret',
        'mercanet-secret-key',
        'paystack-public-key',
        'paystack-secret-key',
        'paypal-api-signature',
        'paypal-express-signature',
        'realex-shared-secret',
        'sharing-shareaholic-key',
        'stripe-api-key',
        'twilio-api-key',
        'saml-certificate-private-key',
    ],

    'theme' => [
        'colours' => [
            'general' => [
                'app-header-background',
                'app-header-background-text',
                'text',
                'link',
                'link-hover',
                'link-active',
                'info-box-background',
                'info-box-heading',
                'info-box-icon',
                'info-box-text',
                'alert-box-success',
                'alert-box-warning',
                'alert-box-info',
                'alert-box-error',
                'alert-box-text',
                'form-box-background',
                'form-box-text',
                'focus-box-background',
                'focus-box-text',
                'home-background',
            ],

            'buttons' => [
                'primary-button',
                'primary-button-text',
                'primary-button-hover',
                'primary-button-hover-text',
                'secondary-button',
                'secondary-button-text',
                'secondary-button-hover',
                'secondary-button-hover-text',
                'tertiary-button',
                'tertiary-button-text',
                'tertiary-button-hover',
                'tertiary-button-hover-text',
            ],

            'tabs' => [
                'tab',
                'tab-text',
                'tab-active',
                'tab-active-text',
                'tab-hover',
                'tab-hover-text',
            ],

            'navigation' => [
                'menu-background',
                'menu-text',
                'menu-hover-background',
                'menu-hover-text',
                'menu-active-background',
                'menu-active-text',
                'tray-background',
                'tray-text',
                'tray-link',
                'tray-link-hover',
                'tray-link-active',
            ],
        ],

        'files' => [
            'footer-background-image',
            'footer-background-image-mobile',
            'footer-logo',
            'footer-logo-mobile',
            'header-background-image',
            'header-background-image-mobile',
            'header-logo',
            'header-logo-mobile',
            'home-logo',
            'home-logo-mobile',
            'home-background-image',
            'favicon',
            'pdf-header-image',
            'pdf-footer-image',
            'email-header-image',
            'email-footer-image',
        ],

        'alignment' => [
            'horizontal' => [
                'footer-logo-horizontal-alignment',
                'header-logo-horizontal-alignment',
                'home-logo-horizontal-alignment',
            ],
        ],

        'sizes' => [
            'home-header-height',

            'home-header-height',
            'home-logo-height',
            'home-logo-height-mobile',
            'home-header-height-mobile',

            'header-height',
            'header-logo-height',
            'header-logo-height-mobile',
            'header-height-mobile',

            'footer-height',
            'footer-height-mobile',
            'footer-logo-height',
            'footer-logo-height-mobile',
        ],

        'links' => [
            'header-link',
            'footer-link',
        ],

        'pdf' => [
            'pdf-header-image-placement',
            'pdf-footer-image-placement',
        ],

        'seed' => [
            'include-footer-on-home' => '1',
        ],

        'default-seeded-images' => [
            'header-logo' => 'default-client-logo-160.png',
            'header-logo-mobile' => 'default-client-logo-120.png',
            'home-logo' => 'default-client-logo-160.png',
            'home-logo-mobile' => 'default-client-logo-120.png',
        ],
    ],

    // Text displayed in the footer next to Award Force logo
    'footer_links' => [
        'Awards management system by Award Force' => 'https://www.awardforce.com/',
        'Awards judging system by Award Force' => 'https://www.awardforce.com/features/judging-suite-features/',
        'Awards system by Award Force' => 'https://www.awardforce.com/',
        'Awards software by Award Force' => 'https://www.awardforce.com/',
        'Awards platform by Award Force' => 'https://www.awardforce.com/',
        'Online nomination software by Award Force' => 'https://www.awardforce.com/awards-software/',
        'Online nomination system by Award Force' => 'https://www.awardforce.com/awards-software/',
        'Online awards software by Award Force' => 'https://www.awardforce.com/awards-software/',
        'Online judging system by Award Force' => 'https://www.awardforce.com/features/judging-suite-features/',
        'Contest management system by Award Force' => 'https://www.awardforce.com/',
        'Competition management software by Award Force' => 'https://www.awardforce.com/',
        'Competition management system by Award Force' => 'https://www.awardforce.com/',
    ],

    /**
     * Official list of supported resources and types for Tabs and Fields.
     *
     * When changing, these places need to be updated too:
     * -> ./app/Modules/Fields/Models/Tab.php   (class constants)
     * -> ./resources/lang/-/tabs.php           (language strings)
     */
    'tabs' => [
        'resources' => [
            'Entries',
            'Users',
        ],
        'types' => [
            'Details',
            'Attachments',
            'Contributors',
            'Fields',
            'Eligibility',
            'Referees',
        ],
    ],

    /**
     * Enabled social authentication providers for settings and register/login buttons
     */
    'social-auth' => [
        'settings' => [
            'facebook',
            'twitter',
            'wordpress',
            'google',
            'linkedin',
        ],
        'registration' => [
            'facebook',
            'twitter',
            'google',
            'linkedin',
        ],
        'logos' => [
            'google' => 'img/brand/new_google_logo.png',
        ],
    ],

    /**
     * Field
     */
    'fields' => [
        'resources' => [
            'Entries',
            'Users',
            'Attachments',
            'Contributors',
            'Referees',
            //            'Organisations',
        ],

        'types' => [
            'checkbox',
            'checkboxlist',
            'content',
            'country',
            'currency',
            'date',
            'datetime',
            'email',
            'file',
            'formula',
            'numeric',
            'phone',
            'radio',
            'drop-down-list',
            'table',
            'text',
            'textarea',
            'time',
            'url',
        ],

        'compatibility' => [
            'checkbox' => false,
            'checkboxlist' => false,
            'content' => false,
            'currency' => false,
            'date' => false,
            'datetime' => false,
            'file' => false,
            'formula' => 'string',
            'table' => false,
            'country' => 'string',
            'email' => 'string',
            'numeric' => 'string',
            'phone' => 'string',
            'radio' => 'string',
            'drop-down-list' => 'string',
            'text' => 'string',
            'textarea' => 'string',
            'time' => false,
            'url' => 'string',
        ],

        'conditional_fields' => [
            'checkbox',
            'checkboxlist',
            'country',
            'currency',
            'email',
            'file',
            'formula',
            'numeric',
            'phone',
            'radio',
            'drop-down-list',
            'text',
            'textarea',
            'time',
            'url',
        ],

        'formula_field_compatible' => [
            'checkbox',
            'checkboxlist',
            'country',
            'currency',
            'date',
            'datetime',
            'email',
            'file',
            'numeric',
            'phone',
            'radio',
            'drop-down-list',
            'text',
            'textarea',
            'time',
            'url',
        ],

        'file' => [
            'types' => [
                'doc',
                'docx',
                'eps',
                'jpeg',
                'jpg',
                'mp3',
                'pdf',
                'png',
                'xls',
                'xlsx',
            ],
        ],

        'options_required' => [
            'checkboxlist',
            'drop-down-list',
            'radio',
        ],

        'video' => [
            'formats' => [
                '3gp',
                'aac',
                'avi',
                'flv',
                'mp4',
                'mpg',
                'mpeg',
                'm2v',
                'mp2',
                'mov',
            ],
        ],

        'conditionals' => [
            'is' => 'is',
            'is_not' => 'is not',
            'empty' => 'empty',
            'is_not_empty' => 'is not empty',
            'is_checked' => 'is checked',
            'is_not_checked' => 'is not checked',
            'is_any_of' => 'is any of',
            'is_not_any_of' => 'is not any of',
            'is_greater_than' => 'is greater than',
            'is_less_than' => 'is less than',
            'contains' => 'contains',
            'does_not_contain' => 'does not contain',
            'starts_with' => 'starts with',
            'does_not_start_with' => 'does not start with',
            'ends_with' => 'ends with',
            'does_not_end_with' => 'does not end with',
        ],

        'searchable' => [
            'checkbox',
            'country',
            'currency',
            'email',
            'numeric',
            'phone',
            'radio',
            'drop-down-list',
            'text',
            'textarea',
            'url',
        ],
    ],

    'active-filters' => [
        'assignment_status',
        'active-in-season',
        'assignment-method',
        'badge',
        'category',
        'chapter',
        'conditional',
        'confirmation',
        'decision',
        'entries',
        'fund',
        'ip',
        'judge',
        'judging-mode',
        'keywords',
        'location',
        'language',
        'mode',
        'moderation',
        'panel',
        'parent',
        'payment_method',
        'payment_status',
        'plagiarism-scan-status',
        'price',
        'protection',
        'resource',
        'review-stage',
        'review_status',
        'reviewer',
        'role',
        'score-set',
        'season_joined',
        'starred',
        'state',
        'status',
        'tab',
        'tab_type',
        'tag',
        'trigger',
        'type',
        'entrant',
        'http_method',
        'subscription_event',
        'entry',
        'contract_template',
        'round',
        'grant_status',
        'user',
        'eligibility_status',
        'before',
        'after',
        'slug',
    ],

    'translation-replacements' => [
        'brand' => 'Award Force',
        'company_url' => 'https://www.awardforce.com',
        'creative_force_url' => 'https://www.creativeforce.team',
    ],

    'trial' => [
        'user-limit' => 20,
        'file-size-limit' => 50,
    ],
    'mail' => [
        'from' => ['address' => '<EMAIL>', 'name' => 'Award Force'],
        'reply_to' => ['address' => '<EMAIL>', 'name' => 'Award Force'],
        'sender' => ['address' => '<EMAIL>', 'name' => 'Award Force'],
    ],
    'billing' => [
        'accounts_email' => '<EMAIL>',
    ],

    'default-user-colors' => [
        '#ff8f8f',
        '#ff83d6',
        '#f42295',
        '#b83dea',
        '#5e3de8',
        '#2c62f4',
        '#00b4ff',
        '#008eaf',
        '#00d2ae',
        '#a7d500',
        '#15a900',
        '#ffd112',
        '#ff941f',
        '#ff4b28',
        '#c7130a',
    ],

    'max_assignments_per_score_set' => env('MAX_ASSIGNMENTS_PER_SCORE_SET', 200000),

    'max_assignments_per_score_set_recalculation' => env('MAX_ASSIGNMENTS_PER_SCORE_SET_RECALCULATION', 5000),

    'marketplace' => [
        'connection_key' => 'Award Force Connection',
    ],

    'throttle_requests' => env('THROTTLE_REQUESTS', true),

    'identifier' => [
        'worker_id' => env('WORKER_ID', 1),
        'process_id' => env('PROCESS_ID', 1),
    ],
];
