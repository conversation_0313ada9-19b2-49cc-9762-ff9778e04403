<?php
/**
 * Translatable Terms!
 *
 * IMPORTANT:
 *   Terms are currently replaced using `str_replace()`, this means that substr
 *   collisions are a very real possibility... To avoid this, please ensure
 *   that you order terms so substr matches are after the full string.
 *   I.e. `invoice` after `invoice_prefix`, so `invoice_prefix` is matched first.
 */
return [
    'visible' => [
        'allocation_payment',
        'allocation',
        'attachment',
        'category',
        'chapter',
        'contributor',
        'enter',
        'entrant',
        'entry',
        'fail',
        'fund',
        'gallery',
        'grant_report',
        'grant',
        'invoice_prefix',
        'invoice',
        'judged',
        'judging',
        'organisation',
        'pass',
        'postcode',
        'qualifying',
        'referee',
        'region',
        'reviewer',
        'review',
        'selection',
        'role',
        'state',
        'top_pick',
        'unsure',
        'vip_judging',
        'voting',
    ],

    'internal' => [
        'action_flow_term',
        'email',
        'role_chapter_manager',
        'vertical',
        'role',
    ],

    'ignore' => [
        'awards' => [
            'grant_report',
        ],
        'grants' => [

        ],
    ],

    'vertical-translations-key' => [
        'awards' => 'defaults',
        'grants' => 'grants',
    ],
];
