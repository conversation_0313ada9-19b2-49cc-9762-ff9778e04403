<?php

return [
    'columnators' => [
        \AwardForce\Modules\AllocationPayments\Search\AllocationPaymentColumnator::class,
        \AwardForce\Modules\Assignments\Search\AssignmentsColumnator::class,
        \AwardForce\Modules\Audit\Search\EventLogsColumnator::class,
        \AwardForce\Modules\Accounts\Search\AccountsColumnator::class,
        \AwardForce\Modules\Accounts\Search\SuspendedAccountsColumnator::class,
        \AwardForce\Modules\Categories\Search\CategoryColumnator::class,
        \AwardForce\Modules\Chapters\Search\ChapterColumnator::class,
        \AwardForce\Modules\Content\Blocks\Search\ContentBlockColumnator::class,
        \AwardForce\Modules\Dashboard\Reports\CategoryEntries\Search\CategoryEntriesColumnator::class,
        \AwardForce\Modules\Dashboard\Reports\CategoryEntries\Search\CreatedVolumeColumnator::class,
        \AwardForce\Modules\Dashboard\Reports\CategoryEntries\Search\SubmittedVolumeColumnator::class,
        \AwardForce\Modules\Ecommerce\Cart\Search\CartColumnator::class,
        \AwardForce\Modules\Ecommerce\Orders\Search\OrderColumnator::class,
        \AwardForce\Modules\Ecommerce\Orders\Search\OrderItemsColumnator::class,
        \AwardForce\Modules\Entries\Search\AttachmentsColumnator::class,
        \AwardForce\Modules\Entries\Search\ApiAttachmentsColumnator::class,
        \AwardForce\Modules\Entries\Search\ApiContributorsColumnator::class,
        \AwardForce\Modules\Entries\Search\ContributorsColumnator::class,
        \AwardForce\Modules\Entries\Search\ManageEntriesColumnator::class,
        \AwardForce\Modules\Entries\Search\ManageDuplicatesColumnator::class,
        \AwardForce\Modules\Entries\Search\MyEntriesColumnator::class,
        \AwardForce\Modules\Entries\Search\FileUploadsColumnator::class,
        \AwardForce\Modules\Entries\Search\ApiFileColumnator::class,
        \AwardForce\Modules\Entries\Search\ApiSimpleFileColumnator::class,
        \AwardForce\Modules\Entries\Search\UserEntriesColumnator::class,
        \AwardForce\Modules\Documents\Search\DocumentColumnator::class,
        \AwardForce\Modules\Documents\Search\MyDocumentsColumnator::class,
        \AwardForce\Modules\Documents\Search\UserViewDocumentColumnator::class,
        \AwardForce\Modules\DocumentTemplates\Search\DocumentTemplateColumnator::class,
        \AwardForce\Modules\Downloads\Search\DownloadColumnator::class,
        \AwardForce\Modules\Forms\Fields\Search\FieldsColumnator::class,
        \AwardForce\Modules\ScoreSets\Search\ScoreSetsColumnator::class,
        \AwardForce\Modules\ScoreSets\Search\ScoreSetCommentsColumnator::class,
        \AwardForce\Modules\Identity\Users\Search\UserColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\QualifyingColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\QualifyingDecisionsColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\TopPickColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\TopPickPicksColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\VipJudgingColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\VipJudgingIndividualScoresColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\VipJudgingScoresColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\VipJudgingScoreSummaryColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\VotingColumnator::class,
        \AwardForce\Modules\Judging\Search\Leaderboard\VotingVotesColumnator::class,
        \AwardForce\Modules\Judging\Search\Progress\QualifyingColumnator::class,
        \AwardForce\Modules\Judging\Search\Progress\TopPickColumnator::class,
        \AwardForce\Modules\Judging\Search\Progress\VipJudgingColumnator::class,
        \AwardForce\Modules\Judging\Search\Progress\VotingColumnator::class,
        \AwardForce\Modules\Panels\Search\PanelColumnator::class,
        \AwardForce\Modules\Reports\Search\ReportColumnator::class,
        \AwardForce\Modules\ReviewFlow\Search\ManageReviewTasksColumnator::class,
        \AwardForce\Modules\ScoringCriteria\Search\ScoringCriteriaColumnator::class,
        \AwardForce\Modules\Funding\Search\AllocationColumnator::class,
        \AwardForce\Modules\Funding\Search\FundColumnator::class,
        \AwardForce\Modules\Forms\Tabs\Search\TabsColumnator::class,
        \AwardForce\Modules\Seasons\Search\SeasonColumnator::class,
        \AwardForce\Modules\Judging\Search\JudgingColumnator::class,
        \AwardForce\Modules\Judging\Search\ScoreMatrixColumnator::class,
        \AwardForce\Modules\Webhooks\Search\WebhookColumnator::class,
        \AwardForce\Modules\Contract\Search\ContractColumnator::class,
        \AwardForce\Modules\Forms\Forms\Search\FormColumnator::class,
        \AwardForce\Modules\Forms\Forms\Search\FormApiColumnator::class,
        \AwardForce\Modules\Notifications\Search\NotificationColumnator::class,
        \AwardForce\Modules\Integrations\Search\IntegrationColumnator::class,
        \AwardForce\Modules\ReviewFlow\Search\ReviewStageColumnator::class,
        \AwardForce\Modules\Rounds\Search\RoundColumnator::class,
        \AwardForce\Modules\Tags\Search\TagColumnator::class,
        \AwardForce\Modules\Awards\Search\AwardsColumnator::class,
        \AwardForce\Modules\Identity\Roles\Search\RoleColumnator::class,
        \AwardForce\Modules\Seasons\Search\SeasonColumnator::class,
        \AwardForce\Modules\Broadcasts\Search\BroadcastColumnator::class,
        \AwardForce\Modules\Exports\Search\ExportLayoutColumnator::class,
        \AwardForce\Modules\Grants\Search\GrantStatus\GrantStatusColumnator::class,
        \AwardForce\Modules\Grants\Search\ManageGrantsColumnator::class,
        \AwardForce\Modules\GrantReports\Search\MyGrantReportsColumnator::class,
        \AwardForce\Modules\GrantReports\Search\ManageGrantReportsColumnator::class,
        \AwardForce\Modules\PaymentMethods\Search\PaymentMethodColumnator::class,
        \AwardForce\Modules\PaymentScheduleTemplates\Search\PaymentScheduleTemplateColumnator::class,
        \AwardForce\Modules\Organisations\Organisations\Search\OrganisationColumnator::class,
        \AwardForce\Modules\Organisations\Organisations\Search\MyOrganisationColumnator::class,
        \AwardForce\Modules\Entries\Search\EntryReviewTasksColumnator::class,
        \AwardForce\Modules\Payments\Search\TaxColumnator::class,
        \AwardForce\Modules\Forms\Collaboration\Search\EntryCollaboratorsColumnator::class,
        \AwardForce\Modules\Forms\Collaboration\Search\GrantReportCollaboratorsColumnator::class,
        \AwardForce\Modules\Grants\Search\UserGrantsColumnator::class,
    ],

    // Areas not available for selection in the Custom export layouts
    'excluded-areas' => [
        'attachments.export',
        'event_logs.export', // audit logs
        'accounts.export',
        'document_template.export',
        'document.my.export',
        'document.export',
        'document.user.export',
        'downloads.export',
        'entry_review_tasks.export',
        'round.export',
        'suspended-accounts.export',
        'taxes.export',
    ],

    'non-exportable' => [
        'action-overflow',
        'assignments.action_overflow',
        'assignments.comments',
        'assignments.judge',
        'assignments.marker',
        'columnator',
        'fields.category_count',
        'manage_entries.feedback',
        'manage_entries.status',
        'manage_entries.thumbnail',
        'manage_review_tasks.entry_thumbnail',
        'marker',
        'orders.comments',
        'user.action-overflow',
        'vip_judging_leaderboard.rank',
        'vip_judging_leaderboard.score',
        'vip_judging_leaderboard.status',
        'voting_leaderboard.rank',
        'user.avatar',
        'progress.avatar',
    ],

    'default-exports' => [
        'allocations' => [
            'allocations.export',
        ],
        'allocation-payments' => [
            'allocation-payments.export',
        ],
        'assignments' => [
            'assignments.export',
        ],
        'attachments' => [
            'attachments.export',
        ],
        'categories' => [
            'categories.export',
        ],
        'category_entries_report' => [
            'category_entries_report.export',
        ],
        'chapter' => [
            'chapter.export',
        ],
        'content' => [
            'content.export',
        ],
        'contributors' => [
            'contributors.export',
        ],
        'event_logs' => [
            'event_logs.export',
        ],
        'fields' => [
            'fields.export',
        ],
        'files' => [
            'files.export',
        ],
        'funds' => [
            'funds.export',
        ],
        'manage_entries' => [
            'manage_entries.export',
        ],
        'manage_grant_reports' => [
            'manage_grant_reports.export',
        ],
        'manage_review_tasks' => [
            'manage_review_tasks.export',
        ],
        'orders' => [
            'orders.export',
            'order-items.export',
        ],
        'panels' => [
            'panels.export',
        ],
        'qualifying_decisions' => [
            'qualifying_decisions.export',
        ],
        'qualifying_leaderboard' => [
            'qualifying_leaderboard.export',
        ],
        'qualifying_progress' => [
            'qualifying_progress.export',
        ],
        'score_sets' => [
            'score_sets.export',
        ],
        'score_set_comments' => [
            'score_set_comments.export',
        ],
        'scoring_criteria' => [
            'scoring_criteria.export',
        ],
        'tabs' => [
            'tabs.export',
        ],
        'top_pick_leaderboard' => [
            'top_pick_leaderboard.export',
        ],
        'top_pick_picks' => [
            'top_pick_picks.export',
        ],
        'top_pick_progress' => [
            'top_pick_progress.export',
        ],
        'users' => [
            'users.export',
        ],
        'vip_judging_individual_scores' => [
            'vip_judging_individual_scores.export',
        ],
        'vip_judging_leaderboard' => [
            'vip_judging_leaderboard.export',
        ],
        'vip_judging_progress' => [
            'vip_judging_progress.export',
        ],
        'vip_judging_scores' => [
            'vip_judging_scores.export',
        ],
        'voting_leaderboard' => [
            'voting_leaderboard.export',
        ],
        'voting_progress' => [
            'voting_progress.export',
        ],
        'voting_votes' => [
            'voting_votes.export',
        ],
        'contracts' => [
            'contracts.export',
        ],
        'manage_grants' => [
            'manage_grants.export',
        ],
    ],

    'exporter' => [
        'export_limit' => env('EXPORT_LIMIT', 500),
    ],

    'join_files' => [
        'files.export' => [
            'files.export',
            'attachments.export',
        ],
    ],
];
