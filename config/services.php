<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'aws' => [
        'kms' => [
            'maximum' => env('KMS_KEY_MAXIMUM'),
            'region' => env('KMS_REGION'),
        ],

        'cloudfront' => array_merge([
            'assets' => [
                'domain' => env('CLOUDFRONT_DOMAIN_ASSETS'),
            ],
        ], collect(explode(',', env('AF_REGIONS')))->mapWithKeys(function ($region) {
            $uppercaseRegion = strtoupper($region);

            return [
                $region => [
                    'domain' => env("CLOUDFRONT_DOMAIN_{$uppercaseRegion}"),
                    'private_key_path' => env('CLOUDFRONT_PRIVATE_KEY_PATH'),
                    'key_pair_id' => env('CLOUDFRONT_KEY_PAIR_ID'),
                ],
            ];
        })->all()),

        'media_convert' => collect(explode(',', env('AF_REGIONS')))->mapWithKeys(function ($region) {
            $uppercaseRegion = strtoupper($region);

            return [
                $region => [
                    'template' => env("MEDIA_CONVERT_TEMPLATE_{$uppercaseRegion}"),
                    'region' => env("MEDIA_CONVERT_REGION_{$uppercaseRegion}", env("S3_REGION_{$uppercaseRegion}")),
                    'bucket' => 's3://'.env("S3_BUCKET_{$uppercaseRegion}"),
                    'version' => env("MEDIA_CONVERT_VERSION_{$uppercaseRegion}", '2017-08-29'),
                    'endpoint' => env("MEDIA_CONVERT_ENDPOINT_{$uppercaseRegion}"),
                    'role' => env("MEDIA_CONVERT_ROLE_{$uppercaseRegion}"),
                ],
            ];
        })->all(),
    ],

    'consul' => [
        'host' => env('CONSUL_HOST'),
    ],

    'elevio' => [
        'awardforce' => [
            'account_id' => env('ELEVIO_AF_ACCOUNT_ID'),
            'support_module' => 'support',
        ],
        'goodgrants' => [
            'account_id' => env('ELEVIO_GG_ACCOUNT_ID'),
            'support_module' => '2',
        ],
    ],

    'facebook' => [
        'client_id' => env('AUTH_FACEBOOK_ID'),
        'client_secret' => env('AUTH_FACEBOOK_SECRET'),
        'redirect' => 'https://'.env('AUTH_DOMAIN').'/social/callback/facebook',
    ],

    'firebase' => [
        'apiKey' => env('FIREBASE_API_KEY'),
        'authDomain' => env('FIREBASE_AUTH_DOMAIN'),
        'databaseURL' => env('FIREBASE_DATABASE_URL'),
        'projectId' => env('FIREBASE_PROJECT_ID'),
        'storageBucket' => env('FIREBASE_STORAGE_BUCKET'),
        'messagingSenderId' => env('FIREBASE_MESSAGING_SENDERID'),
        'appId' => env('FIREBASE_APP_ID'),
        'measurementId' => env('FIREBASE_MEASUREMENT_ID'),
    ],

    'github' => [
        'client_id' => env('AUTH_GITHUB_ID'),
        'client_secret' => env('AUTH_GITHUB_SECRET'),
        'redirect' => 'https://'.env('AUTH_DOMAIN').'/social/callback/github',
    ],

    'google' => [
        'client_id' => env('AUTH_GOOGLE_ID'),
        'client_secret' => env('AUTH_GOOGLE_SECRET'),
        'redirect' => 'https://'.env('AUTH_DOMAIN').'/social/callback/google',
    ],

    'imgix' => collect(explode(',', env('AF_REGIONS')))->mapWithKeys(function ($region) {
        $uppercaseRegion = strtoupper($region);

        return [
            $region => [
                'domain' => env("IMGIX_DOMAIN_{$uppercaseRegion}"),
                'https' => env('IMGIX_HTTPS', false),
                'secure_key' => env("IMGIX_SECURE_KEY_{$uppercaseRegion}", null),
            ],
        ];
    })->merge([
        'holocron' => [
            'domain' => env('IMGIX_HOLOCRON_DOMAIN_IRELAND'),
            'https' => true,
            'secure_key' => env('IMGIX_HOLOCRON_SECURE_KEY_IRELAND'),
        ],
    ])->all(),

    'intercom' => [
        'awardforce' => [
            'app_id' => env('INTERCOM_AF_ID'),
            'key' => env('INTERCOM_AF_KEY'),
        ],
        'goodgrants' => [
            'app_id' => env('INTERCOM_GG_ID'),
            'key' => env('INTERCOM_GG_KEY'),
        ],
    ],

    'mailgun' => [
        'awardforce' => [
            'domain' => env('MAILGUN_AF_DOMAIN'),
            'secret' => env('MAILGUN_AF_KEY'),
        ],
        'goodgrants' => [
            'domain' => env('MAILGUN_GG_DOMAIN'),
            'secret' => env('MAILGUN_GG_KEY'),
        ],
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.eu.mailgun.net'),
        'scheme' => 'https',
    ],

    'mandrill' => [
        'secret' => '',
    ],

    'nexmo' => [
        'api_key' => env('NEXMO_KEY'),
        'api_secret' => env('NEXMO_SECRET'),
        'debug' => env('APP_DEBUG', false),
        'timeout' => env('NEXMO_TIMEOUT', 5.0),
        'usa_virtual_number' => env('NEXMO_NUMBER_USA'),
        'uae_default_sender' => env('NEXMO_DEFAULT_UAE', 'AD-CR4CE'),
        'uae_confirmation_sender' => env('NEXMO_CONFIRMATION_UAE', 'CR4CE'),
        'countries_with_anti_spam_legislation' => ['+971', '+61', '+44'],
        'countries_require_consent' => ['+971'],
    ],

    'onesky' => [
        'public_key' => env('ONESKY_KEY'),
        'secret_key' => env('ONESKY_SECRET'),
        'project_id' => 50920,
        'languages' => [
            'ar_AR' => 'ar',
            'cy_GB' => 'cy-GB',
            'de_DE' => 'de-DE',
            'en_GB' => 'en-GB',
            'es_NN' => 'es',
            'es_LA' => 'es-419',
            'fi_FI' => 'fi',
            'fr_FR' => 'fr-FR',
            'fr_CA' => 'fr-CA',
            'he_IL' => 'he',
            'it_IT' => 'it-IT',
            'ja_JP' => 'ja',
            'ko_KR' => 'ko',
            'lt_LT' => 'lt-LT',
            'nl_NL' => 'nl-NL',
            'pt_BR' => 'pt-BR',
            'ru_RU' => 'ru-RU',
            'sl_SI' => 'sl-SI',
            'th_TH' => 'th',
            'vi_VN' => 'vi',
            'zh_CN' => 'zh-Hans-CN',
            'zh_HK' => 'zh-HK',
        ],
    ],

    'salesforce' => [
        'client_id' => env('AUTH_SALESFORCE_ID'),
        'redirect_uri' => env('AUTH_SALESFORCE_REDIRECT'),
        'auth_endpoint' => env('AUTH_SALESFORCE_ENDPOINT'),
        'sandbox_auth_endpoint' => env('AUTH_SALESFORCE_SANDBOX_ENDPOINT', 'https://test.salesforce.com/services/oauth2/token'),
        'object_endpoint' => ':instance/services/data/v39.0/sobjects',
        'query_endpoint' => ':instance/services/data/v39.0/query',
    ],

    'ses' => [
        'key' => '',
        'secret' => '',
        'region' => 'us-east-1',
    ],

    'shareaholic' => [
        'key' => env('SHAREAHOLIC_KEY', '33111506'),
        'app_id' => env('SHAREAHOLIC_APP_ID', '650fcfdc35de88617ccbe32d1b66bd6e'),
    ],

    'stripe' => [
        'model' => 'User',
        'secret' => '',
        'api_version' => '2019-05-16',
    ],

    'stripe_connect' => [
        'api_key_test' => env('STRIPE_API_KEY_TEST'),
        'api_key_live' => env('STRIPE_API_KEY_LIVE'),
        'stripe_client_id' => env('STRIPE_CLIENT_ID'),
        'api_version' => '2019-05-16',
    ],

    'twitter' => [
        'client_id' => env('AUTH_TWITTER_ID', ''),
        'client_secret' => env('AUTH_TWITTER_SECRET', ''),
        'redirect' => 'https://'.env('AUTH_DOMAIN').'/social/callback/twitter',
    ],

    'url2png' => [
        'key' => env('URL2PNG_KEY'),
        'secret' => env('URL2PNG_SECRET'),
        'user_agent' => 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:79.0) Gecko/20100101 Firefox/79.0 CreativeForce/1.0',
        'blacklist' => [
            'dropbox.com',
            'instagram.com',
        ],
        'extra_options' => [
            'url2pngflag' => 'n57MuBc4maej',
        ],
    ],

    'virus_scan' => [
        'command' => env('VIRUS_SCAN_COMMAND'),
        'options' => env('VIRUS_SCAN_OPTIONS', '--no-summary'),
    ],

    'zendesk' => [
        'awardforce' => [
            'key' => env('ZENDESK_AF_KEY'),
        ],
        'goodgrants' => [
            'key' => env('ZENDESK_GG_KEY'),
        ],
    ],

    'hyper_formula' => [
        'license_key' => env('HYPER_FORMULA_LICENSE_KEY', 'gpl-v3'),
    ],

    'datadog' => [
        'frontend' => [
            'client_token' => env('DATADOG_FRONTEND_CLIENT_TOKEN', ''),
            'application_id' => env('DATADOG_FRONTEND_APPLICATION_ID', ''),
            'site' => env('DATADOG_FRONTEND_SITE', ''),
            'service' => env('DATADOG_FRONTEND_SERVICE', ''),
            'env' => env('DATADOG_FRONTEND_ENV', ''),
            'sample_rate' => env('DATADOG_FRONTEND_SAMPLE_RATE', 100),
            'replay_rate' => env('DATADOG_FRONTEND_REPLAY_RATE', 20),
            'track_user_interactions' => env('DATADOG_FRONTEND_TRACK_USER_INTERACTIONS', true),
        ],
    ],

    'twilio' => [
        'custom_sender' => env('TWILIO_CUSTOM_SENDER', 'CR4CE'),
        'account_sid' => env('TWILIO_ACCOUNT_SID', '1234'),
        'auth_token' => env('TWILIO_AUTH_TOKEN', '1234'),
        'service_id' => [
            'default' => env('TWILIO_DEFAULT_SERVICE_ID', '1234'),
            'custom' => env('TWILIO_CUSTOM_SERVICE_ID', '1234'),
            'verify' => env('TWILIO_VERIFY_SERVICE_ID', '1234'),
        ],
        'do_not_retry_error_codes' => explode(',', env('TWILIO_DO_NOT_RETRY_ERROR_CODES', '')),
        'api_key' => env('TWILIO_API_KEY', ''),
        'api_secret' => env('TWILIO_API_SECRET', ''),
    ],

    'heap' => [
        'app_id' => env('HEAP_APP_ID', ''),
    ],

    'announce_kit' => [
        'widget_url' => 'https://announcekit.app/widgets/v2/%s',
        'awardforce' => [
            'widget_id' => env('ANNOUNCEKIT_WIDGET_AF_ID', ''),
        ],
        'goodgrants' => [
            'widget_id' => env('ANNOUNCEKIT_WIDGET_GG_ID', ''),
        ],
    ],

    'prismatic' => [
        'organization_id' => env('PRISMATIC_ORGANIZATION_ID', ''),
        'signing_key' => env('PRISMATIC_SIGNING_KEY', ''),
        'awardforce_url' => env('PRISMATIC_AWARDFORCE_URL', ''),
        'goodgrants_url' => env('PRISMATIC_GOODGRANTS_URL', ''),
        'refresh_token' => env('PRISMATIC_REFRESH_TOKEN', ''),
        'api_base_url' => env('PRISMATIC_API_BASE_URL'),
    ],

    'dewatermark' => [
        'api_key' => env('DEWATERMARK_API_KEY'),
    ],

    'embeddable' => [
        'environment' => env('EMBEDDABLE_ENVIRONMENT'),
        'api_key' => env('EMBEDDABLE_API_KEY'),
        'base_url' => env('EMBEDDABLE_BASE_URL'),
        'dashboards' => [
            'awards' => [
                'programManager' => env('EMBEDDABLE_AF_PROGRAM_MANAGER_DASHBOARD_ID'),
                'chapterManager' => env('EMBEDDABLE_AF_CHAPTER_MANAGER_DASHBOARD_ID'),
            ],
            'grants' => [
                'programManager' => env('EMBEDDABLE_GG_PROGRAM_MANAGER_DASHBOARD_ID'),
                'chapterManager' => env('EMBEDDABLE_GG_CHAPTER_MANAGER_DASHBOARD_ID'),
            ],
        ],
    ],
];
