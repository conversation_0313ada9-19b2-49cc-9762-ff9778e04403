<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default filesystem disk that should be used
    | by the framework. The "local" disk, as well as a variety of cloud
    | based disks are available to your application. Just store away!
    |
    */

    'default' => 's3',

    /*
    |--------------------------------------------------------------------------
    | Default Cloud Filesystem Disk
    |--------------------------------------------------------------------------
    |
    | Many applications store files both locally and in the cloud. For this
    | reason, you may specify a default "cloud" driver here. This driver
    | will be bound as the Cloud disk implementation in the container.
    |
    */

    'cloud' => 's3',

    /*
    |--------------------------------------------------------------------------
    | Filesystem Disks
    |--------------------------------------------------------------------------
    |
    | Here you may configure as many filesystem "disks" as you wish, and you
    | may even configure multiple disks of the same driver. Defaults have
    | been set up for each driver as an example of the required values.
    |
    | Supported Drivers: "local", "ftp", "sftp", "s3"
    |
    */

    'disks' => array_merge([

        'local' => [
            'driver' => 'local',
            'root' => storage_path().'/app/',
            'throw' => true,
        ],

        'lang' => [
            'driver' => 'local',
            'root' => base_path().'/resources/lang/',
            'throw' => true,
        ],

        'resources' => [
            'driver' => 'local',
            'root' => base_path().'/resources/',
            'throw' => true,
        ],

        'public' => [
            'driver' => 'local',
            'root' => public_path().'/',
            'throw' => false,
        ],

        'test' => [
            'driver' => 'local',
            'root' => base_path().'/tests/storage/',
            'throw' => true,
        ],

        'tmp' => [
            'driver' => 'local',
            'root' => '/tmp/',
        ],

        'backups' => [
            'driver' => 's3',
            'region' => 'eu-central-1',
            'bucket' => 'tectonic-backups-all',
            'bucket_url' => 'https://tectonic-database-backups-af4.eu-central-1.amazonaws.com/',
            'throw' => true,
        ],

        'secure' => [
            'driver' => 's3',
            'region' => env('S3_REGION'),
            'bucket' => env('S3_SECURE_BUCKET'),
            'bucket_url' => 'https://'.env('S3_SECURE_BUCKET').'.s3-'.env('S3_REGION').'.amazonaws.com/',
            'throw' => true,
        ],

        'rackspace' => [
            'driver' => 'rackspace',
            'username' => 'your-username',
            'key' => 'your-key',
            'container' => 'your-container',
            'endpoint' => 'https://identity.api.rackspacecloud.com/v2.0/',
            'region' => 'IAD',
        ],

    ],
        collect(explode(',', env('AF_REGIONS')))->map(function ($region) {
            $uppercaseRegion = strtoupper($region);
            $driver = 's3';

            $diskKey = "$driver-$region";

            return [
                $diskKey => [
                    'driver' => $driver,
                    'region' => env("S3_REGION_{$uppercaseRegion}", ''),
                    'bucket' => env("S3_BUCKET_{$uppercaseRegion}", ''),
                    'bucket_url' => 'https://'.env("S3_BUCKET_{$uppercaseRegion}", '').'.s3-'.env("S3_REGION_{$uppercaseRegion}", '').'.amazonaws.com/',
                    'temporary' => 'temp',
                    'stream_reads' => true,
                    'throw' => true,
                ],

                "{$diskKey}_deleted" => [
                    'driver' => 's3',
                    'region' => env("S3_REGION_{$uppercaseRegion}", ''),
                    'bucket' => env("S3_BUCKET_{$uppercaseRegion}_DELETED", ''),
                    'bucket_url' => 'https://'.env("S3_BUCKET_{$uppercaseRegion}_DELETED", '').'.s3-'.env("S3_REGION_{$uppercaseRegion}", '').'.amazonaws.com/',
                    'throw' => true,
                ],

                "{$diskKey}_uat" => [
                    'driver' => 's3',
                    'region' => env("S3_REGION_{$uppercaseRegion}", ''),
                    'bucket' => env("S3_BUCKET_{$uppercaseRegion}_UAT", ''),
                    'bucket_url' => 'https://'.env("S3_BUCKET_{$uppercaseRegion}_UAT", '').'.s3-'.env("S3_REGION_{$uppercaseRegion}", '').'.amazonaws.com/',
                    'throw' => true,
                ],
            ];
        })->collapse()->merge([
            's3-app' => [
                'driver' => 's3',
                'region' => env('S3_REGION', ''),
                'bucket' => env('S3_BUCKET', ''),
                'bucket_url' => 'https://'.env('S3_BUCKET').'.s3-'.env('S3_REGION').'.amazonaws.com/',
                'stream_reads' => true,
                'throw' => true,
            ],
        ])->all()),

    'max_chunk_size' => '100mb',

    /*
    |--------------------------------------------------------------------------
    | Symbolic Links
    |--------------------------------------------------------------------------
    |
    | Here you may configure the symbolic links that will be created when the
    | `storage:link` Artisan command is executed. The array keys should be
    | the locations of the links and the values should be their targets.
    |
    */

    'links' => [
        public_path('storage') => storage_path('app/public'),
    ],
];
